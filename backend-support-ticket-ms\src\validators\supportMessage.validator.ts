import { celebrate, Joi, Segments } from "celebrate";

// Following recipe-ms pattern with function wrapper
const addMessageValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        message_content: Joi.string().min(1).max(2000).required(),
        message_type: Joi.string()
          .valid("user_message", "agent_message", "internal_note", "system_message")
          .default("user_message"),
        is_internal: Joi.boolean().default(false),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      ticketId: Joi.number().integer().positive().required(),
    }),
  });

const getTicketMessagesValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      ticketId: Joi.number().integer().positive().required(),
    }),
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        message_type: Joi.string()
          .valid("user_message", "agent_message", "internal_note", "system_message")
          .optional(),
        is_internal: Joi.boolean().optional(),
      })
      .unknown(true),
  });

const addInternalNoteValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        message_content: Joi.string().min(1).max(2000).required(),
        is_internal: Joi.boolean().default(true),
        message_type: Joi.string().default("internal_note"),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      ticketId: Joi.number().integer().positive().required(),
    }),
  });

const updateMessageValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        message_content: Joi.string().min(1).max(2000).optional(),
        is_internal: Joi.boolean().optional(),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      ticketId: Joi.number().integer().positive().required(),
      messageId: Joi.number().integer().positive().required(),
    }),
  });

const deleteMessageValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      ticketId: Joi.number().integer().positive().required(),
      messageId: Joi.number().integer().positive().required(),
    }),
  });

const searchMessagesValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        q: Joi.string().min(1).max(100).required(),
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        ticket_id: Joi.number().integer().positive().optional(),
        message_type: Joi.string()
          .valid("user_message", "agent_message", "internal_note", "system_message")
          .optional(),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
      })
      .unknown(true),
  });

export {
  addMessageValidation,
  getTicketMessagesValidation,
  addInternalNoteValidation,
  updateMessageValidation,
  deleteMessageValidation,
  searchMessagesValidation,
};

export default {
  addMessageValidation,
  getTicketMessagesValidation,
  addInternalNoteValidation,
  updateMessageValidation,
  deleteMessageValidation,
  searchMessagesValidation,
};
