import { getOrganizationById, validateSupportPin } from "../keycloak/common";

/**
 * Organization Helper - Fetch organization data from Keycloak
 * Following TTH architecture patterns (same as auth microservice)
 */

export interface OrganizationData {
  id: string;
  name: string;
  alias: string;
  enabled: boolean;
  support_pin: string;
  organization_logo?: string;
  attributes?: any;
  // Add other fields as needed
}

export interface OrganizationResponse {
  status: boolean;
  message: string;
  data: OrganizationData;
}

class OrganizationHelper {
  constructor() {
    // No need for config here, using keycloak/common functions
  }

  /**
   * Get organization by ID using Keycloak common functions
   * Same pattern as auth microservice
   */
  async getOrganizationById(
    organizationId: string,
    authToken?: string
  ): Promise<OrganizationData | null> {
    try {
      console.log(`🔍 Fetching organization data for ID: ${organizationId}`);

      const result = await getOrganizationById(organizationId, authToken);

      if (!result.status) {
        console.error(`❌ Failed to fetch organization: ${result.message}`);
        return null;
      }

      const orgData = result.data;
      console.log(
        `✅ Organization data fetched successfully for ID: ${organizationId}`
      );

      // Transform to our interface format
      return {
        id: orgData.id,
        name: orgData.name,
        alias: orgData.alias || orgData.name,
        enabled: orgData.enabled !== false,
        support_pin: orgData.attributes?.support_pin || "",
        organization_logo: orgData.attributes?.organization_logo || "",
        attributes: orgData.attributes,
      };
    } catch (error: any) {
      console.error(
        `❌ Error fetching organization ${organizationId}:`,
        error.message
      );
      return null;
    }
  }

  /**
   * Validate support PIN for organization using Keycloak common functions
   * Same pattern as auth microservice
   */
  async validateSupportPin(
    organizationId: string,
    providedPin: string,
    authToken?: string
  ): Promise<{
    isValid: boolean;
    organization?: OrganizationData;
    error?: string;
  }> {
    try {
      console.log(
        `🔐 Validating support PIN for organization: ${organizationId}`
      );

      const result = await validateSupportPin(
        organizationId,
        providedPin,
        authToken
      );

      if (!result.isValid) {
        console.log(`❌ PIN validation failed: ${result.error}`);
        return {
          isValid: false,
          error: result.error,
        };
      }

      console.log(
        `✅ PIN validation successful for organization: ${organizationId}`
      );

      // Transform organization data to our interface format
      const orgData = result.organization;
      const transformedOrg: OrganizationData = {
        id: orgData.id,
        name: orgData.name,
        alias: orgData.alias || orgData.name,
        enabled: orgData.enabled !== false,
        support_pin: orgData.attributes?.support_pin || "",
        organization_logo: orgData.attributes?.organization_logo || "",
        attributes: orgData.attributes,
      };

      return {
        isValid: true,
        organization: transformedOrg,
      };
    } catch (error: any) {
      console.error("Error validating support PIN:", error);
      return {
        isValid: false,
        error: "Failed to validate support PIN",
      };
    }
  }

  /**
   * Check if organization exists and is enabled
   */
  async isOrganizationActive(
    organizationId: string,
    authToken?: string
  ): Promise<boolean> {
    try {
      const organization = await this.getOrganizationById(
        organizationId,
        authToken
      );
      return organization ? organization.enabled : false;
    } catch (error) {
      console.error("Error checking organization status:", error);
      return false;
    }
  }

  /**
   * Get organization name for display purposes
   */
  async getOrganizationName(
    organizationId: string,
    authToken?: string
  ): Promise<string | null> {
    try {
      const organization = await this.getOrganizationById(
        organizationId,
        authToken
      );
      return organization ? organization.name : null;
    } catch (error) {
      console.error("Error getting organization name:", error);
      return null;
    }
  }
}

// Export singleton instance
export const organizationHelper = new OrganizationHelper();

export default organizationHelper;
