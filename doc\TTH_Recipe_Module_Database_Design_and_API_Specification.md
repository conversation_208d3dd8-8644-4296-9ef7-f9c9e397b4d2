# 🍳 TTH Recipe Management Module - Database Design & API Specification

## 📋 **Document Overview**

**Module:** Recipe Management & Operations
**Version:** 1.0
**Date:** December 2024
**Project:** TeamTrainHub (TTH)
**Based on:** Kafoodle-like comprehensive recipe management system

## 🎯 **Core Philosophy & Requirements**

### **Key Features Required:**
- ✅ Comprehensive ingredient management with nutritional data
- ✅ Recipe creation with costing and allergen tracking
- ✅ Version control and recipe scaling
- ✅ Public recipe publishing capabilities
- ✅ Role-based access control
- ✅ Nutritional analysis and reporting
- ✅ Multi-tenant organization support

### **Smart Design Decisions:**
- 🧠 **Separate Nutrition Model:** Linked to ingredients for reusability
- 🧠 **Separate Allergen Model:** System-wide + organization-specific
- 🧠 **Separate Supplier Model:** Multiple suppliers per ingredient
- 🧠 **Version Control:** Track recipe changes with history
- 🧠 **Unit Conversion System:** Flexible measurement handling

---

## 🗄️ **DATABASE DESIGN**

### **1. 🏢 Core System Models**

#### **Organizations & Branches (Existing TTH Models)**
```sql
-- Uses existing TTH models:
-- nv_organizations, nv_branches, nv_departments, nv_users, nv_roles
```

### **2. 🥗 Allergen Management Models**

#### **Allergens Table (`recipe_allergens`)**
```sql
CREATE TABLE recipe_allergens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  allergen_name VARCHAR(100) NOT NULL,
  allergen_code VARCHAR(20) UNIQUE NOT NULL,
  alternative_names JSON, -- ["Gluten", "Wheat Protein"]
  allergen_type ENUM('SYSTEM', 'ORGANIZATION') DEFAULT 'SYSTEM',
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,

  -- Multi-tenant (NULL for system allergens)
  organization_id VARCHAR(50) NULL,

  -- Audit fields (TTH pattern)
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_allergen_per_org (allergen_name, organization_id)
);
```

**Field Explanations:**
- `allergen_type`: SYSTEM (14 default allergens, non-deletable) vs ORGANIZATION (custom)
- `alternative_names`: JSON array for regional variations
- `organization_id`: NULL for system allergens, specific for custom ones

### **3. 🏷️ Universal Category Management Model**

#### **Universal Categories Table (`recipe_categories`)**
```sql
CREATE TABLE recipe_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category_name VARCHAR(100) NOT NULL,
  category_code VARCHAR(50) NOT NULL,
  category_description TEXT,
  category_color VARCHAR(7), -- #hex color
  category_icon VARCHAR(50), -- icon name

  -- Category Type & Hierarchy
  category_type ENUM('INGREDIENT', 'RECIPE') NOT NULL,
  parent_category_id INT NULL,
  category_level INT DEFAULT 1, -- 1=main, 2=sub, 3=sub-sub
  category_order INT DEFAULT 0,

  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  is_system_default BOOLEAN DEFAULT FALSE, -- For pre-defined categories

  -- Multi-tenant
  organization_id VARCHAR(50) NOT NULL,

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (parent_category_id) REFERENCES recipe_categories(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_category_per_org_type (category_name, organization_id, category_type),
  UNIQUE KEY unique_code_per_org_type (category_code, organization_id, category_type),
  INDEX idx_category_type (category_type),
  INDEX idx_parent_category (parent_category_id),
  INDEX idx_category_level (category_level)
);
```

**Field Explanations:**
- `category_type`: Distinguishes between INGREDIENT and RECIPE categories
- `category_level`: Supports hierarchical categories (Main > Sub > Sub-Sub)
- `is_system_default`: For pre-populated categories that can't be deleted
- `parent_category_id`: Enables nested category structures

#### **Suppliers Table (`recipe_suppliers`)**
```sql
CREATE TABLE recipe_suppliers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  supplier_name VARCHAR(200) NOT NULL,
  supplier_code VARCHAR(50),
  contact_person VARCHAR(100),
  email VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  website VARCHAR(255),
  payment_terms VARCHAR(100),
  delivery_days INT DEFAULT 7,
  minimum_order_amount DECIMAL(10,2),
  is_active BOOLEAN DEFAULT TRUE,

  -- Multi-tenant
  organization_id VARCHAR(50) NOT NULL,

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_supplier_per_org (supplier_name, organization_id)
);
```

#### **Nutrition Data Table (`recipe_nutrition_data`)**
```sql
CREATE TABLE recipe_nutrition_data (
  id INT PRIMARY KEY AUTO_INCREMENT,

  -- Per 100g/ml nutritional values
  calories_per_100g DECIMAL(8,2),
  protein_per_100g DECIMAL(8,2),
  carbohydrates_per_100g DECIMAL(8,2),
  fat_per_100g DECIMAL(8,2),
  saturated_fat_per_100g DECIMAL(8,2),
  fiber_per_100g DECIMAL(8,2),
  sugar_per_100g DECIMAL(8,2),
  sodium_per_100g DECIMAL(8,2),
  cholesterol_per_100g DECIMAL(8,2),

  -- Vitamins & Minerals (optional)
  vitamin_c_per_100g DECIMAL(8,2),
  calcium_per_100g DECIMAL(8,2),
  iron_per_100g DECIMAL(8,2),
  potassium_per_100g DECIMAL(8,2),

  -- Additional nutritional info
  additional_nutrients JSON, -- For custom nutrients

  -- Data source tracking
  data_source ENUM('MANUAL', 'USDA', 'SUPPLIER', 'CALCULATED') DEFAULT 'MANUAL',
  data_verified BOOLEAN DEFAULT FALSE,
  verified_by INT NULL,
  verified_at TIMESTAMP NULL,

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),
  FOREIGN KEY (verified_by) REFERENCES nv_users(id)
);
```

#### **Ingredients Table (`recipe_ingredients`)**
```sql
CREATE TABLE recipe_ingredients (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ingredient_code VARCHAR(50) NOT NULL, -- Auto-generated: ORG-CAT-SEQ
  ingredient_name VARCHAR(200) NOT NULL,
  ingredient_description TEXT,

  -- Category & Classification
  category_id INT,
  ingredient_type ENUM('DAIRY', 'MEAT', 'POULTRY', 'SEAFOOD', 'VEGETABLES', 'FRUITS', 'GRAINS', 'NUTS', 'HERBS_SPICES', 'OILS', 'CONDIMENTS', 'BAKING', 'DRY_GOODS', 'BEVERAGES', 'OTHER') NOT NULL,

  -- Units & Measurements
  primary_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'oz', 'lb', 'tbsp', 'tsp', 'cup', 'pint', 'quart', 'gallon') NOT NULL,

  -- Nutritional Information (linked)
  nutrition_data_id INT,

  -- Dietary & Allergen Info
  dietary_flags JSON, -- ["vegan", "vegetarian", "gluten_free", "halal", "kosher"]

  -- Storage & Handling
  storage_instructions TEXT,
  shelf_life_days INT,
  waste_percentage DECIMAL(5,2) DEFAULT 0.00, -- For costing calculations

  -- Cost Information (default supplier)
  default_supplier_id INT,
  current_cost_per_unit DECIMAL(10,4),
  cost_currency VARCHAR(3) DEFAULT 'USD',
  last_cost_update TIMESTAMP,

  -- Status & Availability
  ingredient_status ENUM('ACTIVE', 'INACTIVE', 'DISCONTINUED', 'OUT_OF_STOCK') DEFAULT 'ACTIVE',
  is_featured BOOLEAN DEFAULT FALSE,

  -- Media
  ingredient_image_url VARCHAR(500),
  ingredient_thumbnail_url VARCHAR(500),

  -- Multi-tenant
  organization_id VARCHAR(50) NOT NULL,

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (category_id) REFERENCES recipe_ingredient_categories(id),
  FOREIGN KEY (nutrition_data_id) REFERENCES recipe_nutrition_data(id),
  FOREIGN KEY (default_supplier_id) REFERENCES recipe_suppliers(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_ingredient_per_org (ingredient_name, organization_id),
  UNIQUE KEY unique_code_per_org (ingredient_code, organization_id),
  INDEX idx_ingredient_type (ingredient_type),
  INDEX idx_ingredient_status (ingredient_status)
);
```

#### **Ingredient Allergens Junction Table (`recipe_ingredient_allergens`)**
```sql
CREATE TABLE recipe_ingredient_allergens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ingredient_id INT NOT NULL,
  allergen_id INT NOT NULL,
  allergen_level ENUM('CONTAINS', 'MAY_CONTAIN', 'TRACES') DEFAULT 'CONTAINS',
  notes TEXT,

  -- Audit fields
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (ingredient_id) REFERENCES recipe_ingredients(id) ON DELETE CASCADE,
  FOREIGN KEY (allergen_id) REFERENCES recipe_allergens(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_ingredient_allergen (ingredient_id, allergen_id)
);
```

#### **Ingredient Suppliers Junction Table (`recipe_ingredient_suppliers`)**
```sql
CREATE TABLE recipe_ingredient_suppliers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ingredient_id INT NOT NULL,
  supplier_id INT NOT NULL,

  -- Supplier-specific details
  supplier_product_code VARCHAR(100),
  supplier_product_name VARCHAR(200),
  pack_size DECIMAL(10,3),
  pack_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'oz', 'lb') NOT NULL,
  cost_per_pack DECIMAL(10,4),
  cost_currency VARCHAR(3) DEFAULT 'USD',

  -- Supplier terms
  minimum_order_quantity INT DEFAULT 1,
  lead_time_days INT DEFAULT 7,
  is_preferred BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,

  -- Price history
  price_valid_from DATE,
  price_valid_until DATE,
  last_ordered_date DATE,
  last_received_date DATE,

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (ingredient_id) REFERENCES recipe_ingredients(id) ON DELETE CASCADE,
  FOREIGN KEY (supplier_id) REFERENCES recipe_suppliers(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_ingredient_supplier (ingredient_id, supplier_id)
);
```

### **4. 📝 Recipe Management Models**

#### **Recipes Table (`recipes`)**
```sql
CREATE TABLE recipes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_code VARCHAR(50) NOT NULL, -- Auto-generated: ORG-RCP-SEQ
  recipe_name VARCHAR(200) NOT NULL,
  recipe_description TEXT,
  public_name VARCHAR(200), -- For public publishing
  public_description TEXT, -- For public publishing

  -- Classification
  category_id INT,
  cuisine_type VARCHAR(100), -- Italian, Mexican, etc.
  recipe_type ENUM('MAIN_COURSE', 'APPETIZER', 'DESSERT', 'BEVERAGE', 'SAUCE', 'SIDE_DISH', 'SOUP', 'SALAD', 'BREAKFAST', 'SNACK', 'OTHER') NOT NULL,
  difficulty_level ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM',

  -- Yield & Portioning
  total_yield_quantity DECIMAL(10,3),
  total_yield_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'portions', 'servings') NOT NULL,
  number_of_portions INT NOT NULL,
  standard_portion_size DECIMAL(10,3),
  portion_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'slice', 'piece') NOT NULL,

  -- Timing
  prep_time_minutes INT DEFAULT 0,
  cook_time_minutes INT DEFAULT 0,
  total_time_minutes INT GENERATED ALWAYS AS (prep_time_minutes + cook_time_minutes) STORED,

  -- Instructions & Method
  method_instructions LONGTEXT NOT NULL,
  special_equipment TEXT,
  storage_instructions TEXT,
  reheating_instructions TEXT,

  -- Role-based instructions
  chef_instructions TEXT,
  serving_instructions TEXT,
  bartender_instructions TEXT,

  -- Costing & Pricing
  calculated_total_cost DECIMAL(10,4) DEFAULT 0.00,
  calculated_cost_per_portion DECIMAL(10,4) DEFAULT 0.00,
  suggested_selling_price DECIMAL(10,2),
  target_food_cost_percentage DECIMAL(5,2) DEFAULT 30.00,

  -- Nutritional Summary (calculated)
  total_calories DECIMAL(10,2),
  total_protein DECIMAL(10,2),
  total_carbs DECIMAL(10,2),
  total_fat DECIMAL(10,2),

  -- Status & Publishing
  recipe_status ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED') DEFAULT 'DRAFT',
  is_published_public BOOLEAN DEFAULT FALSE,
  public_url_slug VARCHAR(200),
  public_published_at TIMESTAMP NULL,

  -- Version Control
  version_number DECIMAL(3,1) DEFAULT 1.0,
  is_current_version BOOLEAN DEFAULT TRUE,
  parent_recipe_id INT NULL, -- For version tracking

  -- Access Control
  recipe_owner_id INT NOT NULL,
  is_shared BOOLEAN DEFAULT FALSE,
  sharing_permissions JSON, -- {"view": ["role1"], "edit": ["role2"]}

  -- Media
  recipe_image_url VARCHAR(500),
  recipe_thumbnail_url VARCHAR(500),
  recipe_video_url VARCHAR(500),

  -- Analytics (for public recipes)
  public_view_count INT DEFAULT 0,
  public_like_count INT DEFAULT 0,
  public_download_count INT DEFAULT 0,

  -- Multi-tenant
  organization_id VARCHAR(50) NOT NULL,
  branch_id VARCHAR(50),

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (branch_id) REFERENCES nv_branches(branch_id),
  FOREIGN KEY (category_id) REFERENCES recipe_categories(id),
  FOREIGN KEY (recipe_owner_id) REFERENCES nv_users(id),
  FOREIGN KEY (parent_recipe_id) REFERENCES recipes(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_recipe_per_org (recipe_name, organization_id, version_number),
  UNIQUE KEY unique_code_per_org (recipe_code, organization_id),
  UNIQUE KEY unique_public_slug (public_url_slug),
  INDEX idx_recipe_status (recipe_status),
  INDEX idx_recipe_type (recipe_type),
  INDEX idx_is_published (is_published_public),
  INDEX idx_current_version (is_current_version)
);
```

#### **Recipe Ingredients Junction Table (`recipe_ingredients_list`)**
```sql
CREATE TABLE recipe_ingredients_list (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,
  ingredient_id INT NOT NULL,

  -- Quantity & Units
  quantity DECIMAL(10,4) NOT NULL,
  unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'oz', 'lb', 'tbsp', 'tsp', 'cup', 'pint', 'quart', 'gallon') NOT NULL,

  -- Cost tracking (snapshot at time of adding)
  cost_per_unit_at_time DECIMAL(10,4),
  total_ingredient_cost DECIMAL(10,4),

  -- Recipe-specific notes
  preparation_notes TEXT, -- "finely chopped", "room temperature"
  ingredient_order INT DEFAULT 0, -- Order in recipe
  is_optional BOOLEAN DEFAULT FALSE,

  -- Sub-recipe handling
  is_sub_recipe BOOLEAN DEFAULT FALSE,
  sub_recipe_id INT NULL, -- If this "ingredient" is actually another recipe

  -- Audit fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (ingredient_id) REFERENCES recipe_ingredients(id),
  FOREIGN KEY (sub_recipe_id) REFERENCES recipes(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_recipe_ingredient (recipe_id, ingredient_id),
  INDEX idx_ingredient_order (ingredient_order)
);
```

#### **Recipe Version History Table (`recipe_version_history`)**
```sql
CREATE TABLE recipe_version_history (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,
  version_number DECIMAL(3,1) NOT NULL,

  -- Change tracking
  change_summary TEXT,
  change_details JSON, -- Detailed diff of what changed
  change_type ENUM('MINOR', 'MAJOR', 'CRITICAL') DEFAULT 'MINOR',

  -- Snapshot of recipe data at this version
  recipe_data_snapshot JSON, -- Full recipe data at this version

  -- Version metadata
  is_published BOOLEAN DEFAULT FALSE,
  published_at TIMESTAMP NULL,
  is_current BOOLEAN DEFAULT FALSE,

  -- Audit fields
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_recipe_version (recipe_id, version_number),
  INDEX idx_version_number (version_number),
  INDEX idx_is_current (is_current)
);
```

### **5. 🎯 Unit Conversion & System Models**

#### **Unit Conversions Table (`recipe_unit_conversions`)**
```sql
CREATE TABLE recipe_unit_conversions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  from_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'oz', 'lb', 'tbsp', 'tsp', 'cup', 'pint', 'quart', 'gallon') NOT NULL,
  to_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'oz', 'lb', 'tbsp', 'tsp', 'cup', 'pint', 'quart', 'gallon') NOT NULL,
  conversion_factor DECIMAL(15,8) NOT NULL, -- Multiply by this to convert
  conversion_type ENUM('WEIGHT', 'VOLUME', 'COUNT') NOT NULL,

  -- Optional ingredient-specific conversions
  ingredient_id INT NULL, -- NULL for universal conversions
  density_factor DECIMAL(10,6), -- For weight-volume conversions

  -- System fields
  is_active BOOLEAN DEFAULT TRUE,
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (ingredient_id) REFERENCES recipe_ingredients(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),

  UNIQUE KEY unique_conversion (from_unit, to_unit, ingredient_id),
  INDEX idx_conversion_type (conversion_type)
);
```

### **6. 📊 Analytics & Reporting Models**

#### **Recipe Analytics Table (`recipe_analytics`)**
```sql
CREATE TABLE recipe_analytics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,

  -- Usage metrics
  times_viewed INT DEFAULT 0,
  times_cooked INT DEFAULT 0,
  times_scaled INT DEFAULT 0,
  times_duplicated INT DEFAULT 0,

  -- Public metrics (if published)
  public_views INT DEFAULT 0,
  public_likes INT DEFAULT 0,
  public_shares INT DEFAULT 0,
  public_downloads INT DEFAULT 0,

  -- Cost analysis
  average_cost_per_portion DECIMAL(10,4),
  cost_trend ENUM('INCREASING', 'DECREASING', 'STABLE') DEFAULT 'STABLE',
  last_cost_calculation TIMESTAMP,

  -- Performance metrics
  avg_rating DECIMAL(3,2) DEFAULT 0.00,
  total_ratings INT DEFAULT 0,

  -- Date tracking
  last_viewed_at TIMESTAMP,
  last_cooked_at TIMESTAMP,
  last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,

  UNIQUE KEY unique_recipe_analytics (recipe_id),
  INDEX idx_public_views (public_views),
  INDEX idx_times_cooked (times_cooked)
);
```

### **7. 🔐 Access Control & Permissions Models**

#### **Recipe Permissions Table (`recipe_permissions`)**
```sql
CREATE TABLE recipe_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,

  -- User/Role based permissions
  user_id INT NULL,
  role_id INT NULL,
  department_id VARCHAR(50) NULL,
  branch_id VARCHAR(50) NULL,

  -- Permission levels
  can_view BOOLEAN DEFAULT FALSE,
  can_edit BOOLEAN DEFAULT FALSE,
  can_delete BOOLEAN DEFAULT FALSE,
  can_publish BOOLEAN DEFAULT FALSE,
  can_share BOOLEAN DEFAULT FALSE,
  can_export BOOLEAN DEFAULT FALSE,

  -- Permission metadata
  granted_by INT,
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  is_active BOOLEAN DEFAULT TRUE,

  FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES nv_users(id),
  FOREIGN KEY (role_id) REFERENCES nv_roles(id),
  FOREIGN KEY (department_id) REFERENCES nv_departments(department_id),
  FOREIGN KEY (branch_id) REFERENCES nv_branches(branch_id),
  FOREIGN KEY (granted_by) REFERENCES nv_users(id),

  INDEX idx_user_permissions (user_id),
  INDEX idx_role_permissions (role_id),
  INDEX idx_active_permissions (is_active)
);
```

---

## 🚀 **API SPECIFICATION**

### **Base URL Structure**
```
https://api.teamtrainhub.com/api/v1/recipe/
```

### **Authentication**
- **Type:** Bearer Token (JWT)
- **Header:** `Authorization: Bearer <token>`
- **Organization Context:** `X-Organization-ID: <org_id>`
- **Branch Context:** `X-Branch-ID: <branch_id>` (optional)

### **Standard Response Format**
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "meta": {
    "timestamp": "2024-12-20T10:30:00Z",
    "version": "1.0",
    "pagination": { ... }
  },
  "errors": []
}
```

---

## 🥗 **1. ALLERGEN MANAGEMENT APIs**

### **GET /allergens**
**Purpose:** Get all allergens (system + organization-specific)

**Query Parameters:**
- `type` (optional): `SYSTEM` | `ORGANIZATION` | `ALL` (default: ALL)
- `active_only` (optional): boolean (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "allergens": [
      {
        "id": 1,
        "allergen_name": "Gluten",
        "allergen_code": "GLU",
        "alternative_names": ["Wheat Protein"],
        "allergen_type": "SYSTEM",
        "description": "Contains gluten proteins",
        "is_active": true,
        "organization_id": null
      }
    ]
  }
}
```

### **POST /allergens**
**Purpose:** Create custom organization allergen

**Request Body:**
```json
{
  "allergen_name": "Custom Allergen",
  "allergen_code": "CUST",
  "alternative_names": ["Alt Name"],
  "description": "Custom allergen description"
}
```

### **PUT /allergens/{id}**
**Purpose:** Update organization allergen (system allergens cannot be updated)

### **DELETE /allergens/{id}**
**Purpose:** Soft delete organization allergen (system allergens cannot be deleted)

---

## 🏷️ **2. UNIVERSAL CATEGORY MANAGEMENT APIs**

### **GET /categories**
**Purpose:** Get all categories (ingredients and recipes) for organization

**Query Parameters:**
- `category_type` (optional): `INGREDIENT` | `RECIPE` | `ALL` (default: ALL)
- `parent_id` (optional): Filter by parent category
- `level` (optional): Filter by category level (1, 2, 3)
- `active_only` (optional): boolean (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "category_name": "Vegetables",
        "category_code": "VEG",
        "category_description": "Fresh vegetables and produce",
        "category_color": "#4CAF50",
        "category_icon": "leaf",
        "category_type": "INGREDIENT",
        "parent_category_id": null,
        "category_level": 1,
        "category_order": 1,
        "is_active": true,
        "is_system_default": true,
        "children": [
          {
            "id": 2,
            "category_name": "Root Vegetables",
            "category_type": "INGREDIENT",
            "category_level": 2,
            "parent_category_id": 1
          }
        ]
      }
    ]
  }
}
```

### **POST /categories**
**Purpose:** Create new category

**Request Body:**
```json
{
  "category_name": "Organic Vegetables",
  "category_code": "ORG-VEG",
  "category_description": "Certified organic vegetables",
  "category_color": "#8BC34A",
  "category_icon": "eco",
  "category_type": "INGREDIENT",
  "parent_category_id": 1,
  "category_order": 5
}
```

### **PUT /categories/{id}**
**Purpose:** Update category (system defaults cannot be updated)

### **DELETE /categories/{id}**
**Purpose:** Soft delete category (system defaults cannot be deleted)

### **GET /categories/tree**
**Purpose:** Get hierarchical category tree

**Query Parameters:**
- `category_type` (required): `INGREDIENT` | `RECIPE`

---

## 🥕 **3. INGREDIENT MANAGEMENT APIs**

### **GET /ingredients**
**Purpose:** Get all ingredients for organization

**Query Parameters:**
- `category_id` (optional): Filter by category
- `ingredient_type` (optional): Filter by type
- `status` (optional): Filter by status
- `search` (optional): Search by name
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `include_nutrition` (optional): Include nutrition data (default: false)
- `include_suppliers` (optional): Include supplier data (default: false)

**Response:**
```json
{
  "success": true,
  "data": {
    "ingredients": [
      {
        "id": 1,
        "ingredient_code": "ORG-VEG-001",
        "ingredient_name": "Fresh Tomatoes",
        "ingredient_description": "Organic fresh tomatoes",
        "category": {
          "id": 1,
          "category_name": "Vegetables",
          "category_color": "#4CAF50"
        },
        "ingredient_type": "VEGETABLES",
        "primary_unit": "kg",
        "current_cost_per_unit": 3.50,
        "cost_currency": "USD",
        "waste_percentage": 10.00,
        "ingredient_status": "ACTIVE",
        "dietary_flags": ["vegan", "vegetarian"],
        "allergens": [
          {
            "id": 1,
            "allergen_name": "None",
            "allergen_level": "CONTAINS"
          }
        ],
        "nutrition_data": {
          "calories_per_100g": 18,
          "protein_per_100g": 0.9,
          "carbohydrates_per_100g": 3.9,
          "fat_per_100g": 0.2
        },
        "suppliers": [
          {
            "id": 1,
            "supplier_name": "Fresh Farm Co",
            "cost_per_pack": 35.00,
            "pack_size": 10,
            "pack_unit": "kg",
            "is_preferred": true
          }
        ],
        "created_at": "2024-12-20T10:30:00Z",
        "updated_at": "2024-12-20T10:30:00Z"
      }
    ]
  },
  "meta": {
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 100,
      "items_per_page": 20
    }
  }
}
```

### **POST /ingredients**
**Purpose:** Create new ingredient

**Request Body:**
```json
{
  "ingredient_name": "Fresh Tomatoes",
  "ingredient_description": "Organic fresh tomatoes",
  "category_id": 1,
  "ingredient_type": "VEGETABLES",
  "primary_unit": "kg",
  "current_cost_per_unit": 3.50,
  "waste_percentage": 10.00,
  "dietary_flags": ["vegan", "vegetarian"],
  "storage_instructions": "Store in cool, dry place",
  "shelf_life_days": 7,
  "allergen_ids": [1, 2],
  "nutrition_data": {
    "calories_per_100g": 18,
    "protein_per_100g": 0.9,
    "carbohydrates_per_100g": 3.9,
    "fat_per_100g": 0.2
  },
  "suppliers": [
    {
      "supplier_id": 1,
      "supplier_product_code": "TOM-001",
      "pack_size": 10,
      "pack_unit": "kg",
      "cost_per_pack": 35.00,
      "is_preferred": true
    }
  ]
}
```

### **GET /ingredients/{id}**
**Purpose:** Get single ingredient with full details

### **PUT /ingredients/{id}**
**Purpose:** Update ingredient

### **DELETE /ingredients/{id}**
**Purpose:** Soft delete ingredient (with validation for recipe usage)

### **POST /ingredients/bulk-import**
**Purpose:** Bulk import ingredients from CSV/Excel

**Request:** Multipart form with file upload

---

## 📝 **4. RECIPE MANAGEMENT APIs**

### **GET /recipes**
**Purpose:** Get all recipes for organization

**Query Parameters:**
- `category_id` (optional): Filter by category
- `recipe_type` (optional): Filter by type
- `status` (optional): Filter by status
- `cuisine_type` (optional): Filter by cuisine
- `difficulty_level` (optional): Filter by difficulty
- `is_published_public` (optional): Filter public recipes
- `owner_id` (optional): Filter by owner
- `search` (optional): Search by name/description
- `page` (optional): Page number
- `limit` (optional): Items per page
- `include_ingredients` (optional): Include ingredient list
- `include_nutrition` (optional): Include nutrition summary
- `include_costing` (optional): Include cost analysis

**Response:**
```json
{
  "success": true,
  "data": {
    "recipes": [
      {
        "id": 1,
        "recipe_code": "ORG-RCP-001",
        "recipe_name": "Classic Margherita Pizza",
        "recipe_description": "Traditional Italian pizza",
        "category": {
          "id": 1,
          "category_name": "Main Course"
        },
        "recipe_type": "MAIN_COURSE",
        "cuisine_type": "Italian",
        "difficulty_level": "MEDIUM",
        "total_yield_quantity": 4,
        "total_yield_unit": "portions",
        "number_of_portions": 4,
        "standard_portion_size": 1,
        "portion_unit": "piece",
        "prep_time_minutes": 30,
        "cook_time_minutes": 15,
        "total_time_minutes": 45,
        "calculated_total_cost": 12.50,
        "calculated_cost_per_portion": 3.13,
        "suggested_selling_price": 15.00,
        "recipe_status": "ACTIVE",
        "is_published_public": false,
        "version_number": 1.0,
        "is_current_version": true,
        "recipe_owner": {
          "id": 1,
          "name": "Chef Mario",
          "email": "<EMAIL>"
        },
        "allergens_summary": ["Gluten", "Dairy"],
        "nutrition_summary": {
          "total_calories": 1200,
          "calories_per_portion": 300,
          "total_protein": 48,
          "protein_per_portion": 12
        },
        "ingredients_count": 8,
        "public_view_count": 0,
        "created_at": "2024-12-20T10:30:00Z",
        "updated_at": "2024-12-20T10:30:00Z"
      }
    ]
  }
}
```

### **POST /recipes**
**Purpose:** Create new recipe

**Request Body:**
```json
{
  "recipe_name": "Classic Margherita Pizza",
  "recipe_description": "Traditional Italian pizza",
  "category_id": 1,
  "recipe_type": "MAIN_COURSE",
  "cuisine_type": "Italian",
  "difficulty_level": "MEDIUM",
  "total_yield_quantity": 4,
  "total_yield_unit": "portions",
  "number_of_portions": 4,
  "standard_portion_size": 1,
  "portion_unit": "piece",
  "prep_time_minutes": 30,
  "cook_time_minutes": 15,
  "method_instructions": "1. Prepare dough...\n2. Add toppings...",
  "chef_instructions": "Ensure oven is preheated to 450°F",
  "serving_instructions": "Serve immediately while hot",
  "suggested_selling_price": 15.00,
  "target_food_cost_percentage": 30.00,
  "ingredients": [
    {
      "ingredient_id": 1,
      "quantity": 500,
      "unit": "g",
      "preparation_notes": "room temperature",
      "ingredient_order": 1,
      "is_optional": false
    },
    {
      "ingredient_id": 2,
      "quantity": 200,
      "unit": "g",
      "preparation_notes": "grated",
      "ingredient_order": 2,
      "is_optional": false
    }
  ]
}
```

### **GET /recipes/{id}**
**Purpose:** Get single recipe with full details including ingredients, nutrition, and costing

### **PUT /recipes/{id}**
**Purpose:** Update recipe (creates new version if significant changes)

### **DELETE /recipes/{id}**
**Purpose:** Soft delete recipe

### **POST /recipes/{id}/scale**
**Purpose:** Scale recipe up or down

**Request Body:**
```json
{
  "scale_type": "PORTIONS", // or "YIELD"
  "new_value": 8, // new number of portions or yield quantity
  "save_as_new": true, // whether to save as new recipe or version
  "new_recipe_name": "Large Margherita Pizza (8 portions)"
}
```

### **POST /recipes/{id}/duplicate**
**Purpose:** Duplicate recipe as starting point for variations

### **GET /recipes/{id}/versions**
**Purpose:** Get version history of recipe

### **POST /recipes/{id}/publish**
**Purpose:** Publish recipe publicly

**Request Body:**
```json
{
  "public_name": "Amazing Margherita Pizza",
  "public_description": "Our signature pizza recipe",
  "public_url_slug": "amazing-margherita-pizza",
  "include_nutrition": true,
  "include_allergens": true,
  "call_to_action": {
    "type": "CONTACT_FORM",
    "title": "Want to learn more?",
    "message": "Contact us for cooking classes!"
  }
}
```

### **POST /recipes/{id}/unpublish**
**Purpose:** Remove recipe from public access

---

## 📊 **5. ANALYTICS & REPORTING APIs**

### **GET /analytics/dashboard**
**Purpose:** Get recipe module dashboard statistics

**Response:**
```json
{
  "success": true,
  "data": {
    "overview_stats": {
      "total_recipes": 150,
      "total_ingredients": 500,
      "total_allergens": 18,
      "total_suppliers": 25,
      "total_categories": 12,
      "public_recipes": 25
    },
    "public_recipe_insights": {
      "total_published": 25,
      "top_viewed_recipes": [
        {
          "id": 1,
          "recipe_name": "Classic Margherita",
          "public_views": 1250,
          "public_likes": 89
        }
      ],
      "top_liked_recipes": [
        {
          "id": 2,
          "recipe_name": "Chocolate Cake",
          "public_likes": 156,
          "public_views": 890
        }
      ]
    },
    "cost_analysis": {
      "average_cost_per_portion": 4.25,
      "highest_cost_recipe": {
        "id": 5,
        "recipe_name": "Premium Steak",
        "cost_per_portion": 25.50
      },
      "lowest_cost_recipe": {
        "id": 8,
        "recipe_name": "Simple Salad",
        "cost_per_portion": 1.25
      }
    }
  }
}
```

### **GET /analytics/recipes/{id}**
**Purpose:** Get detailed analytics for specific recipe

### **GET /analytics/ingredients/usage**
**Purpose:** Get ingredient usage analytics

### **GET /analytics/costs/trends**
**Purpose:** Get cost trend analysis

---

## 🔍 **6. SEARCH & FILTERING APIs**

### **GET /search**
**Purpose:** Global search across recipes and ingredients

**Query Parameters:**
- `q` (required): Search query
- `type` (optional): `RECIPES` | `INGREDIENTS` | `ALL` (default: ALL)
- `filters` (optional): JSON object with filters

**Response:**
```json
{
  "success": true,
  "data": {
    "recipes": [
      {
        "id": 1,
        "recipe_name": "Classic Margherita Pizza",
        "match_score": 0.95,
        "match_fields": ["name", "description"]
      }
    ],
    "ingredients": [
      {
        "id": 1,
        "ingredient_name": "Fresh Tomatoes",
        "match_score": 0.87,
        "match_fields": ["name"]
      }
    ]
  }
}
```

---

## 📄 **7. EXPORT & REPORTING APIs**

### **GET /export/recipes**
**Purpose:** Export recipes to PDF/Excel

**Query Parameters:**
- `format`: `PDF` | `EXCEL` | `CSV`
- `recipe_ids` (optional): Comma-separated recipe IDs
- `include_costing` (optional): boolean
- `include_nutrition` (optional): boolean

### **GET /export/ingredients**
**Purpose:** Export ingredients to Excel/CSV

### **GET /recipes/{id}/print**
**Purpose:** Generate print-friendly recipe card

---

## 🔐 **8. PERMISSIONS & ACCESS CONTROL APIs**

### **GET /recipes/{id}/permissions**
**Purpose:** Get recipe permissions

### **POST /recipes/{id}/permissions**
**Purpose:** Grant recipe permissions

**Request Body:**
```json
{
  "user_id": 123,
  "permissions": {
    "can_view": true,
    "can_edit": false,
    "can_delete": false,
    "can_publish": false
  },
  "expires_at": "2025-12-31T23:59:59Z"
}
```

---

## ⚙️ **IMPLEMENTATION NOTES**

### **Field Explanations:**

1. **Separate Nutrition Model:** Allows reusability across ingredients and easy updates
2. **Allergen System:** System-wide defaults + organization-specific custom allergens
3. **Cost Tracking:** Historical cost snapshots for accurate recipe costing
4. **Version Control:** Full recipe versioning with change tracking
5. **Multi-tenant:** Organization and branch-level data isolation
6. **Unit Conversions:** Flexible system supporting ingredient-specific conversions
7. **Public Publishing:** Secure public recipe sharing with analytics
8. **Role-based Access:** Granular permissions for different user types

### **Key Business Logic:**

1. **Recipe Costing:** `Total Cost = Σ(ingredient_quantity × cost_per_unit × (1 + waste_percentage))`
2. **Nutrition Calculation:** Aggregate from ingredients based on quantities
3. **Allergen Detection:** Union of all ingredient allergens
4. **Recipe Scaling:** Proportional scaling with unit conversion support
5. **Version Control:** Automatic versioning on significant changes

### **Performance Considerations:**

1. **Indexing:** Strategic indexes on search and filter fields
2. **Caching:** Recipe calculations and public recipe data
3. **Pagination:** All list endpoints support pagination
4. **Lazy Loading:** Optional includes for related data
5. **Search:** Full-text search with relevance scoring

This comprehensive design provides a robust, scalable foundation for a professional recipe management system similar to Kafoodle, with all the features mentioned in your requirements documents.
