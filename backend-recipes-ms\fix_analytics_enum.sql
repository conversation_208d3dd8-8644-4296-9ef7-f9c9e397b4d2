-- Fix Analytics ENUM Issue
-- This script will clean up old event_type values and update the ENUM

-- Step 1: Check current event_type values in the database
SELECT DISTINCT event_type, COUNT(*) as count 
FROM mo_recipe_analytics 
GROUP BY event_type;

-- Step 2: Update old event types to new ones (if any exist)
-- Common old values that might exist:
UPDATE mo_recipe_analytics SET event_type = 'recipe_view' WHERE event_type = 'view';
UPDATE mo_recipe_analytics SET event_type = 'recipe_view' WHERE event_type = 'recipe_viewed';
UPDATE mo_recipe_analytics SET event_type = 'cta_click' WHERE event_type = 'click';
UPDATE mo_recipe_analytics SET event_type = 'cta_click' WHERE event_type = 'cta_clicked';
UPDATE mo_recipe_analytics SET event_type = 'contact_form_submit' WHERE event_type = 'contact';
UPDATE mo_recipe_analytics SET event_type = 'contact_form_submit' WHERE event_type = 'contact_submitted';
UPDATE mo_recipe_analytics SET event_type = 'contact_form_submit' WHERE event_type = 'form_submit';

-- Step 3: Delete any rows with invalid event_types that can't be mapped
DELETE FROM mo_recipe_analytics 
WHERE event_type NOT IN ('recipe_view', 'cta_click', 'contact_form_submit');

-- Step 4: Now safely update the ENUM
ALTER TABLE mo_recipe_analytics 
MODIFY COLUMN event_type ENUM('recipe_view', 'cta_click', 'contact_form_submit') NOT NULL;

-- Step 5: Verify the fix
SELECT DISTINCT event_type, COUNT(*) as count 
FROM mo_recipe_analytics 
GROUP BY event_type;
