{"UNAUTHORIZED_ACCESS": "Unauthorized access", "INVALID_TOKEN": "Invalid or expired token", "USER_NOT_FOUND": "User not found", "AUTHENTICATION_FAILED": "Authentication failed", "INSUFFICIENT_PERMISSIONS": "Insufficient permissions", "AUTHORIZATION_FAILED": "Authorization failed", "VALIDATION_ERROR": "Validation error", "REQUIRED_FIELDS_MISSING": "Required fields are missing", "TICKET_CREATED_SUCCESSFULLY": "Support ticket created successfully", "TICKET_CREATION_FAILED": "Failed to create support ticket", "TICKETS_FETCHED_SUCCESSFULLY": "Tickets retrieved successfully", "TICKETS_FETCH_FAILED": "Failed to retrieve tickets", "TICKET_FETCHED_SUCCESSFULLY": "Ticket retrieved successfully", "TICKET_FETCH_FAILED": "Failed to retrieve ticket", "TICKET_NOT_FOUND": "Ticket not found", "TICKET_UPDATED_SUCCESSFULLY": "Ticket updated successfully", "TICKET_UPDATE_FAILED": "Failed to update ticket", "TICKET_ASSIGNED_SUCCESSFULLY": "Ticket assigned successfully", "TICKET_ASSIGNMENT_FAILED": "Failed to assign ticket", "TICKET_ESCALATED_SUCCESSFULLY": "Ticket escalated successfully", "TICKET_ESCALATION_FAILED": "Failed to escalate ticket", "MESSAGE_SENT_SUCCESSFULLY": "Message sent successfully", "MESSAGE_SEND_FAILED": "Failed to send message", "MESSAGES_FETCHED_SUCCESSFULLY": "Messages retrieved successfully", "MESSAGES_FETCH_FAILED": "Failed to retrieve messages", "MESSAGE_NOT_FOUND": "Message not found", "MESSAGE_UPDATED_SUCCESSFULLY": "Message updated successfully", "MESSAGE_UPDATE_FAILED": "Failed to update message", "MESSAGE_DELETED_SUCCESSFULLY": "Message deleted successfully", "MESSAGE_DELETE_FAILED": "Failed to delete message", "MESSAGE_MARKED_AS_SOLUTION": "Message marked as solution", "MESSAGE_RATING_SAVED": "Message rating saved successfully", "AI_RESPONSE_GENERATED": "AI response generated successfully", "AI_SERVICE_UNAVAILABLE": "AI service is currently unavailable", "AI_ANALYSIS_COMPLETED": "AI analysis completed", "AI_ANALYSIS_FAILED": "AI analysis failed", "ESCALATION_COMPLETED": "Escalation completed successfully", "ESCALATION_FAILED": "Escalation failed", "NOTIFICATION_SENT": "Notification sent successfully", "NOTIFICATION_FAILED": "Failed to send notification", "RATE_LIMIT_EXCEEDED": "Rate limit exceeded. Please try again later", "AI_RATE_LIMIT_EXCEEDED": "AI request limit exceeded. Please try again later", "FILE_UPLOAD_RATE_LIMIT_EXCEEDED": "File upload limit exceeded. Please try again later", "STRICT_RATE_LIMIT_EXCEEDED": "Rate limit exceeded for this sensitive operation", "INVALID_FILE_TYPE": "Invalid file type. Please upload a supported file format", "FILE_TOO_LARGE": "File size exceeds the maximum allowed limit", "FILE_UPLOAD_FAILED": "File upload failed", "FILE_UPLOAD_SUCCESSFUL": "File uploaded successfully", "SEARCH_COMPLETED": "Search completed successfully", "SEARCH_FAILED": "Search operation failed", "ANALYTICS_FETCHED": "Analytics data retrieved successfully", "ANALYTICS_FETCH_FAILED": "Failed to retrieve analytics data", "CONFIG_UPDATED": "Configuration updated successfully", "CONFIG_UPDATE_FAILED": "Failed to update configuration", "CONFIG_FETCHED": "Configuration retrieved successfully", "CONFIG_FETCH_FAILED": "Failed to retrieve configuration", "DASHBOARD_DATA_FETCHED": "Dashboard data retrieved successfully", "DASHBOARD_DATA_FETCH_FAILED": "Failed to retrieve dashboard data", "SYSTEM_STATUS_FETCHED": "System status retrieved successfully", "SYSTEM_STATUS_FETCH_FAILED": "Failed to retrieve system status", "KNOWLEDGE_BASE_UPDATED": "Knowledge base updated successfully", "KNOWLEDGE_BASE_UPDATE_FAILED": "Failed to update knowledge base", "SUGGESTIONS_FETCHED": "Suggestions retrieved successfully", "SUGGESTIONS_FETCH_FAILED": "Failed to retrieve suggestions", "AI_TEST_COMPLETED": "AI test completed successfully", "AI_TEST_FAILED": "AI test failed", "AI_RETRAIN_INITIATED": "AI retraining initiated successfully", "AI_RETRAIN_FAILED": "Failed to initiate AI retraining", "SLA_WARNING": "SLA deadline approaching", "SLA_BREACH": "SLA deadline exceeded", "TICKET_RESOLVED": "Ticket has been resolved", "TICKET_CLOSED": "Ticket has been closed", "CUSTOMER_SATISFACTION_RECORDED": "Customer satisfaction rating recorded", "EXPERT_ASSIGNED": "Expert has been assigned to your ticket", "AUTO_ESCALATION": "Ticket has been automatically escalated", "MANUAL_ESCALATION": "Ticket has been manually escalated", "AI_CONFIDENCE_LOW": "AI confidence is low, consider human review", "AI_CONFIDENCE_HIGH": "AI is confident about this response", "SENTIMENT_NEGATIVE": "Negative sentiment detected in customer message", "SENTIMENT_POSITIVE": "Positive sentiment detected in customer message", "EMOTION_DETECTED": "Customer emotion detected", "PRIORITY_ESCALATED": "Ticket priority has been escalated", "ORGANIZATION_ACCESS_DENIED": "Access denied for this organization", "SUPER_ADMIN_REQUIRED": "Super administrator privileges required", "ADMIN_REQUIRED": "Administrator privileges required", "AGENT_REQUIRED": "Agent privileges required", "TICKET_OWNER_ONLY": "Only ticket owner can perform this action", "INTERNAL_SERVER_ERROR": "Internal server error occurred", "SERVICE_UNAVAILABLE": "Service temporarily unavailable", "MAINTENANCE_MODE": "System is under maintenance", "FEATURE_DISABLED": "This feature is currently disabled", "INVALID_REQUEST": "Invalid request format", "MISSING_PARAMETERS": "Required parameters are missing", "INVALID_PARAMETERS": "Invalid parameter values provided", "OPERATION_SUCCESSFUL": "Operation completed successfully", "OPERATION_FAILED": "Operation failed", "DATA_NOT_FOUND": "Requested data not found", "DUPLICATE_ENTRY": "Duplicate entry detected", "CONFLICT_ERROR": "Conflict with existing data", "PRECONDITION_FAILED": "Precondition failed", "RESOURCE_LOCKED": "Resource is currently locked", "QUOTA_EXCEEDED": "Quota limit exceeded", "SESSION_EXPIRED": "Session has expired", "ACCOUNT_SUSPENDED": "Account has been suspended", "ACCOUNT_LOCKED": "Account is temporarily locked", "PASSWORD_EXPIRED": "Password has expired", "TWO_FACTOR_REQUIRED": "Two-factor authentication required", "EMAIL_VERIFICATION_REQUIRED": "Email verification required", "PHONE_VERIFICATION_REQUIRED": "Phone verification required", "TERMS_ACCEPTANCE_REQUIRED": "Terms and conditions acceptance required", "PRIVACY_POLICY_ACCEPTANCE_REQUIRED": "Privacy policy acceptance required", "GDPR_CONSENT_REQUIRED": "GDPR consent required", "DATA_RETENTION_POLICY": "Data retention policy applies", "BACKUP_COMPLETED": "Backup completed successfully", "BACKUP_FAILED": "Backup operation failed", "RESTORE_COMPLETED": "Restore completed successfully", "RESTORE_FAILED": "Restore operation failed", "SYNC_COMPLETED": "Synchronization completed successfully", "SYNC_FAILED": "Synchronization failed", "MIGRATION_COMPLETED": "Migration completed successfully", "MIGRATION_FAILED": "Migration failed", "HEALTH_CHECK_PASSED": "Health check passed", "HEALTH_CHECK_FAILED": "Health check failed", "PERFORMANCE_DEGRADED": "Performance degradation detected", "PERFORMANCE_OPTIMAL": "System performance is optimal", "SECURITY_ALERT": "Security alert triggered", "SECURITY_BREACH": "Security breach detected", "AUDIT_LOG_CREATED": "Audit log entry created", "COMPLIANCE_CHECK_PASSED": "Compliance check passed", "COMPLIANCE_CHECK_FAILED": "Compliance check failed", "SOMETHING_WENT_WRONG": "Something went wrong", "SUCCESS_DATA_FETCHED": "Data fetched successfully", "SUCCESS_DATA_CREATED": "Data created successfully", "SUCCESS_DATA_UPDATED": "Data updated successfully", "SUCCESS_DATA_RETRIEVED": "Data retrieved successfully", "ERROR_CREATING_TICKET": "Error creating ticket", "ERROR_UPDATING_TICKET": "Error updating ticket", "ERROR_DELETING_TICKET": "Error deleting ticket", "ERROR_ASSIGNING_TICKET": "Error assigning ticket", "ERROR_UPDATING_TICKET_STATUS": "Error updating ticket status", "ERROR_FETCHING_TICKETS": "Error fetching tickets", "ERROR_FETCHING_TICKET": "Error fetching ticket", "TICKET_STATUS_UPDATED": "Ticket status updated successfully", "PERMISSION_DENIED": "Permission denied", "ACCESS_DENIED": "Access denied", "FORBIDDEN": "Forbidden", "BAD_REQUEST": "Bad request", "NOT_FOUND": "Not found", "METHOD_NOT_ALLOWED": "Method not allowed", "CONFLICT": "Conflict occurred", "UNPROCESSABLE_ENTITY": "Unprocessable entity", "TOO_MANY_REQUESTS": "Too many requests", "DATABASE_ERROR": "Database error occurred", "DATABASE_CONNECTION_ERROR": "Database connection error", "FILE_UPLOAD_ERROR": "File upload error", "FILE_SIZE_TOO_LARGE": "File size is too large", "FILE_TYPE_NOT_ALLOWED": "File type is not allowed", "INVALID_FILE_FORMAT": "Invalid file format", "NO_FILE_UPLOADED": "No file uploaded", "FILES_UPLOADED_SUCCESSFULLY": "Files uploaded successfully", "OPERATION_NOT_ALLOWED": "Operation not allowed", "RESOURCE_NOT_FOUND": "Resource not found", "INVALID_DATA_FORMAT": "Invalid data format", "MISSING_REQUIRED_FIELDS": "Missing required fields", "ORGANIZATION_ID_REQUIRED": "Organization ID is required", "TICKET_ID_REQUIRED": "Ticket ID is required", "MESSAGE_ID_REQUIRED": "Message ID is required", "USER_ID_REQUIRED": "User ID is required", "INVALID_TICKET_ID": "Invalid ticket ID", "INVALID_MESSAGE_ID": "Invalid message ID", "INVALID_USER_ID": "Invalid user ID", "INVALID_ORGANIZATION_ID": "Invalid organization ID", "CONFIG_CREATED_SUCCESSFULLY": "Configuration created successfully", "CONFIG_UPDATED_SUCCESSFULLY": "Configuration updated successfully", "CONFIG_FETCHED_SUCCESSFULLY": "Configuration retrieved successfully", "CONFIG_NOT_FOUND": "Configuration not found", "ERROR_UPDATING_CONFIG": "Error updating configuration", "ERROR_FETCHING_CONFIG": "Error fetching configuration", "PIN_VALIDATED_SUCCESSFULLY": "PIN validated successfully", "PIN_VALIDATION_FAILED": "PIN validation failed", "ERROR_VALIDATING_PIN": "Error validating PIN", "INVALID_PIN": "Invalid PIN", "MESSAGE_CREATED_SUCCESSFULLY": "Message created successfully", "ERROR_SENDING_MESSAGE": "Error sending message", "ERROR_FETCHING_MESSAGES": "Error fetching messages", "TICKET_PRIORITY_UPDATED": "Ticket priority updated successfully", "TICKET_CLOSED_SUCCESSFULLY": "Ticket closed successfully", "TICKET_REOPENED_SUCCESSFULLY": "Ticket reopened successfully", "TICKET_RESOLVED_SUCCESSFULLY": "Ticket resolved successfully", "ANALYTICS_FETCHED_SUCCESSFULLY": "Analytics data fetched successfully", "STATISTICS_FETCHED_SUCCESSFULLY": "Statistics fetched successfully", "BULK_OPERATION_COMPLETED": "Bulk operation completed successfully", "EXPORT_COMPLETED_SUCCESSFULLY": "Export completed successfully", "IMPORT_COMPLETED_SUCCESSFULLY": "Import completed successfully", "SUPPORT_PIN_REQUIRED": "Support PIN is required to create a ticket", "INVALID_SUPPORT_PIN": "Invalid support PIN. Please check your PIN and try again", "SUPPORT_CONFIG_NOT_FOUND": "Support configuration not found for this organization", "SUPPORT_PIN_VALID": "Support PIN is valid", "COMMENT_ADDED_SUCCESSFULLY": "Comment added to ticket successfully", "COMMENT_ADD_FAILED": "Failed to add comment to ticket", "COMMENTS_FETCHED_SUCCESSFULLY": "Ticket comments retrieved successfully", "COMMENTS_FETCH_FAILED": "Failed to retrieve ticket comments", "INTERNAL_COMMENT_ADDED": "Internal comment added successfully", "PUBLIC_COMMENT_ADDED": "Public comment added successfully", "TICKET_STATUS_UPDATED_SUCCESSFULLY": "Support ticket status updated successfully", "TICKET_STATUS_UPDATE_FAILED": "Failed to update support ticket status", "TICKET_MODULE_UPDATED": "Support ticket module type updated successfully", "TICKET_MODULE_UPDATE_FAILED": "Failed to update support ticket module type", "INVALID_TICKET_IDENTIFIER": "Invalid ticket identifier provided", "INVALID_TICKET_STATUS": "Invalid ticket status provided", "INVALID_PRIORITY": "Invalid priority level provided", "INVALID_MODULE_TYPE": "Invalid module type provided", "INVALID_ISSUE_TYPE": "Invalid issue type provided", "INVALID_MESSAGE_TYPE": "Invalid message type provided", "FILE_UPLOAD_SUCCESS": "File uploaded successfully", "FILE_SIZE_EXCEEDED": "File size exceeds maximum allowed limit", "MAX_FILES_EXCEEDED": "Maximum number of files exceeded", "REQUIRED_FIELD_MISSING": "Required field is missing", "INVALID_EMAIL_FORMAT": "Invalid email format provided", "INVALID_PHONE_FORMAT": "Invalid phone number format", "TEXT_TOO_LONG": "Text exceeds maximum allowed length", "TEXT_TOO_SHORT": "Text is too short. Minimum length required", "ORGANIZATION_NOT_FOUND": "Organization not found", "ORGANIZATION_DISABLED": "Organization is disabled", "ORGANIZATION_INACTIVE": "Organization is inactive", "USER_INACTIVE": "User account is inactive", "USER_UNAUTHORIZED": "User is not authorized for this action", "AGENT_ASSIGNED": "Agent assigned to ticket successfully", "AGENT_UNASSIGNED": "Agent unassigned from ticket successfully", "AGENT_NOT_FOUND": "Agent not found", "PRIORITY_UPDATED": "Ticket priority updated successfully", "STATUS_CHANGED": "Ticket status changed successfully", "MODULE_ASSIGNED": "Module type assigned successfully", "ATTACHMENT_ADDED": "Attachment added successfully", "ATTACHMENT_REMOVED": "Attachment removed successfully", "ATTACHMENT_NOT_FOUND": "Attachment not found", "HISTORY_RECORDED": "Action recorded in ticket history", "HISTORY_FETCH_FAILED": "Failed to retrieve ticket history", "SEARCH_RESULTS_FOUND": "Search results found", "NO_SEARCH_RESULTS": "No search results found", "SEARCH_QUERY_INVALID": "Invalid search query", "PAGINATION_ERROR": "Pagination parameters are invalid", "PAGE_NOT_FOUND": "Requested page not found", "FILTER_APPLIED": "Filters applied successfully", "FILTER_INVALID": "Invalid filter parameters", "EMAIL_SENT": "<PERSON>ail sent successfully", "EMAIL_FAILED": "Failed to send email", "CACHE_CLEARED": "<PERSON><PERSON> cleared successfully", "CACHE_ERROR": "Cache operation failed", "REQUEST_TIMEOUT": "Request timeout. Please try again", "SUCCESS": "Operation completed successfully", "ERROR": "An error occurred", "WARNING": "Warning: Please review the information", "INFO": "Information updated", "CREATED": "Created successfully", "UPDATED": "Updated successfully", "DELETED": "Deleted successfully", "RETRIEVED": "Retrieved successfully", "USERS_FETCHED_SUCCESSFULLY": "Users retrieved successfully", "ERROR_FETCHING_USERS": "Error fetching users", "USER_DETAILS_FETCHED_SUCCESSFULLY": "User details retrieved successfully", "ERROR_FETCHING_USER_DETAILS": "Error fetching user details", "USER_WORKLOADS_FETCHED_SUCCESSFULLY": "User workloads retrieved successfully", "ERROR_FETCHING_USER_WORKLOADS": "Error fetching user workloads", "ASSIGNEE_SUGGESTION_GENERATED": "Assignee suggestion generated successfully", "ERROR_SUGGESTING_ASSIGNEE": "Error generating assignee suggestion", "NO_ASSIGNABLE_USERS_FOUND": "No assignable users found", "TICKET_DATA_REQUIRED": "Ticket data is required", "ASSIGNED_USER_ID_REQUIRED": "Assigned user ID is required", "ASSIGNEE_NOT_FOUND": "Assignee not found", "ASSIGNEE_ORGANIZATION_MISMATCH": "Assignee must be from the same organization", "ASSIGNEE_NOT_ACTIVE": "Assignee account is not active", "ASSIGNEE_INSUFFICIENT_PERMISSIONS": "Assignee has insufficient permissions", "AI_CHAT_FAILED": "AI chat failed", "MESSAGE_REQUIRED": "Message is required", "CONTENT_REQUIRED": "Content is required", "TICKET_CONTENT_REQUIRED": "Ticket content is required", "TICKET_ANALYZED_SUCCESSFULLY": "Ticket analyzed successfully", "TICKET_ANALYSIS_FAILED": "Ticket analysis failed", "RESPONSE_GENERATED_SUCCESSFULLY": "Response generated successfully", "NO_RESPONSE_GENERATED": "No response generated", "RESPONSE_GENERATION_FAILED": "Response generation failed", "AI_STATUS_FETCHED_SUCCESSFULLY": "AI status fetched successfully", "AI_STATUS_FETCH_FAILED": "AI status fetch failed", "AI_CONNECTION_TEST_FAILED": "AI connection test failed", "ISSUE_TYPE_REQUIRED": "Issue type is required", "TROUBLESHOOTING_STEPS_FETCHED": "Troubleshooting steps fetched successfully", "TROUBLESHOOTING_STEPS_FETCH_FAILED": "Troubleshooting steps fetch failed", "AI_CONFIG_UPDATED_SUCCESSFULLY": "AI configuration updated successfully", "AI_CONFIG_UPDATE_FAILED": "AI configuration update failed", "DASHBOARD_FETCHED_SUCCESSFULLY": "Dashboard data fetched successfully", "DASHBOARD_FETCH_FAILED": "Dashboard data fetch failed", "OPERATION_COMPLETED": "Operation completed successfully", "INVALID_OPERATION": "Invalid operation", "TICKET_DELETED": "Ticket deleted successfully", "BULK_OPERATION_FAILED": "Bulk operation failed", "TICKET_IDS_REQUIRED": "Ticket IDs are required"}