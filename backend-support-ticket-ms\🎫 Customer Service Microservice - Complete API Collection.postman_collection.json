{"info": {"_postman_id": "39b788a8-a8ab-47c6-8b45-83cef885fce6", "name": "🎫 TTH Support Ticket Microservice - Complete API Collection v1.0", "description": "🎫 **Customer Service Microservice - Complete API Collection**\n\n**Features:**\n- ✅ Support ticket management (CRUD operations)\n- ✅ File attachments (up to 10MB per file, max 5 files)\n- ✅ Real-time messaging system with internal notes\n- ✅ Admin dashboard and bulk operations\n- ✅ Organization-scoped access control\n- ✅ Advanced filtering and search capabilities\n- ✅ Ticket assignment, resolution, and rating\n- ✅ Support configuration management\n\n**Authentication:** Bearer JWT token required for all private endpoints\n\n**API Endpoints:**\n- **Tickets:** `/v1/private/tickets/*` - Full CRUD, assign, resolve, rate\n- **Messages:** `/v1/private/messages/*` - Add, update, delete, search messages\n- **Admin:** `/v1/private/admin/*` - Dashboard, analytics, bulk operations\n- **Config:** `/v1/private/config/*` - Support configuration management\n\n**Environments:**\n- Development: http://localhost:8030\n- Staging: http://localhost:9030\n\n**Quick Start:**\n1. Set your JWT token in the `auth_token` variable\n2. Update `organization_id` and `ticket_id` variables\n3. Test with health check endpoints first\n4. Create tickets with proper authentication\n\n**Note:** All endpoints require authentication - no public ticket creation.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "40766612", "_collection_link": "https://sdadfasdfasdd.postman.co/workspace/My-Workspace~110d6b3d-9f51-4f2e-81ec-1f5290bae170/collection/40766612-39b788a8-a8ab-47c6-8b45-83cef885fce6?action=share&source=collection_link&creator=40766612"}, "item": [{"name": "🏥 Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}, {"name": "Public Health Check", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Msxousd7PkyMnalOneI4iFPHWDUF3uDnFiXwtkANwUw", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/public/health", "host": ["{{base_url}}"], "path": ["v1", "public", "health"]}}, "response": []}, {"name": "Validate Support PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": \"{{organization_id}}\",\n  \"support_pin\": \"{{support_pin}}\"\n}"}, "url": {"raw": "{{base_url}}/v1/public/validate-pin", "host": ["{{base_url}}"], "path": ["v1", "public", "validate-pin"]}}, "response": []}]}, {"name": "🎫 Support Tickets", "item": [{"name": "Get All Tickets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/list?page=1&limit=10&ticket_status=open&ticket_priority=high&ticket_module=hrms&ticket_type=bug&assigned_to_user_id=123&search=test&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "ticket_status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed"}, {"key": "ticket_priority", "value": "high", "description": "Filter by priority: low, medium, high, urgent"}, {"key": "ticket_module", "value": "hrms", "description": "Filter by module: hrms, pms, other"}, {"key": "ticket_type", "value": "bug", "description": "Filter by type: bug, request_for_feature, general_query, technical, non_technical, export_help, support"}, {"key": "assigned_to_user_id", "value": "123", "description": "Filter by assigned user ID"}, {"key": "search", "value": "test", "description": "Search in ticket title and description"}, {"key": "sort_by", "value": "created_at", "description": "Sort by: created_at, updated_at, ticket_priority, ticket_status"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization ID"}]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/list?page=1&limit=10", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "list"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": true,\n  \"message\": \"Tickets retrieved successfully\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"ticket_slug\": \"TKT-2024-001\",\n      \"ticket_title\": \"Unable to export recipe data\",\n      \"ticket_description\": \"Export button not responding\",\n      \"ticket_module\": \"hrms\",\n      \"ticket_type\": \"bug\",\n      \"ticket_priority\": \"high\",\n      \"ticket_status\": \"open\",\n      \"assigned_to_user_id\": null,\n      \"created_at\": \"2024-01-01T10:00:00.000Z\",\n      \"updated_at\": \"2024-01-01T10:00:00.000Z\"\n    }\n  ],\n  \"pagination\": {\n    \"current_page\": 1,\n    \"total_pages\": 5,\n    \"total_records\": 50,\n    \"per_page\": 10\n  },\n  \"timestamp\": \"2024-01-01T10:00:00.000Z\"\n}"}]}, {"name": "Create Ticket (Private - Authentication Required)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Unable to export recipe data", "description": "Ticket title (required, 5-200 chars)", "type": "text"}, {"key": "ticket_description", "value": "I'm having trouble exporting my recipe data. The export button doesn't respond when clicked.", "description": "Detailed description (required, 10-2000 chars)", "type": "text"}, {"key": "ticket_module", "value": "hrms", "description": "Module: hrms, pms, other (default: other)", "type": "text"}, {"key": "ticket_type", "value": "export_help", "description": "Type: bug, request_for_feature, general_query, technical, non_technical, export_help, support (default: general_query)", "type": "text"}, {"key": "ticket_priority", "value": "medium", "description": "Priority: low, medium, high, urgent (default: medium)", "type": "text"}, {"key": "ticketFiles", "description": "File attachments (max 5 files, 10MB each)", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/create", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "create"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Unable to export recipe data", "type": "text"}, {"key": "ticket_description", "value": "Export button not responding when clicked", "type": "text"}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/create", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "create"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": true,\n  \"message\": \"Ticket created successfully\",\n  \"data\": {\n    \"id\": 1,\n    \"ticket_slug\": \"TKT-2024-001\",\n    \"ticket_title\": \"Unable to export recipe data\",\n    \"ticket_description\": \"Export button not responding when clicked\",\n    \"ticket_module\": \"other\",\n    \"ticket_type\": \"general_query\",\n    \"ticket_priority\": \"medium\",\n    \"ticket_status\": \"open\",\n    \"organization_id\": \"org-001\",\n    \"ticket_owner_user_id\": 123,\n    \"created_at\": \"2024-01-01T10:00:00.000Z\",\n    \"updated_at\": \"2024-01-01T10:00:00.000Z\"\n  },\n  \"timestamp\": \"2024-01-01T10:00:00.000Z\"\n}"}]}, {"name": "Get Ticket by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}"]}}, "response": []}, {"name": "Assign <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"assigned_to_user_id\": 123,\n  \"change_note\": \"Assigning to technical support specialist\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/assign/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "assign", "{{ticket_id}}"]}}, "response": []}, {"name": "Update Ticket Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_progress\",\n  \"change_note\": \"Started working on the issue\",\n  \"resolution_note\": \"Investigating export functionality\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/status/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "status", "{{ticket_id}}"]}}, "response": []}, {"name": "Update Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Updated: Unable to export recipe data", "description": "Updated ticket title (5-200 chars)", "type": "text"}, {"key": "ticket_description", "value": "Updated description with more details about the export issue.", "description": "Updated ticket description (10-2000 chars)", "type": "text"}, {"key": "ticket_priority", "value": "high", "description": "Updated priority: low, medium, high, urgent", "type": "text"}, {"key": "ticket_status", "value": "in_progress", "description": "Updated status: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed", "type": "text"}, {"key": "ticket_module", "value": "hrms", "description": "Updated module: hrms, pms, other", "type": "text"}, {"key": "ticket_type", "value": "bug", "description": "Updated type: bug, request_for_feature, general_query, technical, non_technical, export_help, support", "type": "text"}, {"key": "assigned_to_user_id", "value": "123", "description": "Assign to user ID", "type": "text"}, {"key": "resolution_note", "value": "Issue resolved by updating export functionality", "description": "Resolution note (max 1000 chars)", "type": "text"}, {"key": "rating", "value": "5", "description": "Rating (1-5)", "type": "text"}, {"key": "review_comment", "value": "Excellent support", "description": "Review comment (max 500 chars)", "type": "text"}, {"key": "ticketFiles", "description": "Additional file attachments (max 5 files, 10MB each)", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/update/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "update", "{{ticket_id}}"]}}, "response": []}, {"name": "Delete Ticket", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/delete/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "delete", "{{ticket_id}}"]}}, "response": []}, {"name": "Get Ticket History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}/history?page=1&limit=50", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}", "history"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "50", "description": "Items per page (1-100, default: 50)"}]}}, "response": []}, {"name": "Get Ticket Attachments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}/attachments", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}", "attachments"]}}, "response": []}, {"name": "Resolve Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"resolution_note\": \"Issue resolved by updating export functionality\",\n  \"change_note\": \"Ticket resolved successfully\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/resolve/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "resolve", "{{ticket_id}}"]}}, "response": []}, {"name": "Rate Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"feedback\": \"Excellent support, issue resolved quickly\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/rate/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "rate", "{{ticket_id}}"]}}, "response": []}, {"name": "Update T<PERSON><PERSON> (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Msxousd7PkyMnalOneI4iFPHWDUF3uDnFiXwtkANwUw", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text", "disabled": true}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"module_type\": \"RECIPE\",\n  \"change_note\": \"Reassigning to recipe module team\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/module", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "module"]}}, "response": []}]}, {"name": "💬 Ticket Comments & Conversation", "item": [{"name": "Add Comment to Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "Thank you for reporting this issue. We are investigating the export functionality.", "description": "Comment text (required, 1-5000 chars)", "type": "text"}, {"key": "is_private", "value": "false", "description": "Whether this is an internal comment (agents/admins only)", "type": "text"}, {"key": "commentFiles", "description": "Optional file attachment for the comment", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/comments", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "comments"]}}, "response": []}, {"name": "Get Ticket Comments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/comments?include_private=false", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "comments"], "query": [{"key": "include_private", "value": "false", "description": "Include private comments (agents/admins only)"}]}}, "response": []}, {"name": "Get Ticket Conversation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/conversation?page=1&size=20&include_private=false", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "conversation"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "size", "value": "20", "description": "Messages per page (1-50, default: 20)"}, {"key": "include_private", "value": "false", "description": "Include private messages (agents/admins only)"}]}}, "response": []}, {"name": "Send Conversation Message", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "I've identified the issue with the export functionality. Working on a fix now.", "description": "Message content (required, 1-5000 chars)", "type": "text"}, {"key": "is_private", "value": "false", "description": "Whether this is an internal message (agents/admins only)", "type": "text"}, {"key": "messageFiles", "description": "Optional file attachment for the message", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/support/tickets/{{ticket_id}}/conversation", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "tickets", "{{ticket_id}}", "conversation"]}}, "response": []}]}, {"name": "📨 Support Messages", "item": [{"name": "Get Ticket Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages?page=1&limit=50&message_type=user_message&is_internal=false", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "50", "description": "Messages per page (1-100, default: 50)"}, {"key": "message_type", "value": "user_message", "description": "Filter by type: user_message, agent_message, internal_note, system_message"}, {"key": "is_internal", "value": "false", "description": "Filter by internal status: true, false"}]}}, "response": []}, {"name": "Add Message to Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_content", "value": "The export issue has been resolved. Please try exporting your data again.", "description": "Message content (required, 1-2000 chars)", "type": "text"}, {"key": "message_type", "value": "agent_message", "description": "Message type: user_message, agent_message, internal_note, system_message (default: user_message)", "type": "text"}, {"key": "is_internal", "value": "false", "description": "Whether message is internal (default: false)", "type": "text"}, {"key": "messageFiles", "description": "File attachments (max 3 files)", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages"]}}, "response": []}, {"name": "Add Internal Note", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_content", "value": "Internal note: Customer contacted via phone for additional clarification.", "description": "Internal note content (required, 1-2000 chars)", "type": "text"}, {"key": "messageFiles", "description": "File attachments (max 3 files)", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/internal-note", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "internal-note"]}}, "response": []}, {"name": "Update Message", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"message_content\": \"Updated message content\",\n  \"is_internal\": false\n}"}, "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages/{{message_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages", "{{message_id}}"]}}, "response": []}, {"name": "Delete Message", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages/{{message_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages", "{{message_id}}"]}}, "response": []}, {"name": "Search Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/messages/search?q=export&page=1&limit=50&ticket_id={{ticket_id}}&message_type=user_message&date_from=2024-01-01&date_to=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "search"], "query": [{"key": "q", "value": "export", "description": "Search query (required, 1-100 chars)"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "50", "description": "Messages per page (1-100, default: 50)"}, {"key": "ticket_id", "value": "{{ticket_id}}", "description": "Filter by specific ticket ID"}, {"key": "message_type", "value": "user_message", "description": "Filter by type: user_message, agent_message, internal_note, system_message"}, {"key": "date_from", "value": "2024-01-01", "description": "Search from date (YYYY-MM-DD)"}, {"key": "date_to", "value": "2024-12-31", "description": "Search to date (YYYY-MM-DD)"}]}}, "response": []}]}, {"name": "🎯 Ticket Actions", "item": [{"name": "Assign <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"assigned_to_user_id\": 123\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/assign/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "assign", "{{ticket_id}}"]}}, "response": []}, {"name": "Resolve Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"resolution_note\": \"Issue resolved by updating the export functionality. Customer can now export data successfully.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/resolve/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "resolve", "{{ticket_id}}"]}}, "response": []}, {"name": "Rate Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"review_comment\": \"Excellent support! Issue was resolved quickly and efficiently.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/rate/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "rate", "{{ticket_id}}"]}}, "response": []}]}, {"name": "👥 User Management & AI", "item": [{"name": "Get Assignable Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/support/assignable-users?search=john&roleFilter=agent&page=1&limit=20", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "assignable-users"], "query": [{"key": "search", "value": "john", "description": "Search term for user name or email"}, {"key": "<PERSON><PERSON><PERSON>er", "value": "agent", "description": "Filter by user role"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "20", "description": "Items per page (default: 20)"}]}}, "response": []}, {"name": "AI Suggest Assignee", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticketData\": {\n    \"description\": \"User is unable to export recipe data from the system\",\n    \"subject\": \"Export functionality not working\",\n    \"priority\": \"HIGH\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/support/suggest-assignee", "host": ["{{base_url}}"], "path": ["v1", "private", "support", "suggest-assignee"]}}, "response": []}]}, {"name": "🔧 Admin Dashboard & Management", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/dashboard?organization_id={{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "dashboard"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}]}}, "response": []}, {"name": "Admin Get Tickets with Advanced Filters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/tickets?page=1&limit=10&search=export&ticket_status=open&ticket_priority=high&ticket_module=hrms&ticket_type=bug&assigned_to_user_id=null&date_from=2024-01-01&date_to=2024-12-31&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "search", "value": "export", "description": "Search in ticket number, subject, submitter name/email"}, {"key": "ticket_status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed"}, {"key": "ticket_priority", "value": "high", "description": "Filter by priority: low, medium, high, urgent"}, {"key": "ticket_module", "value": "hrms", "description": "Filter by module: hrms, pms, other"}, {"key": "ticket_type", "value": "bug", "description": "Filter by type: bug, request_for_feature, general_query, technical, non_technical, export_help, support"}, {"key": "assigned_to_user_id", "value": "123", "description": "Filter by assigned user ID"}, {"key": "sort_by", "value": "created_at", "description": "Sort field (default: created_at)"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC (default: DESC)"}]}}, "response": []}, {"name": "Bulk Ticket Operations", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3, 4, 5],\n  \"operation\": \"assign\",\n  \"assigned_to_user_id\": 123,\n  \"bulk_note\": \"Bulk assignment to technical team\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}}, "response": []}, {"name": "Bulk Status Update", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3],\n  \"operation\": \"status_change\",\n  \"new_status\": \"resolved\",\n  \"bulk_note\": \"Bulk resolution after system update\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}}, "response": []}, {"name": "Admin Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/analytics?organization_id={{organization_id}}&period=30d&date_from=2024-01-01&date_to=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "analytics"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)", "disabled": true}, {"key": "period", "value": "30d", "description": "Time period: 7d, 30d, 90d, 1y (default: 30d)"}, {"key": "date_from", "value": "2024-01-01", "description": "Custom start date (overrides period)", "disabled": true}, {"key": "date_to", "value": "2024-12-31", "description": "Custom end date (overrides period)", "disabled": true}]}}, "response": []}, {"name": "Bulk Priority Update", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3],\n  \"operation\": \"priority_change\",\n  \"new_priority\": \"high\",\n  \"bulk_note\": \"Escalating priority due to business impact\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}}, "response": []}]}, {"name": "⚙️ Support Configuration", "item": [{"name": "Get All Support Configurations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization ID"}]}}, "response": []}, {"name": "Get Organization Support Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"]}}, "response": []}, {"name": "Create/Update Support Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"config_key\": \"support_pin\",\n  \"config_value\": \"SUPPORT2024\",\n  \"config_description\": \"Organization support PIN for ticket creation\",\n  \"is_active\": true,\n  \"config_type\": \"string\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"]}}, "response": []}, {"name": "Delete Support Config", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}"]}}, "response": []}, {"name": "<PERSON> Default Config", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/default", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "default"]}}, "response": []}, {"name": "Update Default Config", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"reset_to_defaults\": true,\n  \"config_keys\": [\"support_pin\", \"max_attachment_size\"]\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/default", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "default"]}}, "response": []}, {"name": "Toggle Support Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"is_support_enabled\": true,\n  \"maintenance_message\": \"Support is temporarily unavailable for maintenance.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/{{organization_id}}/toggle", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "{{organization_id}}", "toggle"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set timestamp for requests", "pm.globals.set('timestamp', new Date().toISOString());"]}}], "variable": [{"key": "base_url", "value": "http://localhost:9030"}, {"key": "auth_token", "value": "your_jwt_token_here"}, {"key": "ticket_id", "value": "1"}, {"key": "organization_id", "value": "org-001"}, {"key": "support_pin", "value": "SUPPORT2024"}, {"key": "user_id", "value": "123"}, {"key": "message_id", "value": "1"}, {"key": "staging_base_url", "value": "http://localhost:9030"}, {"key": "development_base_url", "value": "http://localhost:8030"}]}