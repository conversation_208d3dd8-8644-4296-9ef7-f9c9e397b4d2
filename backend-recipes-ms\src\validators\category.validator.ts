import { celebrate, Joi, Segments } from "celebrate";
import { CategoryStatus, CategoryType } from "../models/Category";

const createCategoryValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        category_name: Joi.string().min(2).max(100).required(),
        category_description: Joi.string().allow(null, "").optional(),
        category_type: Joi.string()
          .valid(...Object.values(CategoryType))
          .required(),
        category_status: Joi.string()
          .valid(...Object.values(CategoryStatus))
          .optional(),
        is_system_category: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
      })
      .unknown(true), // Allow file uploads and other fields
  });

const updateCategoryValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        category_name: Joi.string().min(2).max(100).optional(),
        category_description: Joi.string().allow(null, "").optional(),
        category_type: Joi.string()
          .valid(...Object.values(CategoryType))
          .optional(),
        category_status: Joi.string()
          .valid(...Object.values(CategoryStatus))
          .optional(),
        is_system_category: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional(),
      })
      .unknown(true), // Allow file uploads and other fields
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().min(1).required(),
    }),
  });

const deleteCategoryValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getCategoryValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getCategoriesListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      search: Joi.string().max(100).optional(),
      status: Joi.string()
        .valid(...Object.values(CategoryStatus))
        .optional(),
      type: Joi.string()
        .valid(...Object.values(CategoryType))
        .optional(),
      sort_by: Joi.string()
        .valid("category_name", "created_at", "updated_at")
        .default("created_at"),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
    }),
  });

// Default export object
export default {
  createCategoryValidator,
  updateCategoryValidator,
  deleteCategoryValidator,
  getCategoryValidator,
  getCategoriesListValidator,
};
