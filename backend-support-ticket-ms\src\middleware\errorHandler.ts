import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import { ValidationError as SequelizeValidationError } from "sequelize";
import { CelebrateError } from "celebrate";
import { logger } from "../utils/logger";

/**
 * Custom error class for application-specific errors
 */
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public errorCode?: string;

  constructor(message: string, statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR, errorCode?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.errorCode = errorCode;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Predefined error types for common scenarios
 */
export class NotFoundError extends AppError {
  constructor(resource: string = "Resource") {
    super(`${resource} not found`, StatusCodes.NOT_FOUND, "RESOURCE_NOT_FOUND");
  }
}

// ValidationError removed to avoid conflict with Sequelize ValidationError
// Use AppError with VALIDATION_ERROR code instead

export class UnauthorizedError extends AppError {
  constructor(message: string = "Unauthorized access") {
    super(message, StatusCodes.UNAUTHORIZED, "UNAUTHORIZED");
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = "Access forbidden") {
    super(message, StatusCodes.FORBIDDEN, "FORBIDDEN");
  }
}

export class ConflictError extends AppError {
  constructor(message: string = "Resource conflict") {
    super(message, StatusCodes.CONFLICT, "CONFLICT");
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = "Rate limit exceeded") {
    super(message, StatusCodes.TOO_MANY_REQUESTS, "RATE_LIMIT_EXCEEDED");
  }
}

/**
 * Format Sequelize validation errors
 */
const formatSequelizeError = (error: any): { message: string; details: any[] } => {
  const details: any[] = [];
  
  if (error.errors && Array.isArray(error.errors)) {
    error.errors.forEach((err: any) => {
      details.push({
        field: err.path || err.field,
        message: err.message,
        value: err.value,
        type: err.type || err.validatorKey
      });
    });
  }

  return {
    message: "Validation failed",
    details
  };
};

/**
 * Format Celebrate (Joi) validation errors
 */
const formatCelebrateError = (error: CelebrateError): { message: string; details: any[] } => {
  const details: any[] = [];
  
  error.details.forEach((detail, segment) => {
    detail.details.forEach((err) => {
      details.push({
        field: err.path.join('.'),
        message: err.message,
        value: err.context?.value,
        type: err.type,
        segment: segment
      });
    });
  });

  return {
    message: "Request validation failed",
    details
  };
};

/**
 * Format database constraint errors
 */
const formatDatabaseError = (error: any): { message: string; details?: any } => {
  // Foreign key constraint
  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return {
      message: "Referenced resource does not exist",
      details: {
        constraint: error.index,
        field: error.fields,
        table: error.table
      }
    };
  }

  // Unique constraint
  if (error.name === 'SequelizeUniqueConstraintError') {
    return {
      message: "Resource already exists",
      details: {
        fields: error.fields,
        value: error.value
      }
    };
  }

  // Connection error
  if (error.name === 'SequelizeConnectionError') {
    return {
      message: "Database connection failed"
    };
  }

  // Timeout error
  if (error.name === 'SequelizeTimeoutError') {
    return {
      message: "Database operation timed out"
    };
  }

  return {
    message: "Database operation failed"
  };
};

/**
 * Main error handling middleware
 */
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;
  let message = "Internal server error";
  let errorCode = "INTERNAL_ERROR";
  let details: any = undefined;

  // Log error using structured logger
  logger.error("Request error occurred", error, {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    organizationId: (req as any).user?.organization_id,
    requestId: req.headers['x-request-id'] as string
  });

  // Handle different error types
  if (error instanceof AppError) {
    // Custom application errors
    statusCode = error.statusCode;
    message = error.message;
    errorCode = error.errorCode || "APP_ERROR";
  } else if (error.isCelebrateError) {
    // Celebrate (Joi) validation errors
    statusCode = StatusCodes.BAD_REQUEST;
    errorCode = "VALIDATION_ERROR";
    const formatted = formatCelebrateError(error);
    message = formatted.message;
    details = formatted.details;
  } else if (error.name === 'SequelizeValidationError') {
    // Sequelize validation errors
    statusCode = StatusCodes.BAD_REQUEST;
    errorCode = "VALIDATION_ERROR";
    const formatted = formatSequelizeError(error);
    message = formatted.message;
    details = formatted.details;
  } else if (error.name?.startsWith('Sequelize')) {
    // Other Sequelize errors
    statusCode = StatusCodes.BAD_REQUEST;
    errorCode = "DATABASE_ERROR";
    const formatted = formatDatabaseError(error);
    message = formatted.message;
    details = formatted.details;
  } else if (error.name === 'JsonWebTokenError') {
    // JWT errors
    statusCode = StatusCodes.UNAUTHORIZED;
    message = "Invalid authentication token";
    errorCode = "INVALID_TOKEN";
  } else if (error.name === 'TokenExpiredError') {
    // JWT expiration
    statusCode = StatusCodes.UNAUTHORIZED;
    message = "Authentication token expired";
    errorCode = "TOKEN_EXPIRED";
  } else if (error.name === 'MulterError') {
    // File upload errors
    statusCode = StatusCodes.BAD_REQUEST;
    errorCode = "FILE_UPLOAD_ERROR";
    
    if (error.code === 'LIMIT_FILE_SIZE') {
      message = "File size too large";
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      message = "Too many files";
    } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      message = "Unexpected file field";
    } else {
      message = "File upload failed";
    }
  } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
    // Network errors
    statusCode = StatusCodes.SERVICE_UNAVAILABLE;
    message = "External service unavailable";
    errorCode = "SERVICE_UNAVAILABLE";
  }

  // Prepare response
  const response: any = {
    success: false,
    message,
    error_code: errorCode,
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method
  };

  // Add details if available
  if (details) {
    response.details = details;
  }

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.stack = error.stack;
  }

  // Add request ID if available
  if (req.headers['x-request-id']) {
    response.request_id = req.headers['x-request-id'];
  }

  res.status(statusCode).json(response);
};

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(StatusCodes.NOT_FOUND).json({
    success: false,
    message: `Route ${req.method} ${req.path} not found`,
    error_code: "ROUTE_NOT_FOUND",
    timestamp: new Date().toISOString(),
    path: req.path,
    method: req.method
  });
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Validation error helper
 */
export const createValidationError = (field: string, message: string): AppError => {
  return new AppError(`${field}: ${message}`, StatusCodes.BAD_REQUEST, "VALIDATION_ERROR");
};

/**
 * Success response helper
 */
export const successResponse = (
  res: Response,
  data: any = null,
  message: string = "Success",
  statusCode: number = StatusCodes.OK
): void => {
  const response: any = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  res.status(statusCode).json(response);
};

/**
 * Paginated response helper
 */
export const paginatedResponse = (
  res: Response,
  data: any[],
  pagination: {
    current_page: number;
    total_pages: number;
    total_records: number;
    per_page: number;
  },
  message: string = "Data retrieved successfully"
): void => {
  res.status(StatusCodes.OK).json({
    success: true,
    message,
    data,
    pagination,
    timestamp: new Date().toISOString()
  });
};
