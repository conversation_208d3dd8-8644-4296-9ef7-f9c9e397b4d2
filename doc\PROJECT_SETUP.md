# TTH Recipe Manager - Complete Project Setup Guide

## 🎯 **Project Overview**

The TTH Recipe Manager is a comprehensive full-stack application consisting of:

- **Backend**: Node.js + TypeScript + Express + MySQL (Port 3000)
- **Frontend**: Next.js + TypeScript + Tailwind CSS (Port 3001)

## 🚀 **Quick Start**

### **Option 1: Automated Setup (Recommended)**

#### **For Linux/macOS:**
```bash
chmod +x start-dev.sh
./start-dev.sh
```

#### **For Windows:**
```cmd
start-dev.bat
```

### **Option 2: Manual Setup**

#### **1. Backend Setup**
```bash
# Install backend dependencies
npm install

# Copy environment file
cp .env.example .env

# Configure your database and other settings in .env
# Edit .env file with your database credentials

# Start backend development server
npm run dev
```

#### **2. Frontend Setup**
```bash
# Navigate to frontend directory
cd frontend

# Install frontend dependencies
npm install

# Copy environment file
cp .env.example .env.local

# Start frontend development server
npm run dev
```

## 🔧 **Environment Configuration**

### **Backend (.env)**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=tth_recipe_db
DB_USER=your_username
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# Application Configuration
PORT=3000
NODE_ENV=development

# Organization Configuration
DEFAULT_ORGANIZATION_ID=org_001
DEFAULT_BRANCH_ID=1

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

### **Frontend (.env.local)**
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api/v1

# Application Configuration
NEXT_PUBLIC_APP_NAME=TTH Recipe Manager
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Feature Flags
NEXT_PUBLIC_ENABLE_DARK_MODE=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_DEBUG=true
```

## 📊 **Database Setup**

### **Option 1: Using Docker (Recommended)**
```bash
# Start MySQL with Docker
docker run --name tth-mysql \
  -e MYSQL_ROOT_PASSWORD=rootpassword \
  -e MYSQL_DATABASE=tth_recipe_db \
  -e MYSQL_USER=tth_user \
  -e MYSQL_PASSWORD=tth_password \
  -p 3306:3306 \
  -d mysql:8.0

# Wait for MySQL to start, then run migrations
npm run migrate
npm run seed
```

### **Option 2: Local MySQL Installation**
1. Install MySQL 8.0+
2. Create database: `CREATE DATABASE tth_recipe_db;`
3. Create user and grant permissions
4. Update `.env` with your credentials
5. Run migrations: `npm run migrate`
6. Seed data: `npm run seed`

## 🌐 **Application URLs**

Once both servers are running:

- **Frontend Application**: http://localhost:3001
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health

## 👤 **Default Login Credentials**

For testing purposes, use these demo credentials:

```
Email: <EMAIL>
Password: demo123
```

## 🎨 **Frontend Features**

### **Modern UI/UX**
- ✅ Responsive design for all devices
- ✅ Dark/Light theme with system preference
- ✅ Smooth animations with Framer Motion
- ✅ Glassmorphism design elements
- ✅ Accessible components

### **Recipe Management**
- ✅ Create, edit, and delete recipes
- ✅ Advanced search and filtering
- ✅ Grid and list view modes
- ✅ Recipe categorization and tagging
- ✅ Image upload and management
- ✅ Cost calculation and scaling
- ✅ Recipe duplication

### **User Experience**
- ✅ Real-time search with debouncing
- ✅ Optimistic updates
- ✅ Error boundaries and graceful error handling
- ✅ Loading states and skeleton screens
- ✅ Toast notifications

## 🔧 **Backend Features**

### **Recipe Management**
- ✅ CRUD operations for recipes
- ✅ Advanced filtering and search
- ✅ Recipe cost calculation
- ✅ Recipe scaling functionality
- ✅ Recipe duplication
- ✅ Nutritional information tracking

### **Ingredient Management**
- ✅ Comprehensive ingredient database
- ✅ Supplier information and costs
- ✅ Waste and yield tracking
- ✅ Unit conversion support
- ✅ Allergen information

### **Cost Management**
- ✅ Automatic cost calculation
- ✅ Labor and overhead tracking
- ✅ Profit margin analysis
- ✅ Cost per serving calculation

### **API Features**
- ✅ RESTful API design
- ✅ JWT authentication
- ✅ Role-based access control
- ✅ Request validation
- ✅ Error handling
- ✅ API documentation with Swagger
- ✅ Rate limiting
- ✅ CORS configuration

## 📁 **Project Structure**

```
tth-recipe-manager/
├── src/                          # Backend source code
│   ├── controllers/              # Request handlers
│   ├── models/                   # Database models
│   ├── routes/                   # API routes
│   ├── middleware/               # Custom middleware
│   ├── helpers/                  # Business logic helpers
│   ├── config/                   # Configuration files
│   └── utils/                    # Utility functions
├── frontend/                     # Frontend application
│   ├── src/
│   │   ├── app/                  # Next.js pages (App Router)
│   │   ├── components/           # React components
│   │   ├── contexts/             # React contexts
│   │   ├── lib/                  # Utility libraries
│   │   └── types/                # TypeScript definitions
│   ├── public/                   # Static assets
│   └── tailwind.config.js        # Tailwind configuration
├── migrations/                   # Database migrations
├── seeds/                        # Database seeds
├── docs/                         # Documentation
├── tests/                        # Test files
├── start-dev.sh                  # Linux/macOS startup script
├── start-dev.bat                 # Windows startup script
└── README.md                     # Project documentation
```

## 🧪 **Testing**

### **Backend Testing**
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### **Frontend Testing**
```bash
cd frontend

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e
```

## 🚀 **Production Deployment**

### **Backend Deployment**
```bash
# Build for production
npm run build

# Start production server
npm start
```

### **Frontend Deployment**
```bash
cd frontend

# Build for production
npm run build

# Start production server
npm start
```

### **Environment Variables for Production**
Update environment variables for production:

**Backend:**
- Set `NODE_ENV=production`
- Use production database credentials
- Set secure JWT secret
- Configure CORS for production domain

**Frontend:**
- Set `NEXT_PUBLIC_API_URL` to production API URL
- Set `NEXT_PUBLIC_APP_URL` to production domain
- Disable debug features

## 🔒 **Security Considerations**

### **Backend Security**
- ✅ JWT authentication with secure secrets
- ✅ Password hashing with bcrypt
- ✅ Input validation and sanitization
- ✅ SQL injection prevention with Sequelize
- ✅ CORS configuration
- ✅ Rate limiting
- ✅ Helmet.js for security headers

### **Frontend Security**
- ✅ Secure cookie storage for tokens
- ✅ XSS prevention
- ✅ CSRF protection
- ✅ Input validation
- ✅ Secure API communication

## 🐛 **Troubleshooting**

### **Common Issues**

**Port Already in Use**
```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9

# Kill process on port 3001
lsof -ti:3001 | xargs kill -9
```

**Database Connection Issues**
- Verify MySQL is running
- Check database credentials in `.env`
- Ensure database exists
- Check firewall settings

**Module Not Found Errors**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# For frontend
cd frontend
rm -rf node_modules package-lock.json
npm install
```

**Build Errors**
```bash
# Clear build cache
npm run clean

# For Next.js
cd frontend
rm -rf .next
npm run build
```

## 📚 **API Documentation**

The API documentation is available at:
- **Development**: http://localhost:3000/api-docs
- **Swagger JSON**: http://localhost:3000/api-docs.json

### **Key API Endpoints**

**Authentication**
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/me` - Get current user

**Recipes**
- `GET /api/v1/recipes` - Get all recipes
- `POST /api/v1/recipes` - Create recipe
- `GET /api/v1/recipes/:id` - Get recipe by ID
- `PUT /api/v1/recipes/:id` - Update recipe
- `DELETE /api/v1/recipes/:id` - Delete recipe
- `POST /api/v1/recipes/:id/calculate-cost` - Calculate recipe cost
- `POST /api/v1/recipes/:id/scale` - Scale recipe
- `POST /api/v1/recipes/:id/duplicate` - Duplicate recipe

**Ingredients**
- `GET /api/v1/ingredients` - Get all ingredients
- `POST /api/v1/ingredients` - Create ingredient
- `GET /api/v1/ingredients/:id` - Get ingredient by ID
- `PUT /api/v1/ingredients/:id` - Update ingredient
- `DELETE /api/v1/ingredients/:id` - Delete ingredient

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests
5. Run quality checks
6. Submit a pull request

## 📄 **License**

This project is proprietary to TeamTrainHub. All rights reserved.

## 🆘 **Support**

For support and questions:
- **Documentation**: [TTH Recipe Manager Docs]
- **Issues**: [GitHub Issues]
- **Email**: <EMAIL>

---

**Happy Coding! 🚀**
