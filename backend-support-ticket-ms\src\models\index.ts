"use strict";

import fs from "fs";
import path from "path";
import Sequelize, { Op, QueryTypes } from "sequelize";
const basename = path.basename(__filename);

const config = (global as any).db;
const db: any = {};
let sequelize: any;
if ((global as any).config.use_env_variable) {
  sequelize = new Sequelize.Sequelize(
    (global as any).config.use_env_variable,
    config
  );
} else {
  sequelize = new Sequelize.Sequelize(
    config.database,
    config.username,
    config.password,
    config
  );
}
sequelize
  .authenticate()
  .then(() => {})
  .catch((err: Error) => {});

// Load models in specific order to handle dependencies
const modelFiles = [
  "User.ts", // Load User first (independent)
  "Item.ts", // Load Item second (independent)
  "SupportConfig.ts", // Load SupportConfig third (references organization)
  "Ticket.ts", // Load Ticket fourth (main entity)
  "TicketAttachment.ts", // Load TicketAttachment fifth (depends on Ticket and Item)
  "TicketMessage.ts", // Load TicketMessage sixth (depends on Ticket)
  "TicketHistory.ts", // Load TicketHistory last (depends on Ticket)
];

// Load models in the specified order
modelFiles.forEach((file) => {
  // Try both .ts and .js extensions
  const tsPath = path.join(__dirname, file);
  const jsPath = path.join(__dirname, file.replace(".ts", ".js"));

  let filePath = tsPath;
  if (!fs.existsSync(tsPath) && fs.existsSync(jsPath)) {
    filePath = jsPath;
  }

  if (fs.existsSync(filePath)) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const model = require(filePath);

      // Handle both default exports and named exports
      let modelClass = model.default;

      if (!modelClass) {
        const modelKey = Object.keys(model).find(
          (key) =>
            model[key] &&
            typeof model[key] === "function" &&
            model[key].prototype instanceof Sequelize.Model
        );
        if (modelKey) {
          modelClass = model[modelKey];
        }
      }

      if (modelClass && modelClass.name) {
        db[modelClass.name] = modelClass;
      }
    } catch (error) {
      console.error(`❌ Error loading model ${file}:`, error);
    }
  }
});

// Load any remaining models that weren't in the list
fs.readdirSync(__dirname)
  .filter((file) => {
    return (
      file.indexOf(".") !== 0 &&
      file !== basename &&
      file.slice(-3) === ".ts" &&
      !modelFiles.includes(file)
    );
  })
  .forEach((file) => {
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const model = require(path.join(__dirname, file));
      // Handle both default exports and named exports
      let modelClass = model.default;

      if (!modelClass) {
        const modelKey = Object.keys(model).find(
          (key) =>
            model[key] &&
            typeof model[key] === "function" &&
            model[key].prototype instanceof Sequelize.Model
        );
        if (modelKey) {
          modelClass = model[modelKey];
        }
      }

      if (modelClass && modelClass.name) {
        db[modelClass.name] = modelClass;
      }
    } catch (error) {
      console.error(`❌ Error loading additional model ${file}:`, error);
    }
  });

// Set up model associations
Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    try {
      db[modelName].associate(db);
    } catch (error) {
      console.error(
        `❌ Error setting up associations for ${modelName}:`,
        error
      );
    }
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;
db.Op = Op;
db.QueryTypes = QueryTypes;

export { db, sequelize, QueryTypes };
