### **Support Ticket Flow \- Phase 1**

1. **Login:** User logs into the system.  
2. **Select Module:** Prompt the user to select the module (HRMS, PMS, or Other) related to their issue.  
3. **Issue Type:** Ask for the category of the issue (e.g., **Bug**, **Request for Feature**, or **General Query**).  
4. **Brief Description:** Request a short description of the issue.  
5. **Urgency Level:** Ask how critical the issue is (**Low**, **Medium**, **High**).  
6. **Contact Information:** Autofill contact details from their account but allow edits if necessary.  
7. **Submission Confirmation:** Display a summary for confirmation before submission.  
8. Ticket have 2 modes Public , private.

---

### **Questions**

1. **Which module does your issue relate to?**  
   *(Dropdown: HRMS, PMS, Other)*  
2. **What type of issue are you reporting?**  
   *(Dropdown: Bug, Request for Feature, General Query)* 

   Technical/non technical/export help/support

3. **Please provide a brief description of your issue.**  
   *(Free text field)*  
4. **How urgent is this issue?**  
   *(Dropdown: Low, Medium, High)*  
5. **Is the contact information below correct?**  
   *(Autofill: Name, Email, Phone)*

---

### **End of Phase 1**

The ticket is created and sent to the support team with minimal user effort.

[https://prnt.sc/-d04ZcliDdDq](https://prnt.sc/-d04ZcliDdDq) ,[https://prnt.sc/wVmB\_fhbHpIO](https://prnt.sc/wVmB_fhbHpIO),

[https://prnt.sc/llseqoZHG4CP](https://prnt.sc/llseqoZHG4CP), [https://prnt.sc/xAk9N68boH1u](https://prnt.sc/xAk9N68boH1u)

**Priority :** \-None-, Emergency, Urgent, High, Medium, Low

**TicketStatus:**  Closed, Escalated, In-Progress, Invoiced, On Hold, Open, QA Review, Assigned, Under Review

# **\#Flow**

1. The organization super admin creates support/ticket for issue and it’s private.  
   1. Send email to main super admin  
2. Super admin review ticket and assigned to any developer member.  
   1. Send push notifications to the developer team.  
   2. Update ticket status and org super admin can view updated status and also notify through push notification.  
3. Super admin can update the status to public and the client will see our basic conversation.	  
   1. Currently, the conversation setup is based on API, not socket.  
   2. After the resolved error update the ticket status with resolved and notify the client through Push.

## WEB Design Planning:

### **Screen 1:  Create Ticket :** 

Form Fileds : 

1. Ticket-owner-Name\*  
2. Email\*  
3. Phone number\*  
4. Module name\*  
5. Issue type\*  
6. Problem Description\*  
7. File attachment  
8. Buttons : Submit , Cancel

---

Okay, I've checked the site: https://enchanting-stardust-2c64a3.netlify.app/

It appears to be a simple portfolio or informational website for "John Doe," showcasing skills and projects. It **does not currently have any visible support ticket desk system or functionality.**

Therefore, the SOW I prepare will be for **building a *new*, basic, simple support ticket desk system** that could *theoretically* be integrated into a site like this, or function as a standalone simple helpdesk.

If your intention was different (e.g., there's a hidden system or you wanted an SOW for the existing site's *content*), please let me know\!

Assuming you want an SOW for a **new, simple support ticket system:**

**Reference : [https://enchanting-stardust-2c64a3.netlify.app/](https://enchanting-stardust-2c64a3.netlify.app/)**

---

**Statement of Work (SOW): Basic Support Ticket Desk System MVP**

**Project Title:** Simple Support Ticket Desk \- MVP

**Document Version:** 1.0  
**Date:** October 26, 2023  
**Prepared For:** \[Client Name / Internal Project\]  
**Prepared By:** \[Your Name/Company Name\]

**1\. Project Overview & Purpose**

* **1.1. Introduction:** This document outlines the scope of work for the development of a Minimum Viable Product (MVP) for a "Simple Support Ticket Desk System."  
* **1.2. Project Goal:** To create a straightforward web-based system allowing users (e.g., clients, customers) to submit support requests (tickets) and for administrators/support agents to view, manage, and respond to these tickets. The MVP will focus on core ticketing functionality.  
* **1.3. Target Users:**  
  * **End-Users:** Individuals needing to submit a support request or query.  
  * **Administrators/Support Agents:** Individuals responsible for managing and responding to submitted tickets.

**2\. Scope of Work & Deliverables**

This section details the features and functionalities to be included in the MVP.

     \*\*2.1. User Authentication (Simplified for MVP \- for Admins/Agents)\*\*  
    \*   \*\*2.1.1. Admin/Agent Login:\*\*  
        \*   \*\*Feature:\*\* Secure login for administrators/support agents to access the ticket management interface.  
        \*   \*\*Function:\*\* Authenticate based on pre-defined credentials (e.g., username/password).  
        \*   \*\*Note:\*\* End-user ticket submission will not require login for MVP to keep it simple for them.

\*\*2.2. End-User Ticket Submission (Public Form)\*\*  
    \*   \*\*2.2.1. Submit New Ticket Form:\*\*  
        \*   \*\*Feature:\*\* A publicly accessible web form for end-users to submit support requests.  
        \*   \*\*Function:\*\* Form to collect:  
            \*   \*\*Name:\*\* (Text, Required)  
            \*   \*\*Email Address:\*\* (Email, Required, for communication)  
            \*   \*\*Subject:\*\* (Text, Required, brief summary of the issue)  
            \*   \*\*Description/Details:\*\* (Text Area, Required, detailed explanation of the issue/query)  
            \*   \*\*(Optional) Attachment:\*\* (File Upload, single file, basic types like image/pdf, size limit) \- \*Consider for V1.1 if MVP needs to be leaner.\*  
        \*   \*\*Note:\*\* No user account creation required for submission.  
    \*   \*\*2.2.2. Submission Confirmation:\*\*  
        \*   \*\*Feature:\*\* Provide feedback to the user upon successful ticket submission.  
        \*   \*\*Function:\*\* Display a success message on-screen (e.g., "Your ticket has been submitted. Ticket ID: \[XYZ123\]. We will get back to you shortly.").  
    \*   \*\*2.2.3. Email Notification to User (Basic):\*\*  
        \*   \*\*Feature:\*\* Notify the user that their ticket has been received.  
        \*   \*\*Function:\*\* Send an automated email to the user's provided email address confirming receipt of their ticket, including the Ticket ID.

\*\*2.3. Admin/Agent Ticket Management Interface\*\*  
    \*   \*\*2.3.1. Ticket Dashboard/Listing:\*\*  
        \*   \*\*Feature:\*\* Display a list of all submitted support tickets for logged-in admins/agents.  
        \*   \*\*Function:\*\* Table/list view showing: Ticket ID, Subject, Submitter Name, Submitter Email, Status (e.g., Open, In Progress, Closed), Date Submitted, Last Updated.  
        \*   \*\*Sorting:\*\* Ability to sort by Date Submitted, Status.  
        \*   \*\*Filtering (Basic):\*\* Ability to filter by Status.  
    \*   \*\*2.3.2. View Ticket Details:\*\*  
        \*   \*\*Feature:\*\* Allow admins/agents to view the full details of a specific ticket.  
        \*   \*\*Function:\*\* Display all information submitted by the user (Name, Email, Subject, Description, any attachments). Also show ticket history/comments.  
    \*   \*\*2.3.3. Update Ticket Status:\*\*  
        \*   \*\*Feature:\*\* Allow admins/agents to change the status of a ticket.  
        \*   \*\*Function:\*\* Dropdown or buttons to change status (e.g., from "Open" to "In Progress," or to "Closed").  
    \*   \*\*2.3.4. Add Internal Notes/Comments to Ticket:\*\*  
        \*   \*\*Feature:\*\* Allow admins/agents to add internal notes or comments to a ticket for tracking or collaboration.  
        \*   \*\*Function:\*\* Text area to add comments, timestamped and associated with the agent. These are not visible to the end-user submitting the ticket.  
    \*   \*\*2.3.5. Respond to User (via Email \- Manual Process for MVP):\*\*  
        \*   \*\*Feature:\*\* Facilitate responding to the end-user.  
        \*   \*\*Function:\*\* The MVP will display the user's email address. The agent will manually compose and send an email response using their standard email client.  
        \*   \*\*Note:\*\* Direct in-app replies sending emails are a V1.1+ feature. For MVP, the focus is on tracking the ticket; communication is external but can be logged via internal notes.

\*\*2.4. Email Notifications (Admin/Agent \- Basic)\*\*  
    \*   \*\*2.4.1. New Ticket Notification:\*\*  
        \*   \*\*Feature:\*\* Notify designated admin/agent email(s) when a new ticket is submitted.  
        \*   \*\*Function:\*\* Send an automated email to a pre-configured support email address when a new ticket arrives, including basic ticket details or a link to view it in the admin interface.  
   

**3\. Technical Approach (Proposed \- High Level)**

* **3.1. Frontend (User Submission Form & Admin Interface):**  
  * Technology: HTML, CSS, JavaScript. A simple framework like Svelte, Vue.js, or even static HTML with server-side rendering for the submission form could be used. React for the admin interface if more interactivity is desired.  
  * Styling: Basic, clean CSS.  
* **3.2. Backend (API & Logic):**  
  * Technology: Node.js with Express.js, Python with Flask/FastAPI, or a serverless solution (e.g., Netlify Functions, AWS Lambda, Firebase Functions).  
  * API Design: Simple RESTful API endpoints for ticket submission and admin management.  
* **3.3. Database (Data Storage):**  
  * Technology:  
    * Relational: PostgreSQL, MySQL, SQLite (for extreme simplicity if low volume).  
    * NoSQL: Firebase Firestore, MongoDB (if schema flexibility is highly desired).  
  * Key Data Models:  
    * Admins/Agents: id, username, password\_hash  
    * Tickets: id, submitter\_name, submitter\_email, subject, description, status, created\_at, updated\_at, (optional: attachment\_url)  
    * TicketComments (Internal): id, ticket\_id, agent\_id, comment\_text, created\_at  
* **3.4. Deployment:**  
  * Frontend: Netlify, Vercel, GitHub Pages (for static parts).  
  * Backend: Heroku, Render.com, or serverless platform corresponding to the chosen backend tech.

**4\. Key Assumptions**

* The initial version is for a single administrator/support agent or a small team where complex agent assignment isn't needed.  
* End-users do not need to create accounts to submit tickets. Communication back to them will primarily be via email.  
* The primary goal is a functional system for ticket logging and basic status management.  
* Styling will be clean and functional, not requiring elaborate design.

**5\. Exclusions (Out of Scope for MVP)**

* End-user accounts or portals to track their own ticket status online.  
* Advanced agent assignment, ticket routing, or escalation rules.  
* Service Level Agreement (SLA) tracking.  
* Rich text editing for ticket descriptions or comments.  
* Direct in-app email replies from the admin interface (responses are manual via email).  
* Knowledge base integration.  
* Detailed reporting and analytics beyond basic list views.  
* Multi-language support.  
* Complex file attachment management (multiple files, large sizes, virus scanning).  
* Third-party integrations (e.g., CRM, Slack).

**6\. Acceptance Criteria (High-Level Examples)**

* **AC1 (Ticket Submission):** An end-user can successfully fill out and submit the support ticket form and receive an on-screen confirmation with a Ticket ID.  
* **AC2 (User Email Confirmation):** Upon ticket submission, the end-user receives an email confirming their ticket was received, including the Ticket ID.  
* **AC3 (Admin Notification):** A designated admin email address receives a notification when a new ticket is submitted.  
* **AC4 (Admin Ticket Viewing):** A logged-in admin can view a list of submitted tickets, including their status and submitter details.  
* **AC5 (Admin Ticket Management):** A logged-in admin can open a ticket, view its details, add an internal comment, and change its status (e.g., to "Closed").

**7\. Estimated Timeline & Phases (Illustrative)**

* **Phase 1 (Core Functionality \- 3-5 Weeks):**  
  * Setup project, database schema.  
  * Admin login.  
  * Public ticket submission form.  
  * Backend logic for saving tickets.  
  * Basic email notifications (user confirmation, admin new ticket).  
  * Admin ticket listing page.  
  * Admin ticket detail view.  
  * Admin ability to update status and add internal comments.  
* **Phase 2 (Refinement & Testing \- 1-2 Weeks):**  
  * UI/UX improvements for clarity and ease of use.  
  * Basic filtering/sorting on admin ticket list.  
  * Testing across common browsers.  
  * Bug fixing and deployment preparation.

---

This SOW describes a very lean MVP for a support ticket system. It focuses on the absolute essentials to get a functional system up and running. Features can be added iteratively in subsequent versions based on feedback and needs.

---

