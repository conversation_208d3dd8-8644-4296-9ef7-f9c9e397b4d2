import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

// Enums for message management following TTH patterns
export enum MessageType {
  USER = "USER",
  AGENT = "AGENT",
  SYSTEM = "SYSTEM",
  INTERNAL_NOTE = "INTERNAL_NOTE",
}

interface TicketMessageAttributes {
  id?: number;
  ticket_id: number;
  message_text: string;
  message_type: MessageType;
  is_private: boolean;
  attachment_id?: number;
  created_by: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

export default class TicketMessage
  extends Model<TicketMessageAttributes>
  implements TicketMessageAttributes
{
  public id!: number;
  public ticket_id!: number;
  public message_text!: string;
  public message_type!: MessageType;
  public is_private!: boolean;
  public attachment_id?: number;
  public created_by!: number;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;
  public readonly deleted_at?: Date;

  // Instance methods
  public isInternal(): boolean {
    return this.message_type === MessageType.INTERNAL_NOTE;
  }

  public isFromAgent(): boolean {
    return this.message_type === MessageType.AGENT;
  }

  public isFromUser(): boolean {
    return this.message_type === MessageType.USER;
  }

  public hasAttachment(): boolean {
    return this.attachment_id !== null && this.attachment_id !== undefined;
  }

  static associate(models: any) {
    // TicketMessage belongs to Ticket
    TicketMessage.belongsTo(models.Ticket, {
      foreignKey: "ticket_id",
      as: "ticket",
    });

    // Soft reference to nv_users table (no Sequelize association):
    // - created_by references nv_users.id (message creator)
    // User data will be fetched via raw queries when needed

    // TicketMessage belongs to nv_items table (attachment) - proper association
    TicketMessage.belongsTo(models.Item, {
      foreignKey: "attachment_id",
      as: "attachment",
    });
  }
}

// Initialize the model
TicketMessage.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    ticket_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_support_tickets",
        key: "id",
      },
      comment: "Reference to the support ticket",
    },
    message_text: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: "Message content",
    },
    message_type: {
      type: DataTypes.ENUM("USER", "AGENT", "SYSTEM", "INTERNAL_NOTE"),
      allowNull: false,
      defaultValue: "USER",
      comment: "Type of message",
    },
    is_private: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: "Message visibility",
    },
    attachment_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_items",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
      comment: "File uploaded via nv_items",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "nv_users",
        key: "id",
      },
      comment: "ID of user who created the message",
    },
  },
  {
    sequelize,
    tableName: "mo_support_ticket_messages",
    modelName: "TicketMessage",
    timestamps: true,
    paranoid: true,
    underscored: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    deletedAt: "deleted_at",
    indexes: [
      {
        fields: ["ticket_id"],
      },
      {
        fields: ["message_type"],
      },
      {
        fields: ["created_by"],
      },
      {
        fields: ["is_private"],
      },
      {
        fields: ["attachment_id"],
      },
      {
        fields: ["created_at"],
      },
    ],
  }
);
