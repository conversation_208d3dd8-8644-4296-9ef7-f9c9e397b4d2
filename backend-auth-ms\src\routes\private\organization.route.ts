import { Router } from "express";
import orgController from "../../controller/organization.controller";
import { multerS3 } from "../../helper/upload.helper";
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.ORGANIZATION_LOGO.folder,
);

const router = Router();

/** get Organization By Id */
/**
 * @swagger
 * /v1/private/organization/get-org/{orgId}:
 *   get:
 *     summary: Get organization details by ID
 *     description: Fetches the details of an organization by its unique ID. Requires a JWT token.
 *     tags:
 *       - Organization
 *     security:
 *       - BearerAuth: []  # JWT token authorization
 *     parameters:
 *       - name: orgId
 *         in: path
 *         required: true
 *         description: The unique identifier of the organization.
 *         schema:
 *           type: string
 *           example: cd6ce8bb-73cf-45b8-9ae8-5403c2b83b45
 *     responses:
 *       200:
 *         description: Organization details successfully fetched.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: The unique identifier of the organization.
 *                   example: cd6ce8bb-73cf-45b8-9ae8-5403c2b83b45
 *                 name:
 *                   type: string
 *                   description: The name of the organization.
 *                   example: My Organization
 *                 description:
 *                   type: string
 *                   description: A brief description of the organization.
 *                   example: A company focused on innovation.
 *       400:
 *         description: Bad request, invalid organization ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid organization ID.
 *       404:
 *         description: Organization not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Organization not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Something went wrong.
 */
router.get("/get-org/:orgId", orgController.getOrganizationById);

/** update organization by id */
/**
 * @swagger
 * /v1/private/organization/update-organization:
 *   put:
 *     summary: Update user data
 *     description: This API accepts form data to upload user details.
 *     tags:
 *       - Auth
 *     security:
 *       - BearerAuth: [] # JWT token authorization
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 description: Unique identifier for the organization.
 *                 example: "cd6ce8bb-73cf-45b8-9ae8-5403c2b83b45"
 *               name:
 *                 type: string
 *                 description: The name of the organization.
 *                 example: "John"
 *               description:
 *                 type: string
 *                 description: The description of the organization.
 *                 example: "this is my organization data"
 *               multiple_location:
 *                 type: string
 *                 description: The multiple location of the organization.
 *                 example: 2
 *               email:
 *                 type: string
 *                 description: The email address of the user.
 *                 example: "<EMAIL>"
 *               contact_person:
 *                 type: string
 *                 description: The contact person of organization.
 *                 example: ************
 *               website:
 *                 type: string
 *                 description: The domain of organization.
 *                 example: "jnext.com"
 *               address:
 *                 type: string
 *                 description: The address of organization.
 *                 example: "India"
 *               vat_number:
 *                 type: string
 *                 description: The vat number of organization.
 *                 example: "418597663"
 *               country:
 *                 type: string
 *                 description: The country of organization.
 *                 example: 3
 *               currency:
 *                 type: string
 *                 description: The currency of organization.
 *                 example: "INR"
 *               timezone:
 *                 type: string
 *                 description:  The timezone of organization.
 *                 example: "IST"
 *               facebook_url:
 *                 type: string
 *                 description: The facebook_url of organization.
 *                 example: "facebook.com"
 *               linkdin_url:
 *                 type: string
 *                 description: The linkdin_url of organization.
 *                 example: "linkdin.com"
 *               twitter_url:
 *                 type: string
 *                 description: The twitter_url of organization.
 *                 example: "twitter.com"
 *               organization_logo:
 *                 type: string
 *                 format: binary
 *                 description: The organization's logo file.
 *               created_at:
 *                 type: string
 *                 description: The register date of organization.
 *                 example: 2024-12-25 09:34:39
 *               status:
 *                 type: string
 *                 description: The status of organization.
 *                 example: active
 *     responses:
 *       200:
 *         description: Organization data updated successfully.
 *       400:
 *         description: Bad request.
 */
router.put("/update-organization", multerS3Upload.upload("organization_logo"), orgController.updateOrganization);

router.put("/close-organization-account/:organizationId", orgController.closeOrganization);

export default router;