'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    await queryInterface.bulkInsert('mo_support_ticket_messages', [
      // Messages for Ticket 1 (ORG-001-TKT-001)
      {
        ticket_id: 1,
        message_text: 'Thank you for contacting support. We have received your report about the export issue and are looking into it.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 2,
        created_at: twoDaysAgo,
        updated_at: twoDaysAgo
      },
      {
        ticket_id: 1,
        message_text: 'Internal note: This seems to be related to the recent update. Need to check with the dev team.',
        message_type: 'INTERNAL_NOTE',
        is_private: true,
        created_by: 2,
        created_at: twoDaysAgo,
        updated_at: twoDaysAgo
      },
      {
        ticket_id: 1,
        message_text: 'Hi, I tried again this morning but still getting the same error. The error message says "Export failed: timeout error".',
        message_type: 'USER',
        is_private: false,
        created_by: 0,
        created_at: yesterday,
        updated_at: yesterday
      },

      // Messages for Ticket 2 (ORG-001-TKT-002)
      {
        ticket_id: 2,
        message_text: 'Thank you for the feature request! Dark mode is indeed a popular request. I\'ve assigned this to our development team for evaluation.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 2,
        created_at: yesterday,
        updated_at: yesterday
      },
      {
        ticket_id: 2,
        message_text: 'That sounds great! Is there an estimated timeline for when this might be available?',
        message_type: 'USER',
        is_private: false,
        created_by: 0,
        created_at: yesterday,
        updated_at: yesterday
      },

      // Messages for Ticket 3 (ORG-002-TKT-001) - Critical bug
      {
        ticket_id: 3,
        message_text: 'This is a critical issue. I\'m escalating this to our senior developer immediately.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 3,
        created_at: yesterday,
        updated_at: yesterday
      },
      {
        ticket_id: 3,
        message_text: 'URGENT: Critical bug affecting project creation. User reports system crashes. Needs immediate attention.',
        message_type: 'INTERNAL_NOTE',
        is_private: true,
        created_by: 3,
        created_at: yesterday,
        updated_at: yesterday
      },
      {
        ticket_id: 3,
        message_text: 'We\'ve identified the issue and deployed a hotfix. Can you please try creating a project now?',
        message_type: 'AGENT',
        is_private: false,
        created_by: 3,
        created_at: oneHourAgo,
        updated_at: oneHourAgo
      },
      {
        ticket_id: 3,
        message_text: 'Yes! It\'s working now. Thank you for the quick fix!',
        message_type: 'USER',
        is_private: false,
        created_by: 0,
        created_at: now,
        updated_at: now
      },

      // Messages for Ticket 4 (ORG-002-TKT-002) - Resolved ticket
      {
        ticket_id: 4,
        message_text: 'Hi Sarah! I\'d be happy to help you set up automated reports. Let me walk you through the process step by step.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 2,
        created_at: twoDaysAgo,
        updated_at: twoDaysAgo
      },
      {
        ticket_id: 4,
        message_text: 'First, go to Reports > Automation > Create New Schedule. Then select your report type and frequency.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 2,
        created_at: twoDaysAgo,
        updated_at: twoDaysAgo
      },
      {
        ticket_id: 4,
        message_text: 'Perfect! I followed your instructions and it worked. The automated report is now set up. Thank you so much!',
        message_type: 'USER',
        is_private: false,
        created_by: 0,
        created_at: yesterday,
        updated_at: yesterday
      },
      {
        ticket_id: 4,
        message_text: 'Great! I\'m glad I could help. If you need any further assistance, please don\'t hesitate to reach out.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 2,
        created_at: yesterday,
        updated_at: yesterday
      },

      // Messages for Ticket 5 (ORG-003-TKT-001)
      {
        ticket_id: 5,
        message_text: 'Thank you for your inquiry about API integration. Let me gather some information about your specific requirements.',
        message_type: 'AGENT',
        is_private: false,
        created_by: 3,
        created_at: yesterday,
        updated_at: yesterday
      },
      {
        ticket_id: 5,
        message_text: 'We need to integrate with ADP payroll system. Do you have documentation for this specific integration?',
        message_type: 'USER',
        is_private: false,
        created_by: 0,
        created_at: yesterday,
        updated_at: yesterday
      },
      {
        ticket_id: 5,
        message_text: 'Need to check if we have ADP integration documentation. Will follow up with the integration team.',
        message_type: 'INTERNAL_NOTE',
        is_private: true,
        created_by: 3,
        created_at: yesterday,
        updated_at: yesterday
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('mo_support_ticket_messages', null, {});
  }
};
