import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface RecipeSettingsAttributes {
  id?: number;
  key: string;
  value?: string;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeSettings
  extends Model<RecipeSettingsAttributes, never>
  implements RecipeSettingsAttributes
{
  id!: number;
  key!: string;
  value?: string;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeSettings.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    key: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    value: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_setting",
    modelName: "RecipeSettings",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["key", "organization_id"],
        name: "unique_setting_key_per_org",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_settings_organization",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_settings_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_settings_updated_by",
      },
    ],
  }
);

// Define associations
RecipeSettings.associate = (models: any) => {
  // RecipeSettings belongs to User (created_by)
  RecipeSettings.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeSettings belongs to User (updated_by)
  RecipeSettings.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });
};

export default RecipeSettings;
