export const EMAILCONSTANT = Object.freeze({
  FORGOT_PASSWORD: { subject: "Forgot password" },
  RESEND_OTP: { subject: "Resend OTP" },
  FORGOT_PIN_OTP: { subject: "Forgot pin." },
  USER_VERIFICATION: { subject: "User Verification" },
});

export const TRIAL_PERIOD = Object.freeze({
  DAYS: 180
});

export const RABBITMQ_QUEUE = Object.freeze({
  TRIAL_PLAN: 'trial_plan',
  FORGOT_PASSWORD_MAIL_OTP: 'forgot_password_otp',
  FORGOT_PASSWORD_PIN_OTP: 'forgot_password_pin_otp',
  RESET_PASSWORD_MAIL_OTP: 'reset_password_otp',
  NEW_USER_VERIFICATION: 'new_user_verification',
  STAFF_CREATION: 'staff_creation',
  MAIL_FAILED: 'mail_failed',
  MAIL_SUCCESS: 'mail_success',
  STAFF_CREATION_DETAILS: 'staff_creation_details',
  STAFF_CREATION_SUCCESS: 'staff_creation_success',
  ORG_MASTER_USER: 'org_master_user',
  ORG_MASTER_USER_VERIFICATION_SUCCESS: 'org_master_user_verification_success',
  SUBSCRIPTION_EXPIRY: 'subscription_expiry',
  ORGANIZATION_ACCOUNT_UPDATE_MAIL: 'organization_account_update_mail',
  STAFF_PASSWORD_PIN_GENERATE: 'staff_password_pin_generate',
  SESSION_STORE: 'session_store',
  STORE_STAFF_DATA_FOR_PURCHASED_PLAN: 'store_staff_data_for_purchased_plan',
  USER_ACTIVITY_LOG: 'user_activity_log'
})

export const EMAIL_ADDRESS = Object.freeze({
  ADDRESS: "Suite-1 Francis House, Queens Road, Norwich, England, NR1 3PN",
  PHONE_NUMBER: "+44 **********",
  EMAIL: "<EMAIL>",
  ORGANIZATION_NAME: "Microffice",
});

export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  BRANCH_MANAGER: "Branch Manager",
  ASSIGN_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCHEN: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  SIGNATURE: "Signature"
});

export const ADMIN_SIDE_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.DIRECTOR,
  ROLE_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE
];

export const NORMAL_USER = [
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
];

export const FILE_UPLOAD_CONSTANT = Object.freeze({
  USER_SIGNATURE_PATH: {
    folder: "user_images",
    destinationPath: (orgName: string, userId: any, fileName: string) =>
      `${orgName}/users/${userId}/signatures/${fileName}`,
  },
  ORGANIZATION_LOGO: {
    folder: "organization_images",
    destinationPath: (orgName: string, fileName: string) =>
      `${orgName}/organization_logo/${fileName}`,
  }
});
