import {
  S3<PERSON>lient,
  PutObjectCommand,
  HeadObjectCommand,
  HeadBucketCommand,
  CreateBucketCommand,
  CopyObjectCommand,
  DeleteObjectCommand,
  GetObjectCommand
} from "@aws-sdk/client-s3";
import { getHash, ReadingFile } from "./common";
import {
  Item,
  item_external_location,
  item_IEC,
  item_status,
  item_type,
} from "../models/Item";

// Setup MinIO client
export const s3 = new S3Client({
  endpoint: global.config.MINIO_ENDPOINT,
  region: "us-east-1",
  forcePathStyle: true,
  credentials: {
    accessKeyId: global.config.MINIO_ACCESS_KEY,
    secretAccessKey: global.config.MINIO_SECRET_KEY,
  },
});
import multer from "multer";
import path from "path";
import fs from "fs";

/**
 * S3 upload service with hash verification to prevent duplicates
 * @param bucketName - The S3 bucket name
 * @param folderPath - Optional folder path within the bucket
 * @returns multer instance configured for S3 storage
 */
export const multerS3 = (bucketName: string, folderPath: string = "") => {
  try {
    const storage = multer.diskStorage({
      destination: (req: any, file: any, cb: any) => {
        const uploadPath = path.resolve(__dirname, "../uploads/", folderPath);
        try {
          if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
          }
        } catch (error) {
          console.error("Error creating upload directory:", error);
        }
        cb(null, uploadPath);
      },
      filename: (req: any, file: any, cb: any) => {
        cb(null, `${Date.now()}-${file.originalname}`);
      },
    });
    const upload = multer({
      storage
    });

    createBucketIfNotExists(bucketName).then().catch();

    // Custom middleware for single field with single files
    const s3UploadMiddleware = (fieldName: string, maxCount: number = 1) => {
      const multerMiddleware = upload.array(fieldName, maxCount);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files || req.files?.length === 0) {
            return next();
          }

          const uploadedFiles: any[] = [];

          try {
            for (const file of req.files) {
              const originalFile: any = await ReadingFile(file.path);
              // Generate file hash to check for duplicates
              const fileHash: any = await getHash(originalFile.data, file);
              const fileName = file.filename;
              const filePath = folderPath
                ? `${folderPath}/${fileName}`
                : fileName;
              const fileBuffer = fs.readFileSync(file.path)

              const organizationId = req.body.organizationId || req.user?.organization_id
              // Check if file already exists in the bucket
              let fileExists = false;
              let fileExistAtLocation = false;
              try {
                if (fileHash.status) {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash?.hash,
                      item_organization_id: organizationId,
                    },
                  });
                  if (getItem && getItem.id) {
                    fileExists = true;
                    const checkS3FileExist = await s3.send(
                      new GetObjectCommand({
                        Bucket: bucketName,
                        Key: getItem?.item_location,
                      })
                    );
                    if (checkS3FileExist && checkS3FileExist?.Body) {
                      fileExistAtLocation = true;
                    }
                  }
                }
              } catch (error) {
                // File doesn't exist, continue with upload
              }

              if (!fileExists) {
                // Upload file to S3
                await s3.send(
                  new PutObjectCommand({
                    Bucket: bucketName,
                    Key: filePath,
                    Body: fileBuffer,
                    ContentType: file.mimetype,
                  })
                );

                const saveItem: any = {
                  item_type:
                    file.mimetype == "multipart/form-data"
                      ? item_type.VIDEO
                      : file.mimetype == "application/octet-stream"
                        ? item_type.VIDEO
                        : file.mimetype.split("/")[0] == "application"
                          ? "pdf"
                          : file.mimetype.split("/")[0],
                  item_name: file.filename,
                  item_hash: fileHash?.hash,
                  item_mime_type: file.mimetype,
                  item_extension: path.extname(file.originalname),
                  item_size: file.size,
                  item_IEC: item_IEC.B,
                  item_status: item_status.ACTIVE,
                  item_external_location: item_external_location.NO,
                  item_location: filePath,
                  item_organization_id: organizationId,
                };

                const item = await Item.create(saveItem);

                // Add file info to the request
                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash?.hash,
                  bucket: bucketName,
                  item_id: item.id,
                  type: item.item_type,
                  isMovable: true,
                });

                fs.unlinkSync(file.path);
              } else {
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash?.hash,
                    item_organization_id: organizationId,
                  },
                });
                if (!fileExistAtLocation) {
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );
                  await Item.update(
                    {
                      item_location: filePath,
                      item_status: item_status?.ACTIVE,
                    },
                    {
                      where: {
                        id: getItem?.id,
                      },
                    }
                  );

                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash?.hash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: true,
                  });
                } else {
                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: getItem?.item_location,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash?.hash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: false,
                  });
                }
                fs.unlinkSync(file.path);
              }
            }

            // Replace the files array with our processed files
            req.files = uploadedFiles;
            return next();
          } catch (error) {
            console.error("Error uploading to S3:", error);
            return next(error);
          }
        });
      };
    };

    // Custom middleware for single field with multiple files
    const s3UploadArrayMiddleware = (
      fieldName: string,
      maxCount: number = 10
    ) => {
      const multerMiddleware = upload.array(fieldName, maxCount);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files || req.files?.length === 0) {
            return next();
          }

          const uploadedFiles: any[] = [];
          const organizationId = req.body.organizationId || req.user?.organization_id
          try {
            for (const file of req.files) {
              const originalFile: any = await ReadingFile(file.path);
              // Generate file hash to check for duplicates
              const fileHash: any = await getHash(originalFile.data, file);
              const fileName = file.filename;
              const filePath = folderPath
                ? `${folderPath}/${fileName}`
                : fileName;
              const fileBuffer = fs.readFileSync(file.path)

              let fileExists = false;
              let fileExistAtLocation = false;
              try {
                if (fileHash.status) {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash?.hash,
                      item_organization_id: organizationId,
                    },
                  });
                  if (getItem && getItem.id) {
                    fileExists = true;
                    const checkS3FileExist = await s3.send(
                      new GetObjectCommand({
                        Bucket: bucketName,
                        Key: getItem?.item_location,
                      })
                    );
                    if (checkS3FileExist && checkS3FileExist?.Body) {
                      fileExistAtLocation = true;
                    }
                  }
                }
              } catch (error) {
                // File doesn't exist, continue with upload
              }

              if (!fileExists) {
                // Upload file to S3
                await s3.send(
                  new PutObjectCommand({
                    Bucket: bucketName,
                    Key: filePath,
                    Body: fileBuffer,
                    ContentType: file.mimetype,
                  })
                );

                const saveItem: any = {
                  item_type:
                    file.mimetype == "multipart/form-data"
                      ? item_type.VIDEO
                      : file.mimetype == "application/octet-stream"
                        ? item_type.VIDEO
                        : file.mimetype.split("/")[0] == "application"
                          ? "pdf"
                          : file.mimetype.split("/")[0],
                  item_name: file.filename,
                  item_hash: fileHash?.hash,
                  item_mime_type: file.mimetype,
                  item_extension: path.extname(file.originalname),
                  item_size: file.size,
                  item_IEC: item_IEC.B,
                  item_status: item_status.ACTIVE,
                  item_external_location: item_external_location.NO,
                  item_location: filePath,
                  item_organization_id: organizationId,
                };

                const item = await Item.create(saveItem);

                // Add file info to the request
                uploadedFiles.push({
                  originalname: file.originalname,
                  filename: fileName,
                  path: filePath,
                  size: file.size,
                  mimetype: file.mimetype,
                  hash: fileHash?.hash,
                  bucket: bucketName,
                  item_id: item.id,
                  type: item.item_type,
                  isMovable: true,
                });

                fs.unlinkSync(file.path);
              } else {
                const getItem: any = await Item.findOne({
                  where: {
                    item_hash: fileHash?.hash,
                    item_organization_id: organizationId,
                  },
                });
                if (!fileExistAtLocation) {
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );
                  await Item.update(
                    {
                      item_location: filePath,
                      item_status: item_status?.ACTIVE,
                    },
                    {
                      where: {
                        id: getItem?.id,
                      },
                    }
                  );

                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash?.hash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: true,
                  });
                } else {
                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: getItem?.item_location,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash?.hash,
                    bucket: bucketName,
                    item_id: getItem.id,
                    type: getItem?.item_type,
                    isMovable: false,
                  });
                }

                fs.unlinkSync(file.path);
              }
            }

            // Replace the files array with our processed files
            req.files = uploadedFiles;
            return next();
          } catch (error) {
            console.error("Error uploading to S3:", error);
            return next(error);
          }
        });
      };
    };

    // Custom middleware for multiple fields
    const s3UploadFieldsMiddleware = (
      fields: { name: string; maxCount: number }[]
    ) => {
      const multerMiddleware = upload.fields(fields);

      return async (req: any, res: any, next: any) => {
        multerMiddleware(req, res, async (err: any) => {
          if (err) {
            return next(err);
          }

          if (!req.files) {
            return next();
          }

          try {
            // Process each field separately
            const processedFiles: any = {};
            const organizationId = req.body.organizationId || req.user?.organization_id
            for (const fieldName of Object.keys(req.files)) {
              const fieldFiles = req.files[fieldName];
              const uploadedFiles: any[] = [];

              for (const file of fieldFiles) {
                const originalFile: any = await ReadingFile(file.path);
                // Generate file hash to check for duplicates
                const fileHash: any = await getHash(originalFile.data, file);
                const fileName = file.filename;
                const filePath = folderPath
                ? `${folderPath}/${fileName}`
                : fileName;
                const fileBuffer: any = fs.readFileSync(file.path);

                let fileExists = false;
                let fileExistAtLocation = false;
                try {
                  if (fileHash.status) {
                    const getItem: any = await Item.findOne({
                      where: {
                        item_hash: fileHash?.hash,
                        item_organization_id: organizationId,
                      },
                    });
                    if (getItem && getItem.id) {
                      fileExists = true;
                      const checkS3FileExist = await s3.send(
                        new GetObjectCommand({
                          Bucket: bucketName,
                          Key: getItem?.item_location,
                        })
                      );
                      if (checkS3FileExist && checkS3FileExist?.Body) {
                        fileExistAtLocation = true;
                      }
                    }
                  }
                } catch (error) {
                  // File doesn't exist, continue with upload
                }

                if (!fileExists) {
                  // Upload file to S3
                  await s3.send(
                    new PutObjectCommand({
                      Bucket: bucketName,
                      Key: filePath,
                      Body: fileBuffer,
                      ContentType: file.mimetype,
                    })
                  );

                  const saveItem: any = {
                    item_type:
                      file.mimetype == "multipart/form-data"
                        ? item_type.VIDEO
                        : file.mimetype == "application/octet-stream"
                          ? item_type.VIDEO
                          : file.mimetype.split("/")[0] == "application"
                            ? "pdf"
                            : file.mimetype.split("/")[0],
                    item_name: file.filename,
                    item_hash: fileHash?.hash,
                    item_mime_type: file.mimetype,
                    item_extension: path.extname(file.originalname),
                    item_size: file.size,
                    item_IEC: item_IEC.B,
                    item_status: item_status.ACTIVE,
                    item_external_location: item_external_location.NO,
                    item_location: filePath,
                    item_organization_id: organizationId,
                  };

                  const item = await Item.create(saveItem);

                  // Add file info to the request
                  uploadedFiles.push({
                    originalname: file.originalname,
                    filename: fileName,
                    path: filePath,
                    size: file.size,
                    mimetype: file.mimetype,
                    hash: fileHash?.hash,
                    bucket: bucketName,
                    item_id: item.id,
                    type: item.item_type,
                    isMovable: true,
                  });

                  fs.unlinkSync(file.path);
                } else {
                  const getItem: any = await Item.findOne({
                    where: {
                      item_hash: fileHash?.hash,
                      item_organization_id: organizationId,
                    },
                  });
                  if (!fileExistAtLocation) {
                    await s3.send(
                      new PutObjectCommand({
                        Bucket: bucketName,
                        Key: filePath,
                        Body: fileBuffer,
                        ContentType: file.mimetype,
                      })
                    );
                    await Item.update(
                      {
                        item_location: filePath,
                        item_status: item_status?.ACTIVE,
                      },
                      {
                        where: {
                          id: getItem?.id,
                        },
                      }
                    );

                    uploadedFiles.push({
                      originalname: file.originalname,
                      filename: fileName,
                      path: filePath,
                      size: file.size,
                      mimetype: file.mimetype,
                      hash: fileHash?.hash,
                      bucket: bucketName,
                      item_id: getItem.id,
                      type: getItem?.item_type,
                      isMovable: true,
                    });
                  } else {
                    uploadedFiles.push({
                      originalname: file.originalname,
                      filename: fileName,
                      path: getItem?.item_location,
                      size: file.size,
                      mimetype: file.mimetype,
                      hash: fileHash?.hash,
                      bucket: bucketName,
                      item_id: getItem.id,
                      type: getItem?.item_type,
                      isMovable: false,
                    });
                  }
                  fs.unlinkSync(file.path);
                }
              }

              // Add processed files for this field
              processedFiles[fieldName] = uploadedFiles;
            }

            // Replace the files object with our processed files
            req.files = processedFiles;
            return next();
          } catch (error) {
            console.error("Error uploading to S3:", error);
            return next(error);
          }
        });
      };
    };

    return {
      upload: s3UploadMiddleware,
      fields: s3UploadFieldsMiddleware,
      array: s3UploadArrayMiddleware,
    };
  } catch (error) {
    console.error("S3 upload configuration error:", error);
    // Return dummy middleware functions to prevent TypeScript errors
    const errorMiddleware = (req: any, res: any, next: any) => {
      console.error("S3 upload was not properly configured:", error);
      return next(new Error("S3 upload configuration failed"));
    };

    return {
      upload: (_fieldName: string, _maxCount: number = 1) => errorMiddleware,
      fields: (_fields: { name: string; maxCount: number }[]) =>
        errorMiddleware,
      array: (_fieldName: string, _maxCount: number = 10) => errorMiddleware,
    };
  }
};

const createBucketIfNotExists = async (bucketName: string) => {
  try {
    await s3.send(new HeadBucketCommand({ Bucket: bucketName }));
    // console.log(`Bucket ${bucketName} already exists`);
  } catch (error: any) {
    if (error.name === "NotFound" || error.name === "NoSuchBucket") {
      try {
        await s3.send(new CreateBucketCommand({ Bucket: bucketName }));
        console.log(`Bucket ${bucketName} created successfully`);
      } catch (createError: any) {
        console.error(`Error creating bucket: ${createError.message}`);
      }
    } else {
      console.error(`Error checking bucket: ${error.message}`);
    }
  }
};

/**
 * Move a file to a different path within the same bucket
 * @param bucketName - The bucket name
 * @param sourceKey - Current file path/key
 * @param destinationKey - New file path/key
 * @param deleteSource - Whether to delete the source file after copying (default: true)
 * @returns Object containing information about the move operation
 */
export const moveFileInBucket = async (
  bucketName: string,
  sourceKey: string,
  destinationKey: string,
  item_id: number,
  deleteSource: boolean = true,
  organization_id: string
): Promise<{
  success: boolean;
  sourceUrl?: string;
  destinationUrl?: string;
  error?: any;
}> => {
  try {
    // Ensure bucket exists
    await createBucketIfNotExists(bucketName);

    // Check if source file exists
    try {
      await s3.send(
        new HeadObjectCommand({
          Bucket: bucketName,
          Key: sourceKey,
        }),
      );
    } catch (error) {
      return {
        success: false,
        error: `Source file does not exist: ${bucketName}/${sourceKey}`,
      };
    }

    await Item.update(
      {
        item_organization_id: organization_id,
        item_location: destinationKey,
      },
      {
        where: {
          id: item_id,
        },
      },
    );
    // Copy the file to the destination path
    await s3.send(
      new CopyObjectCommand({
        CopySource: `${bucketName}/${sourceKey}`,
        Bucket: bucketName,
        Key: destinationKey,
      }),
    );

    // Delete the source file if deleteSource is true
    if (deleteSource) {
      await s3.send(
        new DeleteObjectCommand({
          Bucket: bucketName,
          Key: sourceKey,
        }),
      );
    }

    return {
      success: true,
      sourceUrl: `${sourceKey}`,
      destinationUrl: `${destinationKey}`,
    };
  } catch (error) {
    console.error("Error moving file within bucket:", error);
    return {
      success: false,
      error: error,
    };
  }
};

export const deleteFileFromBucket = async (
  bucketName: string,
  sourceKey: string,
) => {
  try {
    await s3.send(
      new DeleteObjectCommand({
        Bucket: bucketName,
        Key: sourceKey,
      }),
    );
    return {
      success: true,
    };
  } catch (error) {
    console.error("Error delete file from bucket:", error);
    return {
      success: false,
      error: error,
    };
  }
};

export const moveFilesLocalToS3 = async (
  file: any,
  folderPath: string,
  fileName: string,
  organization_id: string,
  bucketName: string = process.env.NODE_ENV!,
) => {
  try {
    const originalFile: any = await ReadingFile(file.path);
    // Generate file hash to check for duplicates
    const fileHash: any = await getHash(originalFile.data, file);
    const fileName = file.originalname;
    const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

    // // Check if file already exists in the bucket
    // let fileExists = false;
    // try {
    //   if (fileHash.status) {
    //     const getItem: any = await Item.findOne({
    //       where: {
    //         item_hash: fileHash?.hash,
    //         item_organization_id: organization_id
    //       },
    //     });
    //     if (getItem && getItem.id) {
    //       fileExists = true;
    //     }
    //   }
    // } catch (error) {
    //   // File doesn't exist, continue with upload
    // }

    // Upload file to S3
    await s3.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: filePath,
        Body: file.buffer,
        ContentType: file.mimetype,
      }),
    );

    const saveItem: any = {
      item_type:
        file.mimetype == "multipart/form-data"
          ? item_type.VIDEO
          : file.mimetype == "application/octet-stream"
            ? item_type.VIDEO
            : file.mimetype.split("/")[0] == "application"
              ? "pdf"
              : file.mimetype.split("/")[0],
      item_name: file.filename,
      item_hash: fileHash?.hash,
      item_mime_type: file.mimetype,
      item_extension: path.extname(file.originalname),
      item_size: file.size,
      item_IEC: item_IEC.B,
      item_status: item_status.ACTIVE,
      item_external_location: item_external_location.NO,
      item_location: filePath,
      item_organization_id: organization_id,
    };

    const item = await Item.create(saveItem);
    return {
      success: true,
      data: item,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      error: error,
    };
  }
};

/**
 * Upload a file buffer directly to S3
 * @param bucketName - S3 bucket name
 * @param key - The file path/key within the bucket
 * @param fileBuffer - The file data buffer
 * @param contentType - The file MIME type
 * @returns Object containing upload status and error if any
 */
export const uploadFileToBucket = async (
  bucketName: string,
  key: string,
  fileBuffer: Buffer,
  contentType: string = "application/octet-stream"
): Promise<{
  success: boolean;
  error?: any;
}> => {
  try {
    // Ensure bucket exists
    await createBucketIfNotExists(bucketName);

    // Upload to S3
    await s3.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType
      })
    );

    return {
      success: true
    };
  } catch (error) {
    console.error("Error uploading file to bucket:", error);
    return {
      success: false,
      error
    };
  }
};
