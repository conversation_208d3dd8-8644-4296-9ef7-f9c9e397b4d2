"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface mailAttributes {
    id: number;
    notify_mail_from: string;
    notify_mail_to: string;
    notify_mail_body: string;
    notify_mail_subject: string;
    notify_mail_status: string;
    notify_mail_response: string;
    notify_mail_attachments: string;
    created_by: number;
    updated_by: number;
}

export enum notify_mail_status {
    SENT = "sent",
    FAILED = "failed",
    PENDING = "pending"
}

export class Mail
    extends Model<mailAttributes, never>
    implements mailAttributes {
    id!: number;
    notify_mail_from!: string;
    notify_mail_to!: string;
    notify_mail_body!: string;
    notify_mail_subject!: string;
    notify_mail_status!: string;
    notify_mail_response!: string;
    notify_mail_attachments!: string;
    created_by!: number;
    updated_by!: number;
}

Mail.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        notify_mail_from: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        notify_mail_to: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        notify_mail_body: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        notify_mail_subject: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        notify_mail_status: {
            type: DataTypes.ENUM,
            values: Object.values(notify_mail_status),
            defaultValue: notify_mail_status.PENDING,
        },
        notify_mail_response: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        notify_mail_attachments: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "mails",
        modelName: "Mail",
        timestamps: true,
    },
);
