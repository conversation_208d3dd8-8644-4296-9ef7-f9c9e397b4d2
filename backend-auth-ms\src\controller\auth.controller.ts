import { StatusCodes } from "http-status-codes";
import { TRIAL_PERIOD, RABBITMQ_QUEUE, EMAIL_ADDRESS, EMAILCONSTANT, ADMIN_SIDE_USER, FILE_UPLOAD_CONSTANT, NORMAL_USER } from "../helper/constant";
import { generateToken, generateVerificationToken } from "../helper/utils";
import { GeneratePassword, generateUniqueUsername, otpAttemptValidation, storeActivityLog } from "../helper/common";
import { createUsers, createOrganization, updateUsers, getData, resetPassword, joinUserToOrg, getRealmsRoles, assignRolesToUser, getUserToken, getUserFromToken, getPredefinedRealmRoles, assignPredefinedRoleToUser, getUserRoles, getNewTokenFromRefreshToken, deleteOrganization } from "../keycloak/common";
import moment from "moment";
import timezoneList from "../helper/timezone.json";
import currencyList from "../helper/currency.json";
import countryList from "../helper/countryList.json";
import authValidator from "../validator/auth.validator";
import rabbitmqPublisher from '../rabbitmq/rabbitmq';
import { forgotPasswordService, resendOTPService } from '../services/auth.services';
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models";
import argon2 from "argon2";
import { Buffer } from 'buffer';
import { moveFileInBucket } from "../helper/upload.helper";
import { Item } from "../models/Item";

/**
 *  Organization User Register
 * @param req
 * @param res
 * @returns
 */

const orgUserRegister = async (req: any, res: any) => {
    try {
        /** Destructure user details from the request body */
        const { user, organization } = req.body;
        let data: any = {}
        let userData: any = {}
        /** check if organization exists */
        if (organization) {
            /** Get domain from organization website */
            organization.domain = new URL(organization.website).hostname;

            /**
             * Note: in keycloak organization name can't allowed space. so added below conditions.
             * check if organization name contains spaces, if yes then join with underscore and stored into keycloak.
             */
            organization.name = organization.name && organization.name.includes(' ') ? organization.name.replace(/\s+/g, '_') : organization.name
            data = await createOrganization(organization)
            if (data.status == false) {
                if (data.statusText == 'Bad Request') {
                    const trimmedMessage = data.message.includes('organization')
                        ? data.message.split('organization')[0] + 'organization'
                        : data.message;
                    return res.status(data.statusCode).json({
                        status: data.status,
                        message: trimmedMessage,
                        data: null
                    });
                } else {

                    return res.status(data.statusCode).json({
                        status: data.status,
                        message: data.message,
                        data: null
                    });
                }
            }
            /** Get Organization Id from the response */
            if (data && user) {
                user.organization_id = data.orgData.id;
                const getReportFilterData = await sequelize.query(
                    `SELECT * FROM nv_leave_type where has_annual_leave = true and organization_id = '${user.organization_id}'`,
                    { type: QueryTypes.SELECT },
                );
                if (getReportFilterData.length === 0) {
                    const insertData = [
                        {
                            name: "Annual leave",
                            status: "active",
                            has_annual_leave: true,
                            leave_deduction_type: "paid",
                            leave_type_color: "#ef1212",
                            created_by: user.id,
                            updated_by: user.id,
                            createdAt: new Date(),
                            updatedAt: new Date(),
                            organization_id: user.organization_id,
                        },
                    ];
                    await sequelize.query("INSERT INTO nv_leave_type (name, status, has_annual_leave, leave_deduction_type, leave_type_color, created_by, updated_by, createdAt, updatedAt, organization_id) VALUES ?", {
                        replacements: [insertData.map(item => [
                            item.name,
                            item.status,
                            item.has_annual_leave,
                            item.leave_deduction_type,
                            item.leave_type_color,
                            item.created_by,
                            item.updated_by,
                            item.createdAt,
                            item.updatedAt,
                            item.organization_id
                        ])],
                    }, { type: QueryTypes.INSERT });
                }

                const insertData = [
                    {
                        contract_name: "Full time",
                        is_default: true,
                        created_by: user.id,
                        updated_by: user.id,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        organization_id: user.organization_id,
                    },
                    {
                        contract_name: "Part time",
                        is_default: true,
                        created_by: user.id,
                        updated_by: user.id,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        organization_id: user.organization_id,
                    },
                    {
                        contract_name: "Flexible",
                        is_default: true,
                        created_by: user.id,
                        updated_by: user.id,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        organization_id: user.organization_id,
                    },
                ];
                await sequelize.query("INSERT INTO nv_contract_name (contract_name, is_default, created_by, updated_by, createdAt, updatedAt, organization_id) VALUES ?", {
                    replacements: [insertData.map(item => [
                        item.contract_name,
                        item.is_default,
                        item.created_by,
                        item.updated_by,
                        item.createdAt,
                        item.updatedAt,
                        item.organization_id
                    ])],
                }, { type: QueryTypes.INSERT });
            }
        }

        if (user) {
            /** create user in keycloak */
            user.userToken = await generateVerificationToken()
            userData = await createUsers(user);
            if (userData.status == false) {
                /** Delete Organization if any error occur at the time of user creation */
                await deleteOrganization(user.organization_id)
                return res.status(userData.statusCode).json({
                    status: userData.status,
                    message: userData.message,
                    data: userData.statusText,
                    field: userData.field
                });
            }

            /** Set password for user */
            if (user.password) {
                var passwordData: any = await resetPassword(userData.data.id, user.password)
            }

            if (passwordData?.status == true) {
                /** Set master role to org user */
                const realmRoles: any = await getRealmsRoles();
                if (realmRoles.status == false) {
                    return res.status(realmRoles.statusCode).json({
                        status: realmRoles.status,
                        message: realmRoles.message,
                        data: realmRoles.statusText
                    });
                }
                /** check if master role exists */
                const masterRole: any = realmRoles.data.find((role: any) => role.name === global.config.KEYCLOAK_MASTER_ROLE &&
                    role.description === global.config.KEYCLOAK_MASTER_ROLE_DESCRIPTION);

                /** if master role not found */
                if (!masterRole) {
                    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                        status: false,
                        message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                        data: null
                    })
                }
                /** Assign role to user */
                await assignRolesToUser(userData.data.id, [masterRole])

                /** assign default role to all users */
                let getAllRealmRoles: any = await getPredefinedRealmRoles(userData.data.id)

                /** check if master role exists */
                const getRealmRole: any = getAllRealmRoles.data.find((role: any) => role.role === global.config.KEYCLOAK_REALM_ROLE);

                /** if master role not found */
                if (!getRealmRole) {
                    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                        status: false,
                        message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                        data: null
                    });
                }
                /** prepare predefined realm role object */
                let predefinedObject = {
                    id: getRealmRole.id,
                    name: getRealmRole.role,
                    description: getRealmRole.description
                }
                /** Assign predefined realm role to user for access their token. */
                await assignPredefinedRoleToUser(userData.data.id, [predefinedObject], getRealmRole.clientId)

                /**  Join User to Organization */
                await joinUserToOrg(user.organization_id, userData.data.id)
            } else {
                return res.status(passwordData.statusCode).json({
                    status: passwordData.status,
                    message: passwordData.message,
                    data: passwordData.statusText
                });
            }

            /** Handle email configuration if user_email exists */
            if (user.email) {
                let user_auth_token = userData.data?.attributes?.userToken;

                /** Prepare message for queue */
                const message = {
                    name: `${userData.data.firstName} ${userData.data.lastName}`,
                    email: user.email,
                    organization_name: organization.name,
                    URL: global.config.WEB_BASE_URL + `/org/login?token=${user_auth_token}&data=${userData.data.id}`,
                    LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
                    ADDRESS: EMAIL_ADDRESS.ADDRESS,
                    PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                    EMAIL: EMAIL_ADDRESS.EMAIL,
                    ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                    username: user.username,
                    smtpConfig: 'AUTH'
                };

                /** Publish a message to the "New User verification" queue */
                const queue: any = RABBITMQ_QUEUE.NEW_USER_VERIFICATION;
                await rabbitmqPublisher.publishMessage(queue, message);
            }

            /*** store org master details in mysql database */
            /** Prepare message for org master  */
            const message = {
                firstName: `${userData.data.firstName}`,
                lastName: `${userData.data.lastName}`,
                email: user.email,
                keycloak_auth_id: userData.data.id,
                organization_id: user.organization_id,
                phoneNumber: user.userPhoneNumber,
                userPassword: user.password,
                username: userData.data.username,
                user_designation: 'Super admin'
            };

            /** Publish a message to the "org_master_user" queue */
            const queue: any = RABBITMQ_QUEUE.ORG_MASTER_USER;
            await rabbitmqPublisher.publishMessage(queue, message);
        }

        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.CREATED).json({
                status: true,
                message: res.__("SUCCESS_VERIFICATION_MAIL_SENT"),
                data: userData.data,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}


/** Verify auth token API
 * @param req
 * @param res
 * @returns
 */
const verifyAuthToken = async (req: any, res: any) => {
    try {
        let { userId } = req.body;

        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
        let userData: any = await getData(keycloakRealmUrl);

        if (userData.status == false) {
            return res.status(userData.statusCode).json({
                status: userData.status,
                message: res.__("ERROR_USER_NOT_FOUND"),
                data: userData.statusText
            });
        }
        userData = userData?.data
        /** check user is already verified or not */
        if (userData.attributes.userStatus == 'active') {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("ERROR_VERIFICATION_COMPLETED"),
                data: null,
            });
        }
        /** Prepare User Update object */
        let user: any = {
            username: userData.username,
            userId: userId,
            firstName: userData.firstName,
            lastName: userData.lastName,
            email: userData.email,
            emailVerified: true,
            attributes: {
                userCountryCode: userData.attributes?.userCountryCode || "\"\"",
                userPhoneNumber: userData.attributes?.userPhoneNumber || "\"\"",
                organizationId: userData.attributes?.organizationId || "\"\"",
                userStatus: "active",
                isLoginpin: userData.attributes?.isLoginpin || "0",
                userToken: "\"\"",
                updatedBy: userId, // Updated by current user
                otp: "",
                otpExpireTime: "",
                userSignature: "\"\""
            },
        }
        /** Update User auth token and status */
        let updateUserData: any = await updateUsers(user);
        if (updateUserData.status == false) {
            return res.status(updateUserData.statusCode).json({
                status: updateUserData.status,
                message: updateUserData.message,
                data: updateUserData.statusText
            });
        }

        /** Assigned trial period */
        const message = {
            keycloak_userId: userId,
            trialDays: TRIAL_PERIOD.DAYS,  // 180 days trial period
            isFreeTrial: 1,
            firstName: userData.firstName,
            email: userData.email,
            platformType: req.headers["platform-type"] ? req.headers["platform-type"] : 'web',
            organization_id: userData.attributes?.organizationId,
            smtpConfig: 'SUPPORT'
        };

        // Publish a message to the "trial-plan" queue
        const queue: any = RABBITMQ_QUEUE.TRIAL_PLAN;
        await rabbitmqPublisher.publishMessage(queue, message);

        /** Publish a message to the user verification queue. */
        const verificationMessage = {
            status: 'verified',
            keycloak_auth_id: userId,
        };

        // Publish a message to the "user verification" queue for update status
        const verificationQueue: any = RABBITMQ_QUEUE.ORG_MASTER_USER_VERIFICATION_SUCCESS;
        await rabbitmqPublisher.publishMessage(verificationQueue, verificationMessage);

        // Publish a message to the "user_activity_log" queue for user activity
        const activityLog = {
            message: message,
            activity_table: 'Subscription Purchased',
            activity_action: 'created',
        };
        await storeActivityLog(activityLog, req.headers)

        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_USER_VERIFIED"),
                data: null,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/**
 * Get User By keycloak UserId
 * @param req
 * @param res 
 * @returns 
 */
const getUserDataById = async (req: any, res: any) => {
    try {
        let { userId } = req.params;
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
        let userData: any = await getData(keycloakRealmUrl);
        if (userData.status == false) {
            return res.status(userData.statusCode).json({
                status: userData.status,
                message: userData.message,
                data: userData.statusText
            });
        }
        userData = userData?.data
        if (userData.attributes.userSignature && userData.attributes.userSignature.trim() !== "" && userData.attributes.userSignature !== "\"\"") {
            if (!isNaN(userData.attributes.userSignature)) {
                const getItem = await Item.findOne({
                    where: { id: Number(userData.attributes.userSignature) }
                });
                if (getItem) {
                    userData.attributes.userSignature = global.config.API_BASE_URL + getItem?.item_location
                }
            } else {
                userData.attributes.userSignature = global.config.API_BASE_URL + 'user_images/' + userData.attributes.userSignature
            }
        }
        const selectQuery = `SELECT u.id,u.pin_token_version, u.token_version FROM nv_users AS u WHERE u.keycloak_auth_id = '${userData.id}'`;
        const getUserData: any = await sequelize.query(selectQuery, {
            type: QueryTypes.SELECT,
        });
        userData.id = getUserData[0]?.id || null

        /** Get assigned role by userId */
        let userRoles: any = await getUserRoles(userId);

        /** check if user purchased subscription */
        const selectUserQuery = `SELECT id FROM users AS u WHERE u.keycloak_userId = '${userId}'`;
        const getSubUserData: any = await sequelize.query(selectUserQuery, {
            type: QueryTypes.SELECT,
        });
        userData.purchase_plan = false
        if (getSubUserData.length > 0) {
            const selectSubQuery = `SELECT sp_status FROM subscription_purchased WHERE user_id = '${getSubUserData[0]?.id}' AND sp_status IN ('active','inactive')`;
            const getSubData: any = await sequelize.query(selectSubQuery, {
                type: QueryTypes.SELECT,
            });
            if (getSubData.length > 0) {
                userData.purchase_plan = true
            }
        }

        const selectSubQuery = `SELECT sp.id, sp.sp_limit_max, splan.subs_plan_name 
        FROM subscription_purchased AS sp
        JOIN users AS u ON u.id = sp.user_id
        JOIN subscription_plan AS splan ON splan.id = sp.plan_id
        WHERE sp.sp_status IN ('active','inactive') AND u.organization_id='${userData.attributes.organizationId}' AND splan.subs_plan_category = 'core'
        ORDER BY sp.id DESC;`
        let getSubscriptionData: any = await sequelize.query(selectSubQuery, {
            type: QueryTypes.SELECT,
        });
        console.log("getSubscriptionData", getSubscriptionData);
        if (getSubscriptionData.length > 0) {
            getSubscriptionData = getSubscriptionData[0]
            console.log("getSubscriptionData>>>>>>>", getSubscriptionData);
            let staffCount = getSubscriptionData.sp_limit_max;
            console.log("staffCount", staffCount);
            const staffQuery = `
                    SELECT id 
                    FROM nv_users 
                    WHERE organization_id = '${userData.attributes.organizationId}' 
                    AND web_user_active_role_id != 1`;
            console.log("staffQuery", staffQuery);
            const getUserData: any = await sequelize.query(staffQuery, {
                type: QueryTypes.SELECT,
            });
            console.log("getUserData", getUserData);
            if (getUserData.length > 0) {
                console.log("getUserData length", getUserData.length);

                staffCount -= getUserData.length;
            }
            console.log("staffCount", staffCount);
            userData.remaining_emp = staffCount ? staffCount : 0;
        }

        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_DATA_FETCHED"),
                data: { user: userData, roles: userRoles.data }
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/**
 * Login API
 * @param req
 * @param res 
 * @returns 
 */
const login = async (req: any, res: any) => {
    try {
        let { username, password } = req.body

        /** check if user exist or not, throw error  */
        const selectQuery = `SELECT ID, EMAIL FROM USER_ENTITY WHERE USERNAME = '${username}'`;
        const checkUserExist: any = await sequelize.query(selectQuery, {
            type: QueryTypes.SELECT,
        });
        if (checkUserExist.length == 0) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ERROR_USER_NOT_EXIST"),
                data: null
            });
        }

        /** Get User Token */
        let getUserTokenData: any = await getUserToken(username, password)
        console.log("getUserTokenData", getUserTokenData);
        if (getUserTokenData && getUserTokenData.status === false) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: getUserTokenData.status,
                message: res.__("ERROR_INCORRECT_EMAIL_PASSWORD"),
                data: getUserTokenData.statusText
            });
        }
        /** Get user data from token */
        let getUserData: any = await getUserFromToken(getUserTokenData.access_token)
        let userId: any = getUserData?.sub


        /** Get User data from Id */
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
        let getUsersData: any = await getData(keycloakRealmUrl);
        getUsersData = getUsersData.data

        /** Get User assigned Roles */
        let getRoles: any = await getUserRoles(userId)
        getRoles = getRoles?.data?.realmMappings

        /** check user is super admin */
        const checkUserRole: any = getRoles.find((role: any) => role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
            role.description === global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION);
        /** if not then check below condition else not */
        if (!checkUserRole) {
            /** check if master role exists */
            const masterRole: any = getRoles.find((role: any) =>
                (role.name === global.config.KEYCLOAK_MASTER_ROLE || role.name === global.config.KEYCLOAK_STAFF_ROLE) &&
                (role.description === global.config.KEYCLOAK_MASTER_ROLE_DESCRIPTION || role.description === global.config.KEYCLOAK_STAFF_ROLE_DESCRIPTION))
            /** if master role not found */
            if (!masterRole) {
                return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                    status: false,
                    message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                    data: null
                });
            }
            /** check user is organization super admin and it's verification pending then throw error */
            if (masterRole && masterRole.name !== global.config.KEYCLOAK_STAFF_ROLE && getUsersData.attributes.userStatus == 'pending' && getUsersData.emailVerified == false) {

                let subjectPrefix = EMAILCONSTANT.USER_VERIFICATION.subject
                /** add server prefix in email subject to identify email */
                let subject = `${subjectPrefix} ${process.env.NEXT_NODE_ENV === undefined
                    ? "(development)"
                    : process.env.NEXT_NODE_ENV !== "production"
                        ? `(${process.env.NEXT_NODE_ENV})`
                        : ""}`;
                /** Check otp attempt validation condition */
                const attemptValidation = await otpAttemptValidation(getUsersData.email, subject, global.config.FORGOT_PASSWORD_ATTEMPT);
                /** return error based on condition */
                if (attemptValidation !== true) {
                    return res.status(StatusCodes.BAD_REQUEST).json({
                        status: false,
                        message: res.__(attemptValidation),
                        data: null,
                        isVerify: false,
                    });
                }
                // console.log("\n\n getUsersData.attributes", getUsersData.attributes)
                const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${getUsersData.attributes?.organizationld}`;
                let orgData: any = await getData(keycloakRealmUrl);
                orgData = orgData.data

                if (getUsersData.email) {
                    let user_auth_token = getUsersData?.attributes?.userToken;

                    /** Prepare message for queue */
                    const message = {
                        name: `${getUsersData.firstName} ${getUsersData.lastName}`,
                        email: getUsersData.email,
                        organization_name: orgData.name,
                        URL: global.config.WEB_BASE_URL + `/org/login?token=${user_auth_token}&data=${getUsersData.id}`,
                        LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'AUTH'
                    };

                    /** Publish a message to the "New User verification" queue */
                    const queue: any = RABBITMQ_QUEUE.NEW_USER_VERIFICATION;
                    await rabbitmqPublisher.publishMessage(queue, message);
                }
                return res.status(StatusCodes.OK).json({
                    status: false,
                    message: res.__("ERROR_USER_VERIFICATION_PENDING"),
                    data: null,
                    isVerify: false,
                });
            }
        }


        /** Process and construct user_data object */
        if (getUsersData || getUserTokenData) {
            const userObject: any = {
                user_id: getUsersData.id || null,
                email: getUsersData.email || null,
                is_login_pin: Number(getUsersData?.attributes?.isLoginpin) || 0,
                user_roles: getRoles || [],
                user_status: getUsersData.attributes?.userStatus,
                updatedBy: getUsersData.attributes?.updatedBy || null,
                refresh_token: getUserTokenData.refresh_token || null,
                organizationId: getUsersData.attributes?.organizationId || null,
            };
            // userObject.profileStatus = true
            userObject.organizationStatus = true
            const cleanedOrgId = getUsersData.attributes?.organizationId?.replace(/^"+|"+$/g, "").trim();
            /** Get Organization data to check organization status */
            if (cleanedOrgId) {
                const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${getUsersData.attributes?.organizationId}`;
                let orgData: any = await getData(keycloakRealmUrl);
                orgData = orgData.data
                if (orgData) {
                    /** Check If organization account is closed by admin, if yes then throw error */
                    if (orgData.attributes.status === 'inactive') {
                        return res.status(StatusCodes.FORBIDDEN).json({
                            status: false,
                            message: res.__("ERROR_ORGANIZATION_CLOSED"),
                            data: null
                        });
                    }

                    /** Check organization profile completion */
                    // if (orgData?.attributes?.contact_person == null) {
                    //     userObject.organizationStatus = false
                    // }
                }
            }
            /** Check user profile completion 
            if (getUsersData?.attributes?.userPhoneNumber == null) {
                userObject.profileStatus = false
            }*/
            const selectQuery = `SELECT u.id,u.pin_token_version, u.token_version, user_status FROM nv_users AS u WHERE u.keycloak_auth_id = '${getUsersData.id}'`;
            const getUserData: any = await sequelize.query(selectQuery, {
                type: QueryTypes.SELECT,
            });
            if (getUserData.length > 0 && getUserData[0]?.user_status == 'deleted') {
                return res.status(StatusCodes.FORBIDDEN).json({
                    status: false,
                    message: res.__("ERROR_USER_DELETED"),
                    data: null
                });
            }

            const selectRoleQuery = `SELECT u.role_name FROM nv_roles AS u WHERE u.id IN (SELECT ur.role_id FROM nv_user_roles AS ur WHERE ur.user_id = '${getUserData[0]?.id}')`;
            const adminSideUser = [...ADMIN_SIDE_USER];
            const normalUser = [...NORMAL_USER];

            const getNormalUserData: any = await sequelize.query(selectRoleQuery, {
                type: QueryTypes.SELECT,
            });

            if (
                !getNormalUserData.some((item: any) => adminSideUser.includes(item?.role_name)) &&
                req.headers["platform-type"] == "web"
            ) {
                return res
                    .status(StatusCodes.FORBIDDEN)
                    .json({ status: false, message: res.__("PERMISSION_DENIED") });
            }

            if (
                !getNormalUserData.some((item: any) => normalUser.includes(item?.role_name)) &&
                (req.headers["platform-type"] == "ios" ||
                    req.headers["platform-type"] == "android")
            ) {
                return res
                    .status(StatusCodes.FORBIDDEN)
                    .json({ status: false, message: res.__("PERMISSION_DENIED") });
            }

            /** Generate JWT Token for use all API.  */
            const token = await generateToken({
                user_id: getUsersData.id,
                email: getUsersData.email,
                token: getUserTokenData.access_token || null,
                refresh_token: getUserTokenData.refresh_token || null,
                organizationId: getUsersData.attributes?.organizationId,
                token_version: getUserData[0]?.token_version || 0,
                pin_token_version: getUserData[0]?.pin_token_version || 0,
                id: getUserData[0]?.id || null
            });
            userObject.token = token
            userObject.id = getUserData[0]?.id || null
            /** publish new queue for session storing */
            const message: any = {
                user_id: getUsersData.id,
                device_type: req.headers?.["platform-type"] == 'ios' ? 'android' : req.headers?.["platform-type"],
                token: token,
                id: getUserData[0]?.id || null
            };

            /** Publish a message to the "New User verification" queue */
            const queue: any = RABBITMQ_QUEUE.SESSION_STORE;
            await rabbitmqPublisher.publishMessage(queue, message);


            message.keycloak_userId = getUsersData.id
            message.organization_id = getUsersData.attributes?.organizationId
            // Publish a message to the "user_activity_log" queue for user activity
            const activityLog = {
                message: message,
                activity_table: 'User',
                activity_action: 'login'
            };
            await storeActivityLog(activityLog, req.headers)

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_USER_LOGIN"),
                data: userObject
            });
        }
    } catch (e: any) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/**
 * Change Password API
 * @param req
 * @param res 
 * @returns
 */

const changePassword = async (req: any, res: any) => {
    try {
        let { password } = req.body;
        let userId = req.user.sub
        let token = req.token

        /** Get user data hash from database */
        const selectQuery = `SELECT SECRET_DATA FROM CREDENTIAL WHERE USER_ID LIKE '${userId}'`
        const getUserPasswordData: any = await sequelize.query(selectQuery, {
            type: QueryTypes.SELECT,
        });
        /** check if user data hash exist in database */
        if (getUserPasswordData.length > 0) {
            const secretData = JSON.parse(getUserPasswordData[0].SECRET_DATA);
            const value = secretData.value;
            const isMatch = await argon2.hash(password, {
                type: argon2.argon2id,
                memoryCost: 7168,
                timeCost: 5,
                parallelism: 1,
                hashLength: 32,
                salt: Buffer.from(secretData.salt, 'base64'),
                raw: true
            });
            /** compare user data hash with database hash */
            const match = Buffer.compare(isMatch, Buffer.from(value, 'base64')) === 0;
            /** if match then throw error */
            if (match) {
                return res.status(StatusCodes.NOT_ACCEPTABLE).json({
                    status: false,
                    message: res.__("ERROR_FAIL_NEW_PASSWORD_MUST_DIFFERENT"),
                });
            }
        }

        /** Set password for user */
        let passwordData: any = await resetPassword(userId, password, token)
        if (passwordData.status == false) {
            return res.status(passwordData.statusCode).json({
                status: passwordData.status,
                message: passwordData.message,
                data: passwordData.statusText
            });
        }
        if (passwordData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_PASSWORD_CHANGED"),
                data: passwordData.data,
            });
        }
    } catch (e: any) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Forgot Password 
 * @param req
 * @param res 
 * @returns 
*/
const forgotPassword = async (req: any, res: any) => {
    try {
        let getData: any = await forgotPasswordService(req.body)
        if (getData?.status == false) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: getData.status,
                message: res.__(getData.message),
                data: null
            });
        } else {
            return res.status(StatusCodes.OK).json({
                status: getData.status,
                message: res.__(getData.message),
                data: getData.data
            });
        }
    } catch (e: any) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** 
 * Reset User Password
 * @param req
 * @param res 
 * @returns 
*/
const resetUserPassword = async (req: any, res: any) => {
    try {
        let { password, userId } = req.body;

        /** Get User existing password hash 
        /** check if old password exist, then check old password and new password is same. */

        const selectQuery = `SELECT SECRET_DATA FROM CREDENTIAL WHERE USER_ID LIKE '${userId}'`
        const getUserPasswordData: any = await sequelize.query(selectQuery, {
            type: QueryTypes.SELECT,
        });
        if (getUserPasswordData.length > 0) {
            const secretData = JSON.parse(getUserPasswordData[0].SECRET_DATA);
            const value = secretData.value;
            const isMatch = await argon2.hash(password, {
                type: argon2.argon2id,
                memoryCost: 7168,         // match your Keycloak config
                timeCost: 5,              // match your config
                parallelism: 1,
                hashLength: 32,
                salt: Buffer.from(secretData.salt, 'base64'),
                raw: true                 // output will be raw Buffer for comparison
            });
            const match = Buffer.compare(isMatch, Buffer.from(value, 'base64')) === 0;
            if (match) {
                return res.status(StatusCodes.NOT_ACCEPTABLE).json({
                    status: false,
                    message: res.__("ERROR_FAIL_NEW_PASSWORD_MUST_DIFFERENT"),
                });
            }
        }

        /** Set password for user */
        let passwordData: any = await resetPassword(userId, password, null)
        if (passwordData.status == false) {
            return res.status(passwordData.statusCode).json({
                status: passwordData.status,
                message: passwordData.message,
                data: passwordData.statusText
            });
        }

        /** Get UserData */
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
        let getUsersData: any = await getData(keycloakRealmUrl);
        getUsersData = getUsersData.data

        /** check if user status is pending,then update it's status */
        if (getUsersData.attributes.userStatus == 'pending' && getUsersData.emailVerified == false) {
            /** Prepare User Update object */
            let user: any = {
                username: getUsersData.username,
                userId: userId,
                firstName: getUsersData.firstName,
                lastName: getUsersData.lastName,
                email: getUsersData.email,
                emailVerified: true,
                attributes: {
                    userCountryCode: getUsersData.attributes?.userCountryCode || "\"\"",
                    userPhoneNumber: getUsersData.attributes?.userPhoneNumber || "\"\"",
                    organizationId: getUsersData.attributes?.organizationId || "\"\"",
                    userStatus: "active",
                    isLoginpin: getUsersData.attributes?.isLoginpin || "0",
                    userToken: getUsersData.attributes?.userToken || "\"\"",
                    createdBy: getUsersData.attributes?.createdBy,
                    updatedBy: userId, // Updated by current user
                    otp: "",
                    otpExpireTime: ""
                },
            }
            /** Update User auth token and status */
            let updateUserData: any = await updateUsers(user);

            if (updateUserData.status == false) {
                return res.status(updateUserData.statusCode).json({
                    status: updateUserData.status,
                    message: updateUserData.message,
                    data: updateUserData.statusText
                });
            }
        }

        /** Publish queue for update invitation */
        const message = {
            type: 'reset_password',
            userId: userId
        };

        /** Publish a message to the "staff creation email" queue */
        const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_SUCCESS;
        await rabbitmqPublisher.publishMessage(queue, message);

        if (passwordData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_PASSWORD_CHANGED"),
                data: passwordData.data,
            });
        }
    } catch (e: any) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Resend OTP API 
 * @param req
 * @param res 
 * @returns
*/
const resendOTP = async (req: any, res: any) => {
    try {
        let getData: any = await resendOTPService(req.body)
        if (getData?.status == false) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: getData.status,
                message: res.__(getData.message),
                data: null
            });
        } else {
            return res.status(StatusCodes.OK).json({
                status: getData.status,
                message: res.__(getData.message),
                data: getData.data
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Verify OTP API 
 * @param req
 * @param res 
 * @returns
*/
const verifyOTP = async (req: any, res: any) => {
    try {
        const { userId, otp } = req.body;

        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
        let userData: any = await getData(keycloakRealmUrl);
        if (userData.status == false) {
            return res.status(userData.statusCode).json({
                status: userData.status,
                message: userData.message,
                data: userData.statusText
            });
        }
        userData = userData?.data

        /* Check if the OTP has expired by comparing the current time with the user's OTP expiration time. */
        if (moment(new Date()) > moment(userData?.attributes?.otpExpireTime)) {
            return res
                .status(StatusCodes.NOT_ACCEPTABLE)
                .json({ status: false, message: res.__("ERROR_OTP_EXPIRED") });
        }

        /* Compare the provided OTP with the user's stored OTP. */
        if (otp == userData?.attributes?.otp) {
            /** Prepare User Update object */
            let user: any = {
                userId: userId,
                username: userData.username,
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email,
                emailVerified: true,
                attributes: {
                    userCountryCode: userData.attributes?.userCountryCode || "\"\"",
                    userPhoneNumber: userData.attributes?.userPhoneNumber || "\"\"",
                    organizationId: userData.attributes?.organizationId || "\"\"",
                    userStatus: userData.attributes?.userStatus || "active",
                    isLoginpin: userData.attributes?.isLoginpin || "0",
                    userToken: userData.attributes?.userToken || "\"\"",
                    updatedBy: userId, // Updated by current user
                    otp: "",
                    otpExpireTime: ""
                },
            }

            /** Update User auth token and status */
            let updateUserData: any = await updateUsers(user);

            if (updateUserData.status == false) {
                return res.status(updateUserData.statusCode).json({
                    status: updateUserData.status,
                    message: updateUserData.message,
                    data: updateUserData.statusText
                });
            }
            return res
                .status(StatusCodes.OK)
                .json({ status: true, message: res.__("SUCCESS_OTP_VERIFIED") });
        } else {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_OTP_NOT_MATCH") });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/**
 * Timezone Data
 * @param req
 * @param res 
 * @returns
 * **/

const timezoneData = async (req: any, res: any) => {
    try {
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__(`SUCCESS_DATA_FETCHED`),
            data: timezoneList,
        })
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** 
 * Currency data API
 * @param req
 * @param res 
 * @returns
 */
const currencyData = async (req: any, res: any) => {
    try {
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__(`SUCCESS_DATA_FETCHED`),
            data: currencyList,
        })
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Create staff member API by role
 * @param req
 * @param res 
 * @returns
 */
const createStaffMember = async (req: any, res: any) => {
    try {
        /** Destructure user details from the request body */
        const body = req.body;
        let userData: any = {}
        let userId = req.user.sub

        if (body) {
            /** create user in keycloak */
            body.userToken = null
            body.createdBy = userId
            body.updatedBy = userId
            userData = await createUsers(body);
            if (userData.status == false) {
                return res.status(userData.statusCode).json({
                    status: userData.status,
                    message: userData.message,
                    data: userData.statusText,
                    field: userData.field
                });
            }

            /** Set temporary password for user */
            const randomPassword = await GeneratePassword()
            if (randomPassword) {
                var passwordData: any = await resetPassword(userData.data.id, randomPassword)
            }

            if (passwordData?.status == true) {
                const assignRole: any = body.role
                /** Assign role to user */
                let assignUserRole: any = await assignRolesToUser(userData.data.id, assignRole)
                if (assignUserRole.status == false) {
                    return res.status(assignUserRole.statusCode).json({
                        status: assignUserRole.status,
                        message: assignUserRole.message,
                        data: null
                    });
                }

                /**  Join User to Organization */
                let checkOrg: any = await joinUserToOrg(body.organization_id, userData.data.id)
                if (checkOrg.status == false) {
                    return res.status(checkOrg.statusCode).json({
                        status: checkOrg.status,
                        message: checkOrg.message,
                        data: null
                    });
                }

                /** assign default role to all users */
                let getAllRealmRoles: any = await getPredefinedRealmRoles(userData.data.id)

                /** check if master role exists */
                const getRealmRole: any = getAllRealmRoles.data.find((role: any) => role.role === global.config.KEYCLOAK_REALM_ROLE);

                /** if master role not found */
                if (!getRealmRole) {
                    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                        status: false,
                        message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                        data: null
                    });
                }
                /** prepare predefined realm role object */
                let predefinedObject = {
                    id: getRealmRole.id,
                    name: getRealmRole.role,
                    description: getRealmRole.description
                }
                /** Assign predefined realm role to user for access their token. */
                await assignPredefinedRoleToUser(userData.data.id, [predefinedObject], getRealmRole.clientId)
            } else {
                return res.status(passwordData.statusCode).json({
                    status: passwordData.status,
                    message: passwordData.message,
                    data: passwordData.statusText
                });
            }

            /** Handle email configuration if user_email exists */
            if (body.email) {
                // Extract role names from the payload
                const findName = body.role.map((role: any) => role.name);

                /** Prepare message for queue */
                const message = {
                    name: `${body.firstName} ${body.lastName}`,
                    email: body.email,
                    password: randomPassword,
                    roles: findName.join(", ") // Join role names into a single string
                };

                /** Publish a message to the "New User verification" queue */
                const queue: any = RABBITMQ_QUEUE.STAFF_CREATION;
                await rabbitmqPublisher.publishMessage(queue, message);
            }
        }

        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.CREATED).json({
                status: true,
                message: res.__("SUCCESS_USER_CREATION"),
                data: userData.data,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Get all realm roles
 * @param req
 * @param res 
 * @returns
 */
const getRealmRoles = async (req: any, res: any) => {
    try {
        const realmRoles: any = await getRealmsRoles();
        if (realmRoles.status == false) {
            return res.status(realmRoles.statusCode).json({
                status: realmRoles.status,
                message: realmRoles.message,
                data: realmRoles.statusText
            });
        }
        if (realmRoles) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_DATA_FETCHED"),
                data: realmRoles
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** 
 * Update User Details API
 * @param req
 * @param res 
 * @returns
 */
const updateUser = async (req: any, res: any) => {
    try {

        /** Validate req.body with Joi  */
        const { error } = await authValidator.updateUser.validate(req.body);
        if (error) {
            return res
                .status(400)
                .json({ status: false, message: error.details[0].message });
        }

        let { userId, firstName, lastName, email, userCountryCode, userPhoneNumber, organizationId, userStatus, isLoginpin, userToken, userSignature, username } = req.body
        let token = req.token
        userId = userId ? userId : req.user.sub

        /** Update User auth token and status */
        let user: any = {
            userId: userId,
            username: username,
            firstName: firstName,
            lastName: lastName,
            email: email,
            emailVerified: true,
            attributes: {
                userCountryCode: userCountryCode || "\"\"",
                userPhoneNumber: userPhoneNumber || "\"\"",
                organizationId: organizationId || "\"\"",
                userStatus: userStatus || "active",
                isLoginpin: isLoginpin || "0",
                userToken: userToken || "\"\"",
                updatedBy: userId, // Updated by current user
                otp: null,
                otpExpireTime: null,

            },
        }
        if (req.files[0]?.item_id) {
            user.attributes.userSignature = req.files[0]?.item_id;
            if (req?.files?.[0]?.isMovable) {
                await moveFileInBucket(
                    req.files[0].bucket,
                    req.files[0].path,
                    FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.destinationPath(
                        organizationId,
                        userId,
                        req.files[0].filename,
                    ),
                    req.files[0]?.item_id,
                    true,
                    organizationId
                );
            }
        }
        if (userSignature) {
            const findItem = await Item.findOne({ where: { item_name: { [Op.like]: `%${userSignature}` }, item_organization_id: organizationId } })
            if (findItem && findItem.id) {
                user.attributes.userSignature = findItem?.id
            }
        }
        let updateUserData: any = await updateUsers(user, token);

        if (updateUserData.status == false) {
            return res.status(updateUserData.statusCode).json({
                status: updateUserData.status,
                message: updateUserData.message,
                data: updateUserData.statusText
            });
        }

        /** If user is successfully created, return success response */
        if (updateUserData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_DATA_UPDATED"),
                data: null
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** 
 * CountryList API
 * @param req
 * @param res 
 * @returns
 */
const countryLists = async (req: any, res: any) => {
    try {
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__(`SUCCESS_DATA_FETCHED`),
            data: countryList,
        })
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** 
 * Get New Access token from refresh token
 * @param req
 * @param res 
 * @returns
 */
const getAccessTokenFromRefreshToken = async (req: any, res: any) => {
    try {
        const { refreshToken } = req.body;
        /** Get New Access token from refresh token */
        let response: any = await getNewTokenFromRefreshToken(refreshToken)

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__(`SUCCESS_DATA_FETCHED`),
            data: { access_token: response.access_token, refresh_token: response.refresh_token },
        })
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** 
 * Resend Email for Auth Token
 * @param req
 * @param res 
 * @returns
 */
const resendEmail = async (req: any, res: any) => {
    try {
        let { userId, email, isUpdateEmail = false } = req.body

        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
        let userData: any = await getData(keycloakRealmUrl);
        userData = userData.data

        if (userData.status == false) {
            return res.status(userData.statusCode).json({
                status: userData.status,
                message: userData.message,
                data: userData.statusText
            });
        }

        /** Check if user is already verified then return error message */
        if (userData.attributes.userStatus == 'active') {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ERROR_EMAIL_ALREADY_VERIFIED"),
                data: null
            });
        }
        /** check if user want to update their email, if yes then update and send email to that email. */
        email = isUpdateEmail ? email : userData.email

        /** Prepare User Update object */
        let user: any = {
            username: userData.username,
            userId: userId,
            firstName: userData.firstName,
            lastName: userData.lastName,
            email: email,
            emailVerified: userData.emailVerified,
            attributes: {
                userCountryCode: userData.attributes?.userCountryCode || "\"\"",
                userPhoneNumber: userData.attributes?.userPhoneNumber || "\"\"",
                organizationId: userData.attributes?.organizationId || "\"\"",
                userStatus: userData.attributes?.userStatus || "active",
                isLoginpin: userData.attributes?.isLoginpin || "0",
                userToken: await generateVerificationToken(),
                updatedBy: userId, // Updated by current user
                otp: "",
                otpExpireTime: ""
            },
        }
        /** Update User auth token and status */
        let updateData: any = await updateUsers(user);
        if (updateData.status == false) {
            return res.status(updateData.statusCode).json({
                status: updateData.status,
                message: updateData.message,
                data: updateData.statusText
            });
        }

        let user_auth_token = user.attributes?.userToken;

        /** Prepare message for queue */
        const message = {
            name: `${userData.firstName} ${userData.lastName}`,
            email: email,
            URL: global.config.WEB_BASE_URL + `/org/login?token=${user_auth_token}&data=${userData.id}`,
            LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
            ADDRESS: EMAIL_ADDRESS.ADDRESS,
            PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
            EMAIL: EMAIL_ADDRESS.EMAIL,
            ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
            username: userData.username,
            smtpConfig: 'AUTH'
        };

        /** Publish a message to the "New User verification" queue */
        const queue: any = RABBITMQ_QUEUE.NEW_USER_VERIFICATION;
        await rabbitmqPublisher.publishMessage(queue, message);

        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_VERIFICATION_MAIL_RESENT"),
                data: userData.data,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Transform Old users into keycloak */
const oldUserDataScript = async (req: any, res: any) => {
    try {
        /** Destructure user details from the request body */
        let { id, user_first_name, user_last_name, user_email, user_phone_number, userCountryCode, is_login_pin, webAppToken, appToken, organization_id, user_password } = req.body;
        let userData: any = {}
        let username: any = ''
        /** Make URL for get organization data. */
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${organization_id}`;
        /** Fetch organization name */
        let getOrgData: any = await getData(keycloakRealmUrl, null)
        if (getOrgData.status == false) {
            return {
                status: getOrgData.status,
                message: getOrgData.message,
                data: getOrgData.statusText
            };
        }
        getOrgData = getOrgData?.data
        if (getOrgData) {
            /** Prepare staff user name for combination of below fields
             *  organization_name first 3 letter, first_name, last_name first 2 letter, random_number
             */
            username = `${getOrgData.name.slice(0, 2)}.${user_first_name.replace(/\s+/g, '').slice(0, 10)}${user_last_name.slice(0, 10)}`
            username = await generateUniqueUsername(username)
        }

        if (req.body) {
            /** create user in keycloak */
            let user: any = {
                username: username || "", //Need to discus for old users
                enabled: true,
                emailVerified: false,
                firstName: user_first_name,
                lastName: user_last_name,
                email: user_email,
                userCountryCode: userCountryCode || "", //Need to discus for old users
                userPhoneNumber: user_phone_number || "",
                isLoginpin: is_login_pin || false,
                webAppToken: webAppToken || "",
                appToken: appToken || "",
                organization_id: organization_id,
                createdBy: "",
                updatedBy: "",
                userStatus: "pending",
                userToken: ""
            }
            if (id == 1) {
                user.emailVerified = true
            }
            userData = await createUsers(user);
            if (userData.status == false) {
                return res.status(userData.statusCode).json({
                    status: userData.status,
                    message: userData.message,
                    data: null
                });
            }
            user_password = 'Namaste#1234'
            /** Set password for user */
            var passwordData: any = await resetPassword(userData.data.id, user_password)
            if (passwordData.status == false) {
                return res.status(passwordData.statusCode).json({
                    status: passwordData.status,
                    message: passwordData.message,
                    data: passwordData.statusText,
                    field: passwordData.field
                });
            }
            /** Set master role to org user */
            const realmRoles: any = await getRealmsRoles();
            if (realmRoles.status == false) {
                return res.status(realmRoles.statusCode).json({
                    status: realmRoles.status,
                    message: realmRoles.message,
                    data: realmRoles.statusText
                });
            }

            if (id == 1) {
                /** check if master role exists */
                const masterRole: any = realmRoles.data.find((role: any) => role.name === global.config.KEYCLOAK_MASTER_ROLE &&
                    role.description === global.config.KEYCLOAK_MASTER_ROLE_DESCRIPTION);

                /** if master role not found */
                if (!masterRole) {
                    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                        status: false,
                        message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                        data: null
                    })
                }
                /** Assign org_master role to super admin */
                await assignRolesToUser(userData.data.id, [masterRole])
            } else {
                /** check user staff role exist */
                const masterRole: any = realmRoles.data.find((role: any) => role.name === global.config.KEYCLOAK_STAFF_ROLE &&
                    role.description === global.config.KEYCLOAK_STAFF_ROLE_DESCRIPTION);

                /** if staff role not found */
                if (!masterRole) {
                    return {
                        status: false,
                        message: "Staff role not found",
                        data: null
                    }
                }
                /** Assign staff role to user */
                await assignRolesToUser(userData.data.id, [masterRole])
            }

            /** assign default role to all users */
            let getAllRealmRoles: any = await getPredefinedRealmRoles(userData.data.id)

            /** check if master role exists */
            const getRealmRole: any = getAllRealmRoles.data.find((role: any) => role.role === global.config.KEYCLOAK_REALM_ROLE);

            /** if master role not found */
            if (!getRealmRole) {
                return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                    status: false,
                    message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                    data: null
                });
            }
            /** prepare predefined realm role object */
            let predefinedObject = {
                id: getRealmRole.id,
                name: getRealmRole.role,
                description: getRealmRole.description
            }
            /** Assign predefined realm role to user for access their token. */
            await assignPredefinedRoleToUser(userData.data.id, [predefinedObject], getRealmRole.clientId)

            /**  Join User to Organization */
            await joinUserToOrg(organization_id, userData.data.id)

            /** send email to staff user for that username and password details */
            // if (global.config.NEXT_NODE_ENV == 'production') {
            // if (id == 1 || id == 2 || id == 3 || id == 4 || id == 5) {
            //     let message: any = {
            //         name: `${user_first_name} ${user_last_name}`,
            //         email: user_email,
            //         username: username,
            //         password: user_password,
            //         type: 'nv_users',
            //         LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
            //         ADDRESS: EMAIL_ADDRESS.ADDRESS,
            //         PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
            //         EMAIL: EMAIL_ADDRESS.EMAIL,
            //         ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
            //         smtpConfig: 'AUTH'
            //     }
            //     await rabbitmqPublisher.publishMessage(RABBITMQ_QUEUE.NEW_USER_VERIFICATION, message);
            // }
            // }
        }

        /** If staff create successfully then publish new queue for update keycloak auth id in nv_users tables */
        let message = {
            keycloak_auth_id: userData.data.id,
            userId: id,
            username: userData.data.username
        }
        const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_SUCCESS;
        await rabbitmqPublisher.publishMessage(queue, message);

        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.CREATED).json({
                status: true,
                message: res.__("SUCCESS_USER_CREATED"),
                data: userData.data,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** organization creation */
const createExistingOrganization = async (req: any, res: any) => {
    try {
        let data: any = {}
        const organization: any = {
            name: 'Namaste_Village',
            redirectUrl: "",
            description: "This is my organization data",
            multiple_location: "1",
            email: '<EMAIL>',
            website: 'https://nv.com',
            status: 'active',
            created_at: new Date()
        };

        /** check if organization exists */
        if (organization) {
            organization.domain = new URL(organization.website).hostname;
            data = await createOrganization(organization)
            if (data.status == false) {
                return res.status(data.statusCode).json({
                    status: data.status,
                    message: data.message,
                    data: null
                });
            }
        }
        return res.status(StatusCodes.CREATED).json({
            status: true,
            message: res.__("SUCCESS_USER_CREATED"),
            data: data.orgData,
        });
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Create Main Super admin Script */
const createMainSuperAdmin = async (req: any, res: any) => {
    try {
        /** Destructure user details from the request body */
        let { user_first_name, user_last_name, user_email, user_phone_number, userCountryCode, username, user_password } = req.body;
        let userData: any = {}

        if (req.body) {
            /** create user in keycloak */
            let user: any = {
                username: username || "", //Need to discus for old users
                enabled: true,
                emailVerified: true,
                firstName: user_first_name,
                lastName: user_last_name,
                email: user_email,
                userCountryCode: userCountryCode || "", //Need to discus for old users
                userPhoneNumber: user_phone_number || "",
                isLoginpin: false,
                webAppToken: "",
                appToken: "",
                createdBy: "",
                updatedBy: "",
                userStatus: "verified",
                userToken: ""
            }
            userData = await createUsers(user);
            if (userData.status == false) {
                return res.status(userData.statusCode).json({
                    status: userData.status,
                    message: userData.message,
                    data: null
                });
            }

            /** Set password for user */
            var passwordData: any = await resetPassword(userData.data.id, user_password)
            if (passwordData.status == false) {
                return res.status(passwordData.statusCode).json({
                    status: passwordData.status,
                    message: passwordData.message,
                    data: passwordData.statusText,
                    field: passwordData.field
                });
            }
            /** Set master role to org user */
            const realmRoles: any = await getRealmsRoles();
            if (realmRoles.status == false) {
                return res.status(realmRoles.statusCode).json({
                    status: realmRoles.status,
                    message: realmRoles.message,
                    data: realmRoles.statusText
                });
            }

            /** check if master role exists */
            const masterRole: any = realmRoles.data.find((role: any) => role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
                role.description === global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION);

            /** if master role not found */
            if (!masterRole) {
                return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                    status: false,
                    message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                    data: null
                })
            }
            /** Assign org_master role to super admin */
            await assignRolesToUser(userData.data.id, [masterRole])


            /** assign default role to all users */
            let getAllRealmRoles: any = await getPredefinedRealmRoles(userData.data.id)

            /** check if master role exists */
            const getRealmRole: any = getAllRealmRoles.data.find((role: any) => role.role === global.config.KEYCLOAK_REALM_ROLE);

            /** if master role not found */
            if (!getRealmRole) {
                return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
                    status: false,
                    message: res.__("ERROR_MASTER_ROLE_NOT_FOUND"),
                    data: null
                });
            }
            /** prepare predefined realm role object */
            let predefinedObject = {
                id: getRealmRole.id,
                name: getRealmRole.role,
                description: getRealmRole.description
            }
            /** Assign predefined realm role to user for access their token. */
            await assignPredefinedRoleToUser(userData.data.id, [predefinedObject], getRealmRole.clientId)
        }
        /** Store all data in nv_users data */
        const insertUserQuery = `
            INSERT INTO nv_users (
            id, user_first_name, user_middle_name, user_last_name, user_email, address_line1, address_line2, 
            country, pin_code, user_phone_number, emergency_contact, user_password, user_designation, 
            user_signature, branch_id, otp, department_id, user_avatar, appToken, webAppToken, 
            otp_expire, is_login_pin, user_status, user_gender, user_gender_other, marital_status, 
            marital_status_other, user_login_pin, user_joining_date, date_of_birth, confirm_by, 
            confirm_by_date, created_by, updated_by, user_active_role_id, web_user_active_role_id, 
            employment_contract, token_version, user_verification_doc, last_reject_remark, pin_token_version, 
            createdAt, updatedAt, geo_country, geo_state, geo_city, employment_number, keycloak_auth_id, 
            organization_id, username, rota_group_by, list_order
        ) VALUES (
            NULL, '${user_first_name}', NULL, '${user_last_name}', '${user_email}', NULL, NULL, NULL, NULL, ${user_phone_number}, NULL, 'sha1$100$16$YU5EcePGzFKdHbnwmXrlgQ==$4wr9t02F8E1ktPDmeA9EdA==', 'Super admin', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 
            NULL, '0', 'verified', 'male', NULL, NULL, NULL, NULL, 
            NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, NULL, '0', 
            NULL, NULL, '0', '2025-04-08 13:55:52.000000', '2025-04-08 13:55:52.000000', NULL, NULL, NULL, '000001', 
            '${userData.data.id}', NULL, '${username}', NULL, NULL
        )`;
        const insertMainUser: any = await sequelize.query(insertUserQuery, {
            type: QueryTypes.INSERT,
        });
        const insertUserRolesQuery = ` INSERT INTO nv_user_roles(\`user_id\`, \`role_id\`, \`created_by\`, \`createdAt\`) 
                VALUES ('${insertMainUser[0]}', '1', NULL, '2025-04-08 13:55:52.000000')`;

        const InsertUserRoles: any = await sequelize.query(insertUserRolesQuery, {
            type: QueryTypes.INSERT,
        });
        /** If user is successfully created, return success response */
        if (userData) {
            return res.status(StatusCodes.CREATED).json({
                status: true,
                message: res.__("SUCCESS_USER_CREATED"),
                data: userData.data,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** const mail sending script */
const sendUserMail = async (req: any, res: any) => {
    try {
        let selectQuery = `SELECT id, user_email, keycloak_auth_id, user_first_name, user_last_name,username FROM nv_users WHERE organization_id = '${process.env.ORGANIZATION_ID}'`;
        const users: any = await sequelize.query(selectQuery, {
            type: QueryTypes.SELECT,
        });

        if (users.length > 0) {
            for (let user of users) {
                // const randomPassword = await GeneratePassword()
                const randomPassword = 'Namaste#1234'

                var passwordData: any = await resetPassword(user.keycloak_auth_id, randomPassword)
                if (passwordData.status == false) {
                    return res.status(passwordData.statusCode).json({
                        status: passwordData.status,
                        message: passwordData.message,
                        data: passwordData.statusText,
                        field: passwordData.field
                    });
                }

                if (process.env.NEXT_NODE_ENV == 'production') {
                    let message: any = {
                        name: `${user.user_first_name} ${user.user_last_name}`,
                        email: user.user_email,
                        username: user.username,
                        password: randomPassword,
                        type: 'nv_users',
                        LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'AUTH'
                    }
                    await rabbitmqPublisher.publishMessage(RABBITMQ_QUEUE.NEW_USER_VERIFICATION, message);
                }

            }
            return res.status(StatusCodes.CREATED).json({
                status: true,
                message: res.__("SUCCESS_USER_CREATED"),
                data: users,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}
export default {
    orgUserRegister,
    verifyAuthToken,
    getUserDataById,
    login,
    changePassword,
    forgotPassword,
    resendOTP,
    verifyOTP,
    currencyData,
    timezoneData,
    createStaffMember,
    getRealmRoles,
    updateUser,
    countryLists,
    resetUserPassword,
    getAccessTokenFromRefreshToken,
    resendEmail,
    oldUserDataScript,
    createExistingOrganization,
    createMainSuperAdmin,
    sendUserMail
};