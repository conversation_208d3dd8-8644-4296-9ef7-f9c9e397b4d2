'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Add unique constraint for category names within organization scope
      // This ensures no duplicate category names within the same organization
      // AND no duplicate names between organization categories and system defaults
      
      console.log('🔧 Adding unique constraints for categories...');
      
      // 1. Add unique constraint for category_name + organization_id + category_type
      // This prevents duplicates within the same organization and type
      await queryInterface.addConstraint('mo_category', {
        fields: ['category_name', 'organization_id', 'category_type'],
        type: 'unique',
        name: 'unique_category_name_org_type',
      });
      
      // 2. Add unique constraint for category_slug + organization_id + category_type
      // This prevents duplicate slugs within the same organization and type
      await queryInterface.addConstraint('mo_category', {
        fields: ['category_slug', 'organization_id', 'category_type'],
        type: 'unique',
        name: 'unique_category_slug_org_type',
      });
      
      // 3. Add index for better performance on category lookups
      await queryInterface.addIndex('mo_category', {
        fields: ['category_name', 'category_type', 'organization_id'],
        name: 'idx_category_name_type_org',
      });
      
      // 4. Add index for system categories
      await queryInterface.addIndex('mo_category', {
        fields: ['is_system_category', 'category_type', 'category_status'],
        name: 'idx_system_category_type_status',
      });
      
      console.log('✅ Category unique constraints added successfully');
      
    } catch (error) {
      console.error('❌ Error adding category constraints:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log('🔧 Removing category unique constraints...');
      
      // Remove constraints in reverse order
      await queryInterface.removeConstraint('mo_category', 'unique_category_slug_org_type');
      await queryInterface.removeConstraint('mo_category', 'unique_category_name_org_type');
      
      // Remove indexes
      await queryInterface.removeIndex('mo_category', 'idx_system_category_type_status');
      await queryInterface.removeIndex('mo_category', 'idx_category_name_type_org');
      
      console.log('✅ Category constraints removed successfully');
      
    } catch (error) {
      console.error('❌ Error removing category constraints:', error);
      throw error;
    }
  }
};
