/**
 * Constants for Customer Service / Support Ticket Microservice
 * Following TTH project standards with proper enum definitions and configurations.
 * Based on recipe-ms and auth-ms patterns for consistency across microservices.
 */

// ===== ROLE CONSTANTS =====
// Following exact pattern from recipe-ms and auth-ms

export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  BRANCH_MANAGER: "Branch Manager",
  ASSIGN_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCHEN: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  <PERSON>IG<PERSON><PERSON><PERSON>: "Signature",
});

export const ADMIN_SIDE_USER = Object.freeze([
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.DIRECTOR,
  ROLE_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE,
]);

export const NORMAL_USER = Object.freeze([
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
]);

// ===== TICKET SYSTEM CONSTANTS =====
// Following recipe-ms enum pattern with different keys and values

/**
 * Ticket Status - Lifecycle states of a support ticket
 * Following recipe-ms enum pattern with different keys and values as per standards
 */
export const TICKET_STATUS = Object.freeze({
  OPEN: "open",
  ASSIGNED: "assigned",
  IN_PROGRESS: "in_progress",
  ON_HOLD: "on_hold",
  ESCALATED: "escalated",
  QA_REVIEW: "qa_review",
  UNDER_REVIEW: "under_review",
  RESOLVED: "resolved",
  CLOSED: "closed",
});

/**
 * Priority Levels - Based on TTH requirements document
 * Following recipe-ms enum pattern with different keys and values, removed NONE and EMERGENCY as per complaints
 */
export const TICKET_PRIORITY = Object.freeze({
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  URGENT: "urgent",
});

/**
 * Module Types - Based on TTH requirements document
 * Following recipe-ms enum pattern with different keys and values
 */
export const TICKET_MODULE = Object.freeze({
  HRMS: "hrms",
  PMS: "pms",
  OTHER: "other",
});

/**
 * Issue Types - Based on TTH requirements document
 * Following recipe-ms enum pattern with different keys and values
 */
export const TICKET_TYPE = Object.freeze({
  BUG: "bug",
  REQUEST_FOR_FEATURE: "request_for_feature",
  GENERAL_QUERY: "general_query",
  TECHNICAL: "technical",
  NON_TECHNICAL: "non_technical",
  EXPORT_HELP: "export_help",
  SUPPORT: "support",
});

// ===== RATE LIMITING CONSTANTS =====
// Following auth-ms and recipe-ms patterns

export const RATE_LIMIT = Object.freeze({
  MAX_REQUESTS: 100, // Max requests per window
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  FILE_UPLOAD_PER_HOUR: 50, // File uploads per hour
  STRICT_LIMIT: 5, // For sensitive operations
  STRICT_WINDOW_MS: 5 * 60 * 1000, // 5 minutes
});

// ===== RABBITMQ CONSTANTS =====
// Following recipe-ms and auth-ms patterns

export const RABBITMQ_QUEUE = Object.freeze({
  TICKET_CREATED: "ticket_created",
  TICKET_UPDATED: "ticket_updated",
  TICKET_ASSIGNED: "ticket_assigned",
  TICKET_RESOLVED: "ticket_resolved",
  TICKET_CLOSED: "ticket_closed",
  EMAIL_NOTIFICATION: "email_notification",
  PUSH_NOTIFICATION_SUCCESS: "push_notification_success",
  USER_ACTIVITY_LOG: "user_activity_log",
  // Mail queues following auth-ms pattern
  MAIL_FAILED: "mail_failed",
  MAIL_SUCCESS: "mail_success",
  // Support ticket specific queues
  SUPPORT_TICKET_EMAIL: "support_ticket_email",
  SUPPORT_NOTIFICATION: "support_notification",
  TICKET_STATUS_UPDATE: "ticket_status_update",
  TICKET_ASSIGNMENT_UPDATE: "ticket_assignment_update",
});

// ===== EMAIL CONSTANTS =====
// Following auth-ms patterns

export const EMAIL_CONSTANT = Object.freeze({
  TICKET_CREATED_SUBJECT: "Support Ticket Created",
  TICKET_UPDATED_SUBJECT: "Support Ticket Updated",
  TICKET_RESOLVED_SUBJECT: "Support Ticket Resolved",
  TICKET_CLOSED_SUBJECT: "Support Ticket Closed",
  FROM_EMAIL: "<EMAIL>",
});

// ===== FILE UPLOAD CONSTANTS =====
// Following recipe-ms patterns

export const FILE_UPLOAD_CONSTANT = Object.freeze({
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_EXTENSIONS: [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".pdf",
    ".doc",
    ".docx",
    ".txt",
  ],
  ALLOWED_MIME_TYPES: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "text/plain",
  ],
  UPLOAD_PATH: "uploads/tickets/",
  MAX_FILES_PER_TICKET: 5,
});

// ===== VALIDATION CONSTANTS =====
// Following recipe-ms patterns

export const VALIDATION_CONSTANT = Object.freeze({
  TICKET_TITLE_MIN_LENGTH: 5,
  TICKET_TITLE_MAX_LENGTH: 200,
  TICKET_DESCRIPTION_MIN_LENGTH: 10,
  TICKET_DESCRIPTION_MAX_LENGTH: 5000,
  RESOLUTION_NOTE_MAX_LENGTH: 2000,
  REVIEW_COMMENT_MAX_LENGTH: 1000,
  RATING_MIN: 1,
  RATING_MAX: 5,
});
