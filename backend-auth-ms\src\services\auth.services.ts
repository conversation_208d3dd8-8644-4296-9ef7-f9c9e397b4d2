import { generateOtp, otpExpTime } from "../helper/utils";
import { updateUsers, getData, createUsers, resetPassword, assignPredefinedRoleToUser, getPredefinedRealmRoles, assignRolesToUser, joinUserToOrg, getRealmsRoles } from "../keycloak/common";
import { otpAttemptValidation, generateUniqueUsername } from "../helper/common";
import { EMAIL_ADDRESS, EMAILCONSTANT, RABBITMQ_QUEUE } from "../helper/constant";
import rabbitmqPublisher from '../rabbitmq/rabbitmq';
import { Mail, notify_mail_status } from "../models/Mail";

const forgotPasswordService = async (forgotPasswordObject: any) => {
    try {
        /** If req.body data then check all conditions */
        if (forgotPasswordObject) {
            const { email = "", forgot_type } = forgotPasswordObject;
            const otp = await generateOtp(4);
            const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users?email=${email}`;
            let userData: any = await getData(keycloakRealmUrl);
            if (userData.status == false) {
                return {
                    status: userData.status,
                    message: userData.statusText,
                    data: null
                };
            }
            userData = userData?.data[0]
            /** Check if user not exist then return error */
            if (!userData) {
                return {
                    status: false,
                    message: "ERROR_USER_NOT_FOUND",
                    data: null
                }
            }
            /** Check Email is not link with that user return error */
            if (userData.email !== email) {
                return {
                    status: false,
                    message: "ERROR_EMAIL_NOT_EXIST",
                    data: null
                };
            }

            /** check if user status is pending */
            if (userData.attributes.userStatus === 'pending') {
                return {
                    status: false,
                    message: "ERROR_USER_VERIFICATION_PENDING",
                    data: null
                } // Return error if user status is 'pending'
            }

            if (forgot_type && forgot_type == 'pin' && !userData.isLoginpin) {
                return { status: false, message: "FAIL_PLEASE_SET_PIN" };
            }


            let subjectPrefix = forgot_type === 'pin'
                ? EMAILCONSTANT.FORGOT_PIN_OTP.subject
                : EMAILCONSTANT.FORGOT_PASSWORD.subject;

            /** add server prefix in email subject to identify email */
            let subject = `${subjectPrefix} ${process.env.NEXT_NODE_ENV === undefined
                ? "(development)"
                : process.env.NEXT_NODE_ENV !== "production"
                    ? `(${process.env.NEXT_NODE_ENV})`
                    : ""}`;

            /** Check otp attempt validation condition */
            const attemptValidation = await otpAttemptValidation(email, subject, global.config.FORGOT_PASSWORD_ATTEMPT);
            /** return error based on condition */
            if (attemptValidation !== true) {
                return { status: false, message: attemptValidation };
            }
            if (forgot_type && forgot_type == 'pin') {
                /** Prepare message for queue */
                const message = {
                    name: `${userData.firstName} ${userData.lastName}`,
                    email: email,
                    otp: otp
                };

                /** Publish a message to the "forgot password pin OTP" queue */
                const queue: any = RABBITMQ_QUEUE.FORGOT_PASSWORD_PIN_OTP;
                await rabbitmqPublisher.publishMessage(queue, message);
            } else {
                if (email) {
                    /** Prepare message for queue */
                    const message = {
                        name: `${userData.firstName} ${userData.lastName}`,
                        email: email,
                        otp: otp,
                        LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'AUTH'
                    };

                    /** Publish a message to the "forgot password OTP" queue */
                    const queue: any = RABBITMQ_QUEUE.FORGOT_PASSWORD_MAIL_OTP;
                    await rabbitmqPublisher.publishMessage(queue, message);
                }
            }

            /** Prepare User Update object */
            let user: any = {
                username: userData.username,
                userId: userData.id,
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email,
                attributes: {
                    userCountryCode: userData.attributes?.userCountryCode || "",
                    userPhoneNumber: userData.attributes?.userPhoneNumber || "",
                    organizationId: userData.attributes?.organizationId || "",
                    userStatus: userData.attributes?.userStatus || "active",
                    isLoginpin: userData.attributes?.isLoginpin || "0",
                    userToken: userData.attributes?.userToken || "",
                    updatedBy: userData.id, // Updated by current user
                    otp: otp,
                    otpExpireTime: await otpExpTime()
                },
            }

            /** Update User auth token and status */
            let updateUserData: any = await updateUsers(user);

            if (updateUserData.status == false) {
                return {
                    status: updateUserData.status,
                    message: updateUserData.message,
                    data: updateUserData.statusText
                }
            }
            return { status: true, message: 'SUCCESS_FORGOT_PASSWORD_EMAIL', data: userData }
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

/** Resend Password OTP service to check otp validation */
const resendOTPService = async (resendOTPObject: any) => {
    try {
        /** If req.body data then check all conditions */
        if (resendOTPObject) {
            const { userId = "" } = resendOTPObject;
            const otp = await generateOtp(4);
            const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
            let userData: any = await getData(keycloakRealmUrl);
            if (userData.status == false) {
                return {
                    status: userData.status,
                    message: userData.statusText,
                    data: null
                };
            }
            userData = userData?.data
            /** Check if user not exist then return error */
            if (!userData) {
                return {
                    status: false,
                    message: "ERROR_USER_NOT_FOUND",
                    data: null
                }
            }

            /** add server prefix in email subject to identify email */
            let subject = `${EMAILCONSTANT.RESEND_OTP.subject} ${process.env.NEXT_NODE_ENV === undefined
                ? "(development)"
                : process.env.NEXT_NODE_ENV !== "production"
                    ? `(${process.env.NEXT_NODE_ENV})`
                    : ""}`;

            /** Check otp attempt validation condition */
            const attemptValidation = await otpAttemptValidation(userData.email, subject, global.config.FORGOT_PASSWORD_ATTEMPT);
            /** return error based on condition */
            if (attemptValidation !== true) {
                return { status: false, message: attemptValidation };
            }

            if (userData.email) {
                /** Prepare message for queue */
                const message = {
                    name: `${userData.firstName} ${userData.lastName}`,
                    email: userData.email,
                    otp: otp,
                    LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
                    ADDRESS: EMAIL_ADDRESS.ADDRESS,
                    PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                    EMAIL: EMAIL_ADDRESS.EMAIL,
                    ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                    smtpConfig: 'AUTH'
                };

                /** Publish a message to the "forgot password OTP" queue */
                const queue: any = RABBITMQ_QUEUE.RESET_PASSWORD_MAIL_OTP
                await rabbitmqPublisher.publishMessage(queue, message);
            }
            /** Prepare User Update object */
            let user: any = {
                username: userData.username,
                userId: userData.id,
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email,
                attributes: {
                    userCountryCode: userData.attributes?.userCountryCode || "",
                    userPhoneNumber: userData.attributes?.userPhoneNumber || "",
                    organizationId: userData.attributes?.organizationId || "",
                    userStatus: userData.attributes?.userStatus || "active",
                    isLoginpin: userData.attributes?.isLoginpin || "0",
                    userToken: userData.attributes?.userToken || "",
                    updatedBy: userData.id, // Updated by current user
                    otp: otp,
                    otpExpireTime: await otpExpTime()
                },
            }
            /** Update User auth token and status */
            let updateUserData: any = await updateUsers(user);

            if (updateUserData.status == false) {
                return {
                    status: updateUserData.status,
                    message: updateUserData.message,
                    data: updateUserData.statusText
                }
            }
            return { status: true, message: 'SUCCESS_OTP_RESENT', data: userData }
        }
    } catch (e: any) {
        console.error('Error in Resend OTP Service:', e);
        return { status: false, message: e }
    }
}

/** Check mail failed and success and store data accordingly */
const mailSuccessFailedService = async (mailData: any) => {
    try {
        if (mailData && mailData.mailResponse) {
            let mailOptions = mailData.mailResponse
            let mailObject: any = {
                notify_mail_subject: mailOptions.notify_mail_subject,
                notify_mail_to: JSON.stringify(mailOptions.notify_mail_to),
                notify_mail_from: mailOptions.notify_mail_from,
                notify_mail_body: JSON.stringify(mailOptions.notify_mail_body),
                notify_mail_response: JSON.stringify(mailOptions.notify_mail_response)
            }
            if (mailData.mail_failed === true) {
                mailObject.notify_mail_status = notify_mail_status.FAILED
            } else {
                mailObject.notify_mail_status = notify_mail_status.SENT
            }
            await Mail.create(mailObject)
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

/** consume staff creation details queued and stored data in keycloak. */
const staffCreationDetails = async (staffData: any) => {
    try {
        let username = ''
        staffData = staffData.staffResponse
        switch (staffData.type) {
            case 'staff_update':
                /** update staff member details */
                if (staffData.keycloak_auth_id) {
                    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${staffData.keycloak_auth_id}`;
                    let userData: any = await getData(keycloakRealmUrl);
                    if (userData.status == false) {
                        return {
                            status: userData.status,
                            message: userData.message,
                            data: userData.statusText
                        };
                    }
                    userData = userData?.data

                    /** Prepare User Update object */
                    let user: any = {
                        username: staffData.username ? staffData.username : userData.username,
                        userId: userData.id,
                        firstName: staffData.user_first_name ? staffData.user_first_name : userData.firstName,
                        lastName: staffData.user_last_name ? staffData.user_last_name : userData.lastName,
                        email: staffData.user_email ? staffData.user_email : userData.email,
                        emailVerified: userData.emailVerified,
                        attributes: {
                            userCountryCode: userData.attributes?.userCountryCode || "",
                            userPhoneNumber: staffData.user_phone_number ? staffData.user_phone_number : userData.userPhoneNumber,
                            organizationId: userData.attributes?.organizationId || "",
                            userStatus: userData.attributes?.userStatus,
                            isLoginpin: !!staffData.is_login_pin ? 1 : 0,
                            userToken: userData.attributes?.userToken || "",
                            updatedBy: userData.attributes?.updatedBy || "",
                            createdBy: userData.attributes?.createdBy || "",
                            otp: "",
                            otpExpireTime: ""
                        },
                    }

                    /** check if request is for set login pin then update it 0 to 1 */
                    if (staffData && staffData.is_login_pin) {
                        user.attributes.isLoginpin = 1;
                    }
                    /** Update User auth token and status */
                    let updateUserData: any = await updateUsers(user);
                    if (updateUserData.status == false) {
                        return {
                            status: updateUserData.status,
                            message: updateUserData.statusText,
                            data: null
                        };
                    }
                }
                break;
            case 'staff_reinvite':
                if (staffData.keycloak_auth_id) {
                    var passwordData: any = await resetPassword(staffData.keycloak_auth_id, staffData.user_password)
                    if (passwordData.status == false) {
                        return {
                            status: passwordData.status,
                            message: passwordData.statusText,
                            data: null
                        };
                    }
                    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${staffData.keycloak_auth_id}`;
                    let userData: any = await getData(keycloakRealmUrl);
                    if (userData.status == false) {
                        return {
                            status: userData.status,
                            message: userData.message,
                            data: userData.statusText
                        };
                    }
                    userData = userData?.data
                    username = userData.username
                    if (userData.email) {
                        /** Prepare message for queue */
                        const message = {
                            staffData,
                            username: username,
                            organization_id: userData.attributes?.organizationId
                        };
                        /** Publish a message to the "staff creation email" queue */
                        const queue: any = RABBITMQ_QUEUE.STAFF_CREATION;
                        await rabbitmqPublisher.publishMessage(queue, message);
                    }
                }
                break;
            case 'staff_delete':
                if (staffData.keycloak_auth_id) {
                    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${staffData.keycloak_auth_id}`;
                    let getUsersData: any = await getData(keycloakRealmUrl);
                    getUsersData = getUsersData.data


                    /** Prepare User Update object */
                    let user: any = {
                        username: getUsersData.username,
                        userId: getUsersData.id,
                        firstName: getUsersData.firstName,
                        lastName: getUsersData.lastName,
                        email: getUsersData.email,
                        emailVerified: true,
                        attributes: {
                            userCountryCode: getUsersData.attributes?.userCountryCode || "\"\"",
                            userPhoneNumber: getUsersData.attributes?.userPhoneNumber || "\"\"",
                            organizationId: getUsersData.attributes?.organizationId || "\"\"",
                            userStatus: "deleted",
                            isLoginpin: getUsersData.attributes?.isLoginpin || "0",
                            userToken: getUsersData.attributes?.userToken || "\"\"",
                            createdBy: getUsersData.attributes?.createdBy,
                            updatedBy: getUsersData.attributes?.updatedBy, // Updated by current user
                            otp: "",
                            otpExpireTime: ""
                        },
                    }
                    /** Update User auth token and status */
                    let updateUserData: any = await updateUsers(user);

                    if (updateUserData.status == false) {
                        return {
                            status: updateUserData.status,
                            message: updateUserData.message,
                            data: updateUserData.statusText
                        };
                    }
                }
                break;
            default:
                /** check queue is for create staff data */
                if (staffData.organization_id) {
                    /** Make URL for get organization data. */
                    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${staffData.organization_id}`;
                    /** Fetch organization name */
                    let getOrgData: any = await getData(keycloakRealmUrl, null)
                    if (getOrgData.status == false) {
                        return {
                            status: getOrgData.status,
                            message: getOrgData.message,
                            data: getOrgData.statusText
                        };
                    }
                    getOrgData = getOrgData?.data
                    if (getOrgData) {
                        /** Prepare staff user name for combination of below fields
                         *  organization_name, first_name, last_name, random_number
                         */
                        username = `${getOrgData.name.slice(0, 2)}.${staffData.user_first_name.replace(/\s+/g, '').slice(0, 10)}.${staffData.user_last_name.slice(0, 10)}`
                        username = await generateUniqueUsername(username)

                    }
                }
                if (staffData) {
                    const userPayload: any = {
                        username: username, // Need to discuss
                        enabled: staffData.enabled ?? true,
                        emailVerified: false,
                        firstName: staffData.user_first_name,
                        lastName: staffData.user_last_name,
                        email: staffData.user_email,
                        userCountryCode: staffData.userCountryCode,
                        userPhoneNumber: staffData.user_phone_number,
                        isLoginpin: 0,
                        webAppToken: "",
                        appToken: "",
                        organization_id: staffData.organization_id,
                        createdBy: staffData.keycloak_auth_id ?? "",
                        updatedBy: staffData.keycloak_auth_id ?? "",
                        userStatus: "pending",
                        userToken: "\"\""
                    };
                    let userData: any = await createUsers(userPayload);
                    if (userData.status == false) {
                        return {
                            status: userData.status,
                            message: userData.statusText,
                            data: null
                        };
                    }
                    if (userData.data.id) {
                        if (staffData.randomPassword) {
                            var passwordData: any = await resetPassword(userData.data.id, staffData.randomPassword)
                            if (passwordData.status == false) {
                                return {
                                    status: userData.status,
                                    message: userData.statusText,
                                    data: null
                                };
                            }
                        }
                        /** Get staff role to user */
                        const realmRoles: any = await getRealmsRoles();
                        if (realmRoles.status == false) {
                            return {
                                status: realmRoles.status,
                                message: realmRoles.message,
                                data: realmRoles.statusText
                            }
                        }

                        /** check user staff role exist */
                        const masterRole: any = realmRoles.data.find((role: any) => role.name === global.config.KEYCLOAK_STAFF_ROLE &&
                            role.description === global.config.KEYCLOAK_STAFF_ROLE_DESCRIPTION);

                        /** if staff role not found */
                        if (!masterRole) {
                            return {
                                status: false,
                                message: "Staff role not found",
                                data: null
                            }
                        }
                        /** Assign staff role to user */
                        await assignRolesToUser(userData.data.id, [masterRole])

                        /** get predefined role of keycloak. */
                        let getAllRealmRoles: any = await getPredefinedRealmRoles(userData.data.id)

                        /** check if master role exists */
                        const getRealmRole: any = getAllRealmRoles.data.find((role: any) => role.role === global.config.KEYCLOAK_REALM_ROLE);

                        /** if master role not found */
                        if (!getRealmRole) {
                            return {
                                status: false,
                                message: "Master role not found",
                                data: null
                            }
                        }
                        /** prepare predefined realm role object */
                        let predefinedObject = {
                            id: getRealmRole.id,
                            name: getRealmRole.role,
                            description: getRealmRole.description
                        }
                        /** Assign predefined realm role to user for access their token. */
                        let assignUserRole: any = await assignPredefinedRoleToUser(userData.data.id, [predefinedObject], getRealmRole.clientId)
                        if (assignUserRole.status == false) {
                            return {
                                status: assignUserRole.status,
                                message: assignUserRole.message,
                                data: null
                            }
                        }

                        /**  Join User to Organization */
                        let joinOrganization: any = await joinUserToOrg(staffData.organization_id, userData.data.id)
                        if (joinOrganization.status == false) {
                            return {
                                status: joinOrganization.status,
                                message: joinOrganization.message,
                                data: null
                            }
                        }

                        /** Handle email configuration if user_email exists */
                        if (staffData.user_email) {
                            /** Prepare message for queue */
                            const message = {
                                password: staffData.randomPassword,
                                staffUserId: staffData.userId,
                                adminId: staffData.adminId,
                                username: username
                            };

                            /** Publish a message to the "staff creation email" queue */
                            const queue: any = RABBITMQ_QUEUE.STAFF_CREATION;
                            await rabbitmqPublisher.publishMessage(queue, message);
                        }
                        /** If staff create successfully then publish new queue. */
                        let message = {
                            keycloak_auth_id: userData.data.id,
                            userId: staffData.userId,
                            username: username
                        }
                        const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_SUCCESS;
                        await rabbitmqPublisher.publishMessage(queue, message);

                        /** stored plan data for purchased public payment */
                        let queueMessage: any = {
                            staff_user_id: userData.data.id,
                            admin_user_id: staffData.keycloak_auth_id,
                            user_email: staffData.user_email
                        }
                        const newQueue: any = RABBITMQ_QUEUE.STORE_STAFF_DATA_FOR_PURCHASED_PLAN;
                        await rabbitmqPublisher.publishMessage(newQueue, queueMessage);

                    }
                }
                break;
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

/** consumer staff generate password queue and update into keycloak. */
const staffGeneratePassword = async (staffPasswordData: any) => {
    try {
        staffPasswordData = staffPasswordData.staffResponse
        if (staffPasswordData.type == 'generate_password') {
            let passwordData: any = await resetPassword(staffPasswordData.user_id, staffPasswordData.user_password)
            if (passwordData.status == false) {
                return {
                    status: passwordData.status,
                    message: passwordData.message,
                    data: passwordData.statusText
                };
            }
        }
    } catch (e: any) {
        console.error('Error in forgotPasswordService:', e);
        return { status: false, message: e }
    }
}

export { forgotPasswordService, resendOTPService, mailSuccessFailedService, staffCreationDetails, staffGeneratePassword }