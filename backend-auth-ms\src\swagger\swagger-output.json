{"openapi": "3.0.0", "info": {"title": "Express API", "version": "1.0.0", "description": "API documentation for the Express app"}, "servers": [{"url": "http://localhost:9023", "description": "Local development server"}, {"url": "https://staging.namastevillage.theeasyaccess.com", "description": "staging server"}], "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "paths": {"/v1/private/auth/change-password": {"put": {"summary": "Change user password", "description": "Allows an authenticated user to change their password.", "tags": ["<PERSON><PERSON>"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["password"], "properties": {"password": {"type": "string", "description": "The new password for the user."}}}}}}, "responses": {"200": {"description": "Password successfully changed.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password changed successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "401": {"description": "Unauthorized, JWT token is missing or invalid.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Unauthorized."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/private/auth/create-staff": {"post": {"summary": "Create a new staff member", "description": "Allows an admin to create a new staff member by providing details such as first name, last name, email, etc.", "tags": ["<PERSON><PERSON>"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["firstName", "lastName", "username", "email", "organization_id", "role"], "properties": {"firstName": {"type": "string", "description": "The first name of the staff member."}, "lastName": {"type": "string", "description": "The last name of the staff member."}, "username": {"type": "string", "description": "The username for the staff member (must be unique)."}, "email": {"type": "string", "format": "email", "description": "The email address of the staff member (must be unique)."}, "organization_id": {"type": "string", "description": "The unique identifier of the organization to which the staff member belongs."}, "role": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the role."}, "name": {"type": "string", "description": "The name of the role."}, "description": {"type": "string", "description": "A brief description of the role."}, "composite": {"type": "boolean", "description": "Whether the role is a composite role."}, "clientRole": {"type": "boolean", "description": "Whether the role is a client role."}, "containerId": {"type": "string", "description": "The unique identifier of the container that holds the role."}}}}}}}}}, "responses": {"201": {"description": "Staff member successfully created.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Staff member created successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "401": {"description": "Unauthorized, JWT token is missing or invalid.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Unauthorized."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/private/auth/update-user": {"put": {"summary": "Update user data", "description": "This API accepts form data to upload user details.", "tags": ["<PERSON><PERSON>"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"userId": {"type": "string", "description": "Unique identifier for the user.", "example": "12345"}, "firstName": {"type": "string", "description": "The first name of the user.", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "The last name of the user.", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "description": "The email address of the user.", "example": "<EMAIL>"}, "userStatus": {"type": "string", "description": "The status of the user.", "example": "active"}, "userPhoneNumber": {"type": "string", "description": "The phone number of the user.", "example": "**********"}, "userCountryCode": {"type": "string", "description": "The country code of the user's phone number.", "example": "3"}, "organizationId": {"type": "string", "description": "The ID of the organization the user belongs to.", "example": "6ed78ffd-629b-4779-b0cc-f8c3a5c2d66c"}, "userToken": {"type": "string", "description": "A unique token for the user.", "example": ""}, "isLoginpin": {"type": "boolean", "description": "Indicates if the user has a login PIN.", "example": false}, "updatedBy": {"type": "string", "description": "The ID of the user who created this record.", "example": "6ed78ffd-629b-4779-b0cc-f8c3a5c2d66c"}, "userSignature": {"type": "string", "format": "binary", "description": "The user's signature file."}}}}}}, "responses": {"200": {"description": "User data successfully uploaded."}, "400": {"description": "Bad request."}}}}, "/v1/private/organization/get-org/{orgId}": {"get": {"summary": "Get organization details by ID", "description": "Fetches the details of an organization by its unique ID. Requires a JWT token.", "tags": ["Organization"], "security": [{"BearerAuth": []}], "parameters": [{"name": "orgId", "in": "path", "required": true, "description": "The unique identifier of the organization.", "schema": {"type": "string", "example": "cd6ce8bb-73cf-45b8-9ae8-5403c2b83b45"}}], "responses": {"200": {"description": "Organization details successfully fetched.", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the organization.", "example": "cd6ce8bb-73cf-45b8-9ae8-5403c2b83b45"}, "name": {"type": "string", "description": "The name of the organization.", "example": "My Organization"}, "description": {"type": "string", "description": "A brief description of the organization.", "example": "A company focused on innovation."}}}}}}, "400": {"description": "Bad request, invalid organization ID.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid organization ID."}}}}}}, "404": {"description": "Organization not found.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Organization not found."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/private/organization/update-organization": {"put": {"summary": "Update user data", "description": "This API accepts form data to upload user details.", "tags": ["<PERSON><PERSON>"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the organization.", "example": "cd6ce8bb-73cf-45b8-9ae8-5403c2b83b45"}, "name": {"type": "string", "description": "The name of the organization.", "example": "<PERSON>"}, "description": {"type": "string", "description": "The description of the organization.", "example": "this is my organization data"}, "multiple_location": {"type": "string", "description": "The multiple location of the organization.", "example": 2}, "email": {"type": "string", "description": "The email address of the user.", "example": "<EMAIL>"}, "contact_person": {"type": "string", "description": "The contact person of organization.", "example": ************}, "website": {"type": "string", "description": "The domain of organization.", "example": "jnext.com"}, "address": {"type": "string", "description": "The address of organization.", "example": "India"}, "vat_number": {"type": "string", "description": "The vat number of organization.", "example": "418597663"}, "country": {"type": "string", "description": "The country of organization.", "example": 3}, "currency": {"type": "string", "description": "The currency of organization.", "example": "INR"}, "timezone": {"type": "string", "description": "The timezone of organization.", "example": "IST"}, "facebook_url": {"type": "string", "description": "The facebook_url of organization.", "example": "facebook.com"}, "linkdin_url": {"type": "string", "description": "The linkdin_url of organization.", "example": "linkdin.com"}, "twitter_url": {"type": "string", "description": "The twitter_url of organization.", "example": "twitter.com"}, "organization_logo": {"type": "string", "format": "binary", "description": "The organization's logo file."}, "created_at": {"type": "string", "description": "The register date of organization.", "example": "2024-12-25 09:34:39"}, "status": {"type": "string", "description": "The status of organization.", "example": "active"}}}}}}, "responses": {"200": {"description": "Organization data updated successfully."}, "400": {"description": "Bad request."}}}}, "/get-all-demo-requests": {"get": {"summary": "Get all demo requests", "description": "Fetches all demo requests from the system.", "tags": ["Demo Requests"], "responses": {"200": {"description": "Demo requests fetched successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Demo requests fetched successfully."}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "Test"}, "last_name": {"type": "string", "example": "User"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "mobile_number": {"type": "string", "example": "**********"}, "organization_name": {"type": "string", "example": "test_org"}, "number_of_employee": {"type": "integer", "example": 10}, "industry": {"type": "string", "example": "Hospitality"}, "help_description": {"type": "string", "nullable": true, "example": null}, "created_by": {"type": "string", "nullable": true, "example": null}, "updated_by": {"type": "string", "nullable": true, "example": null}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-02-07T10:20:07.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-02-07T10:20:07.000Z"}}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/v1/public/auth/org-user-register": {"post": {"summary": "Register a new user with organization data", "description": "This API allows registering a new user along with their associated organization details.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user", "organization"], "properties": {"user": {"type": "object", "required": ["firstName", "lastName", "userPhoneNumber", "userCountryCode", "username", "password", "email"], "properties": {"firstName": {"type": "string", "description": "The first name of the user.", "example": "hardik11"}, "lastName": {"type": "string", "description": "The last name of the user.", "example": "Jnext"}, "userPhoneNumber": {"type": "integer", "description": "The phone number of the user.", "example": 6354898289}, "userCountryCode": {"type": "integer", "description": "The country code for the phone number.", "example": 3}, "username": {"type": "string", "description": "The username for the user.", "example": "hardik_test11"}, "password": {"type": "string", "description": "The password for the user.", "example": "hardik@123"}, "email": {"type": "string", "description": "The email of the user.", "example": "<EMAIL>"}}}, "organization": {"type": "object", "required": ["name", "website", "email", "multiple_location", "description"], "properties": {"name": {"type": "string", "description": "The name of the organization.", "example": "test_org"}, "website": {"type": "string", "description": "The website for the organization.", "example": "https://jnextservices11.com/"}, "email": {"type": "string", "description": "The email for the organization.", "example": "<EMAIL>"}, "multiple_location": {"type": "integer", "description": "The number of locations for the organization.", "example": 2}, "redirectUrl": {"type": "string", "description": "The redirect URL for the organization (nullable).", "example": null}, "description": {"type": "string", "description": "A brief description of the organization.", "example": "this is my org data"}}}}}}}}, "responses": {"200": {"description": "Verification email has been sent to your registered email address, please check your inbox..", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Verification email has been sent to your registered email address, please check your inbox"}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/verify-auth-token": {"put": {"summary": "Verify authentication token", "description": "This API verifies the authentication token provided by the user, including user credentials like email and password.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["userId"], "properties": {"userId": {"type": "string", "description": "The unique identifier of the user.", "example": "9fb912c8-e6ef-422a-8ca2-948a65512e7e"}}}}}}, "responses": {"200": {"description": "Data updated successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Data updated successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "401": {"description": "Unauthorized, invalid user credentials or token.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Unauthorized, invalid credentials."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/get-user-data/{userId}": {"get": {"summary": "Get user data by user ID", "description": "This API allows you to retrieve user data using a user ID.", "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "path", "name": "userId", "required": true, "description": "The unique identifier of the user.", "schema": {"type": "string", "example": "9fb912c8-e6ef-422a-8ca2-948a65512e7e"}}], "responses": {"200": {"description": "Successfully retrieved user data.", "content": {"application/json": {"schema": {"type": "object", "properties": {"firstName": {"type": "string", "example": "<PERSON>"}, "lastName": {"type": "string", "example": "<PERSON><PERSON>"}, "username": {"type": "string", "example": "johndoe"}, "email": {"type": "string", "example": "<EMAIL>"}, "userPhoneNumber": {"type": "integer", "example": **********}}}}}}, "400": {"description": "Bad request, invalid user ID format.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid user ID format."}}}}}}, "404": {"description": "User not found with the given user ID.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "User not found."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/login": {"post": {"summary": "User login", "description": "This API endpoint allows a user to log in using their username and password.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["username", "password"], "properties": {"username": {"type": "string", "description": "The username of the user.", "example": "hitesh_test"}, "password": {"type": "string", "description": "The password of the user.", "example": "hitesh@123"}}}}}}, "responses": {"200": {"description": "Successful login, returns a JWT token.", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string", "description": "The JWT token for the logged-in user.", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiYjY4N2Y5ZTgtYjYwZC00OTI0LWI2YmUtNzU0ZTgwOGEzODI0In0.tgMIepQxoGTt34l5bniIUJgFiBaGBd33xyWpbLjQ5K0"}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "401": {"description": "Unauthorized, invalid username or password.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Unauthorized, invalid credentials."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/forgot-password": {"post": {"summary": "Request password reset", "description": "This API endpoint allows a user to request a password reset by providing their email or user ID.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email"], "properties": {"email": {"type": "string", "description": "The email address associated with the user's account.", "example": "<EMAIL>"}}}}}}, "responses": {"200": {"description": "Password reset request was successful.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password reset link sent to the provided email."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "404": {"description": "User not found with the provided email or user ID.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "User not found."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/resend-otp": {"post": {"summary": "Resend OTP for authentication", "description": "This API endpoint allows a user to request a resend of the OTP for authentication by providing their user ID and email address.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["userId"], "properties": {"userId": {"type": "string", "description": "The unique identifier of the user requesting the OTP resend.", "example": "9fb912c8-e6ef-422a-8ca2-948a65512e7e"}}}}}}, "responses": {"200": {"description": "OTP has been successfully resent.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OTP resent successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "404": {"description": "User not found with the provided email or user ID.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "User not found."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/reset-password": {"put": {"summary": "Reset user password", "description": "This API endpoint allows a user to reset their password by providing their user ID and new password.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["userId", "password"], "properties": {"userId": {"type": "string", "description": "The unique identifier for the user.", "example": "9fb912c8-e6ef-422a-8ca2-948a65512e7e"}, "password": {"type": "string", "description": "The new password for the user.", "example": "test@123"}}}}}}, "responses": {"200": {"description": "Password successfully reset.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password reset successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "404": {"description": "User not found with the provided user ID.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "User not found."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/verify-otp": {"post": {"summary": "Verify OTP for authentication", "description": "This API endpoint allows a user to verify the OTP received for authentication by providing their user ID and the OTP.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["userId", "otp"], "properties": {"userId": {"type": "string", "description": "The unique identifier of the user for OTP verification.", "example": "9fb912c8-e6ef-422a-8ca2-948a65512e7e"}, "otp": {"type": "string", "description": "The OTP provided to the user for verification.", "example": "1476"}}}}}}, "responses": {"200": {"description": "OTP successfully verified.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OTP verified successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "401": {"description": "Unauthorized, incorrect OTP or expired OTP.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid OTP."}}}}}}, "404": {"description": "User not found with the provided user ID.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "User not found."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/timezone-list": {"get": {"summary": "Get the list of available timezones", "description": "This API endpoint returns a list of available timezones for the user.", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "A list of available timezones.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "The timezone code (e.g., UTC, GMT, EST).", "example": "UTC"}, "name": {"type": "string", "description": "The name of the timezone (e.g., Coordinated Universal Time).", "example": "Coordinated Universal Time"}, "offset": {"type": "string", "description": "The offset of the timezone from UTC (e.g., +00:00, -05:00).", "example": "+00:00"}}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/currency-list": {"get": {"summary": "Get the list of available currencies", "description": "This API endpoint returns a list of available currencies for the user.", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "A list of available currencies.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "description": "The currency code (e.g., USD, EUR).", "example": "USD"}, "name": {"type": "string", "description": "The name of the currency (e.g., United States Dollar).", "example": "United States Dollar"}, "symbol": {"type": "string", "description": "The symbol of the currency (e.g., $, €).", "example": "$"}}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/get-realm-roles": {"get": {"summary": "Get list of realm roles", "description": "This API endpoint fetches a list of all available realm roles in the system.", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "List of realm roles successfully retrieved.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the role.", "example": "123e4567-e89b-12d3-a456-426614174000"}, "name": {"type": "string", "description": "The name of the role.", "example": "admin"}, "description": {"type": "string", "description": "A brief description of the role.", "example": "Administrator with full access"}}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/country-list": {"get": {"summary": "Get list of countries", "description": "This API endpoint fetches a list of all available countries.", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "List of countries successfully retrieved.", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"countryCode": {"type": "string", "description": "The country code.", "example": "IN"}, "countryName": {"type": "string", "description": "The name of the country.", "example": "India"}}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/get-access-token": {"post": {"summary": "Get access token From refresh token", "description": "This API endpoint allows a to get new access token from the refresh token", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "description": "The Refresh Token.", "example": "eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJjOWJjZGQ1MS1lYzM1LTRjNzMtOWY3OC0xNDViN2UxN2RiYmQifQ.eyJleHAiOjE3MzQzNTEyMzMsImlhdCI6MTczNDM0OTQzMywianRpIjoiNDE3ZDlhN2ItODcxZC00OGUwLWJiMDItN2ZmMjA3YzA0ZmEwIiwiaXNzIjoiaHR0cDovL2xvY2FsaG9zdDo4ODkwL3JlYWxtcy9vcmdhIiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdDo4ODkwL3JlYWxtcy9vcmdhIiwic3ViIjoiNmVkNzhmZmQtNjI5Yi00Nzc5LWIwY2MtZjhjM2E1YzJkNjZjIiwidHlwIjoiUmVmcmVzaCIsImF6cCI6Im5vZGUtYmFja2VuZCIsInNpZCI6ImY2MjViMDNkLWEyNzctNGZkMi04MDVkLWFiZTAyMzgwNWQ2NCIsInNjb3BlIjoiYWNyIHJvbGVzIHdlYi1vcmlnaW5zIGVtYWlsIHByb2ZpbGUgYmFzaWMifQ.8yUixARBkOSYAovhpd30TJwCc9-_oQnxX9bi1Re653s9UiG_kgmvvrWlM8srNg9dwVI1fzZQ2dwQ9_r5Ug_AIw"}}}}}}, "responses": {"200": {"description": "Data fetched successfully..", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Data fetched successfully."}}}}}}, "400": {"description": "Bad request, missing or invalid fields.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid request payload."}}}}}}, "401": {"description": "Unauthorized, Refresh token is not valid.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid refresh token."}}}}}}, "500": {"description": "Internal server error.", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Something went wrong."}}}}}}}}}, "/v1/public/auth/resend-email": {"post": {"summary": "Resend verification email", "description": "Resend the verification email to the user.", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["userId", "email", "isUpdateEmail"], "properties": {"userId": {"type": "string", "example": "b2f566e9-c7d1-437b-a477-0f7467828818", "description": "Unique ID of the user"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email address of the user"}, "isUpdateEmail": {"type": "boolean", "example": true, "description": "Flag to indicate if email needs to be updated"}}}}}}, "responses": {"200": {"description": "Verification email sent successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Verification email sent successfully."}}}}}}, "400": {"description": "Bad request. Missing or invalid parameters."}, "500": {"description": "Internal server error."}}}}, "/v1/public/demo-request/add-demo-request": {"post": {"summary": "Request a demo", "description": "API to request a demo for a user.", "tags": ["Demo Requests"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["first_name", "last_name", "email", "mobile_number", "organization_name", "number_of_employee", "industry"], "properties": {"first_name": {"type": "string", "example": "Test"}, "last_name": {"type": "string", "example": "User"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "mobile_number": {"type": "string", "example": "**********"}, "organization_name": {"type": "string", "example": "test_org"}, "number_of_employee": {"type": "integer", "example": 10}, "industry": {"type": "string", "example": "Hospitality"}}}}}}, "responses": {"200": {"description": "Request submitted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Demo request submitted successfully."}}}}}}, "400": {"description": "Invalid request data"}, "500": {"description": "Internal server error"}}}}, "/v1/public/demo-request/get-all-demo-requests": {"get": {"summary": "Get all demo requests", "tags": ["Demo Requests"], "responses": {"200": {"description": "Demo requests fetched successfully.", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Demo requests fetched successfully."}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "Test"}, "last_name": {"type": "string", "example": "Test"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile_number": {"type": "string", "example": 123465798}, "organization_name": {"type": "string", "example": "NV"}, "number_of_employee": {"type": "integer", "example": 10}, "industry": {"type": "string", "example": "Hospitality"}, "help_description": {"type": "string", "nullable": true, "example": null}, "created_by": {"type": "string", "nullable": true, "example": null}, "updated_by": {"type": "string", "nullable": true, "example": null}, "createdAt": {"type": "string", "format": "date-time", "example": "2025-04-09T11:38:49.000Z"}, "updatedAt": {"type": "string", "format": "date-time", "example": "2025-04-09T11:38:49.000Z"}}}}}}}}}}}}}, "tags": []}