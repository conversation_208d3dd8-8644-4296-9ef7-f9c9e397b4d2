#!/bin/bash

# Enhanced Customer Support System - Startup Script
echo "🚀 Starting Enhanced Customer Support System..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are available"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Check if database is configured
if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "⚠️  Database environment variables not set. Please configure:"
    echo "   - DB_HOST"
    echo "   - DB_NAME" 
    echo "   - DB_USER"
    echo "   - DB_PASS"
    echo "   - JWT_SECRET"
    echo ""
    echo "You can set them in a .env file or export them as environment variables."
fi

# Run database migrations
echo "🗄️  Running database migrations..."
npm run db:migrate

if [ $? -ne 0 ]; then
    echo "⚠️  Database migrations failed. Please check your database configuration."
    echo "   Make sure your database is running and accessible."
else
    echo "✅ Database migrations completed"
fi

# Build the application
echo "🔨 Building application..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Application built successfully"

# Start the application
echo "🎯 Starting Enhanced Customer Support System..."
echo ""
echo "📋 System Features:"
echo "   ✅ Multi-tenant support ticket management"
echo "   ✅ Role-based access control (Public, Agent, Admin)"
echo "   ✅ Advanced filtering and search capabilities"
echo "   ✅ Bulk operations for administrative efficiency"
echo "   ✅ File attachment support"
echo "   ✅ SLA tracking and monitoring"
echo "   ✅ Complete audit trails"
echo "   ✅ Comprehensive dashboard with metrics"
echo ""
echo "🌐 API Endpoints will be available at:"
echo "   Public:  http://localhost:5008/api/public/support/*"
echo "   Private: http://localhost:5008/api/private/support/*"
echo "   Admin:   http://localhost:5008/api/private/admin/*"
echo ""
echo "📖 Documentation: http://localhost:5008/api-docs"
echo "🏥 Health Check: http://localhost:5008/health"
echo ""
echo "🚀 Starting server..."

npm start
