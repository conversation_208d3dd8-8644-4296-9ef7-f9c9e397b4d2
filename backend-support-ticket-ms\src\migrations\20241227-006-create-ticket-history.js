"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("mo_support_ticket_history", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      ticket_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "mo_support_tickets",
          key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        comment: "Reference to the support ticket",
      },
      action_type: {
        type: Sequelize.ENUM(
          "CREATED",
          "UPDATED",
          "ASSIGNED",
          "UNASSIGNED",
          "STATUS_CHANGED",
          "PRIORITY_CHANGED",
          "RESOLVED",
          "CLOSED",
          "REOPENED",
          "RATED",
          "COMMENT_ADDED",
          "ATTACHMENT_ADDED",
          "ATTACHMENT_REMOVED"
        ),
        allowNull: false,
        comment: "Type of action performed on the ticket",
      },
      field_name: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: "Name of the field that was changed (for UPDATE actions)",
      },
      old_value: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Previous value before the change",
      },
      new_value: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "New value after the change",
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Human-readable description of the action",
      },
      is_system_action: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether this was an automated system action",
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: "ID of user who performed the action",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex("mo_support_ticket_history", ["ticket_id"], {
      name: "idx_ticket_history_ticket_id",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["action_type"], {
      name: "idx_ticket_history_action_type",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["created_by"], {
      name: "idx_ticket_history_created_by",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["created_at"], {
      name: "idx_ticket_history_created_at",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["is_system_action"], {
      name: "idx_ticket_history_is_system",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["field_name"], {
      name: "idx_ticket_history_field_name",
    });

    // Composite indexes for common queries
    await queryInterface.addIndex("mo_support_ticket_history", ["ticket_id", "action_type"], {
      name: "idx_ticket_history_ticket_action",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["ticket_id", "created_at"], {
      name: "idx_ticket_history_ticket_created",
    });

    await queryInterface.addIndex("mo_support_ticket_history", ["ticket_id", "is_system_action"], {
      name: "idx_ticket_history_ticket_system",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_ticket_id");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_action_type");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_created_by");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_created_at");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_is_system");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_field_name");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_ticket_action");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_ticket_created");
    await queryInterface.removeIndex("mo_support_ticket_history", "idx_ticket_history_ticket_system");

    // Remove table
    await queryInterface.dropTable("mo_support_ticket_history");
  },
};
