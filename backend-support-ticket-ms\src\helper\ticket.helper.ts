import { QueryTypes } from "sequelize";
import { sequelize } from "../models";

/**
 * Get ticket by ID using raw query with user details (following recipe-ms pattern)
 * @param ticketId - Ticket ID
 * @param organizationId - Organization ID for filtering
 * @returns Complete ticket data with user information
 */
export const getTicketByIdRaw = async (
  ticketId: number,
  organizationId: string
): Promise<any> => {
  try {
    const baseUrl = global.config?.API_UPLOAD_URL || "";

    const query = `
      SELECT
        t.*,
        -- Ticket owner details from nv_users
        owner.user_first_name as owner_first_name,
        owner.user_last_name as owner_last_name,
        owner.user_email as owner_email,
        owner.user_phone_number as owner_phone,
        CONCAT(owner.user_first_name, ' ', owner.user_last_name) as owner_full_name,

        -- Assigned user details
        assigned.user_first_name as assigned_first_name,
        assigned.user_last_name as assigned_last_name,
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,

        -- Resolved by user details
        resolved.user_first_name as resolved_first_name,
        resolved.user_last_name as resolved_last_name,
        resolved.user_email as resolved_email,
        CONCAT(resolved.user_first_name, ' ', resolved.user_last_name) as resolved_full_name,

        -- Created by user details
        creator.user_first_name as creator_first_name,
        creator.user_last_name as creator_last_name,
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name,

        -- Rated by user details
        rater.user_first_name as rater_first_name,
        rater.user_last_name as rater_last_name,
        rater.user_email as rater_email,
        CONCAT(rater.user_first_name, ' ', rater.user_last_name) as rater_full_name
        
      FROM mo_support_tickets t
      LEFT JOIN nv_users owner ON t.ticket_owner_user_id = owner.id
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users resolved ON t.resolved_by_user_id = resolved.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      LEFT JOIN nv_users rater ON t.rated_by_user_id = rater.id
      WHERE t.id = :ticketId 
        AND t.organization_id = :organizationId
        AND t.deleted_at IS NULL
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: { ticketId, organizationId },
    });

    if (!result || result.length === 0) {
      return null;
    }

    return result[0];
  } catch (error) {
    console.error("Error in getTicketByIdRaw:", error);
    throw error;
  }
};

/**
 * Get ticket by slug using raw query with user details (following recipe-ms pattern)
 * @param ticketSlug - Ticket slug
 * @param organizationId - Organization ID for filtering
 * @returns Complete ticket data with user information
 */
export const getTicketBySlugRaw = async (
  ticketSlug: string,
  organizationId: string
): Promise<any> => {
  try {
    const query = `
      SELECT
        t.*,
        -- Ticket owner details from nv_users
        owner.user_first_name as owner_first_name,
        owner.user_last_name as owner_last_name,
        owner.user_email as owner_email,
        owner.user_phone_number as owner_phone,
        CONCAT(owner.user_first_name, ' ', owner.user_last_name) as owner_full_name,

        -- Assigned user details
        assigned.user_first_name as assigned_first_name,
        assigned.user_last_name as assigned_last_name,
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,

        -- Resolved by user details
        resolved.user_first_name as resolved_first_name,
        resolved.user_last_name as resolved_last_name,
        resolved.user_email as resolved_email,
        CONCAT(resolved.user_first_name, ' ', resolved.user_last_name) as resolved_full_name,

        -- Created by user details
        creator.user_first_name as creator_first_name,
        creator.user_last_name as creator_last_name,
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name,

        -- Rated by user details
        rater.user_first_name as rater_first_name,
        rater.user_last_name as rater_last_name,
        rater.user_email as rater_email,
        CONCAT(rater.user_first_name, ' ', rater.user_last_name) as rater_full_name

      FROM mo_support_tickets t
      LEFT JOIN nv_users owner ON t.ticket_owner_user_id = owner.id
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users resolved ON t.resolved_by_user_id = resolved.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      LEFT JOIN nv_users rater ON t.rated_by_user_id = rater.id
      WHERE t.ticket_slug = :ticketSlug
        AND t.organization_id = :organizationId
        AND t.deleted_at IS NULL
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: { ticketSlug, organizationId },
    });

    if (!result || result.length === 0) {
      return null;
    }

    return result[0];
  } catch (error) {
    console.error("Error in getTicketBySlugRaw:", error);
    throw error;
  }
};

/**
 * Get tickets list using raw query with pagination (following recipe-ms pattern)
 * @param organizationId - Organization ID for filtering
 * @param options - Query options (limit, offset, search, filters)
 * @returns Paginated tickets list with user information
 */
export const getTicketsListRaw = async (
  organizationId: string,
  options: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
    priority?: string;
    type?: string;
    module?: string;
    assigned_to?: number;
    created_by?: number;
  } = {}
): Promise<{ tickets: any[]; total: number }> => {
  try {
    const {
      limit,
      offset,
      search,
      status,
      priority,
      type,
      module,
      assigned_to,
      created_by,
    } = options;

    // Build WHERE conditions
    const whereConditions = [
      "t.organization_id = :organizationId",
      "t.deleted_at IS NULL",
    ];
    const replacements: any = { organizationId };

    if (search) {
      whereConditions.push(
        "(t.ticket_title LIKE :search OR t.ticket_description LIKE :search OR t.ticket_slug LIKE :search)"
      );
      replacements.search = `%${search}%`;
    }

    if (status) {
      whereConditions.push("t.ticket_status = :status");
      replacements.status = status;
    }

    if (priority) {
      whereConditions.push("t.ticket_priority = :priority");
      replacements.priority = priority;
    }

    if (type) {
      whereConditions.push("t.ticket_type = :type");
      replacements.type = type;
    }

    if (module) {
      whereConditions.push("t.ticket_module = :module");
      replacements.module = module;
    }

    if (assigned_to) {
      whereConditions.push("t.assigned_to_user_id = :assigned_to");
      replacements.assigned_to = assigned_to;
    }

    if (created_by) {
      whereConditions.push("t.created_by_user_id = :created_by");
      replacements.created_by = created_by;
    }

    const whereClause = whereConditions.join(" AND ");

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM mo_support_tickets t
      WHERE ${whereClause}
    `;

    const countResult = await sequelize.query(countQuery, {
      type: QueryTypes.SELECT,
      replacements,
    });

    const total = (countResult[0] as any).total;

    // Main query with user details
    let mainQuery = `
      SELECT
        t.*,
        -- Ticket owner details
        owner.user_first_name as owner_first_name,
        owner.user_last_name as owner_last_name,
        owner.user_email as owner_email,
        CONCAT(owner.user_first_name, ' ', owner.user_last_name) as owner_full_name,

        -- Assigned user details
        assigned.user_first_name as assigned_first_name,
        assigned.user_last_name as assigned_last_name,
        assigned.user_email as assigned_email,
        CONCAT(assigned.user_first_name, ' ', assigned.user_last_name) as assigned_full_name,

        -- Created by user details
        creator.user_first_name as creator_first_name,
        creator.user_last_name as creator_last_name,
        creator.user_email as creator_email,
        CONCAT(creator.user_first_name, ' ', creator.user_last_name) as creator_full_name

      FROM mo_support_tickets t
      LEFT JOIN nv_users owner ON t.ticket_owner_user_id = owner.id
      LEFT JOIN nv_users assigned ON t.assigned_to_user_id = assigned.id
      LEFT JOIN nv_users creator ON t.created_by_user_id = creator.id
      WHERE ${whereClause}
      ORDER BY t.created_at DESC
    `;

    // Add pagination if provided
    if (limit) {
      mainQuery += ` LIMIT :limit`;
      replacements.limit = limit;

      if (offset) {
        mainQuery += ` OFFSET :offset`;
        replacements.offset = offset;
      }
    }

    const tickets = await sequelize.query(mainQuery, {
      type: QueryTypes.SELECT,
      replacements,
    });

    return { tickets, total };
  } catch (error) {
    console.error("Error in getTicketsListRaw:", error);
    throw error;
  }
};

/**
 * Generate unique ticket slug
 * @param organizationId - Organization ID
 * @returns Unique ticket slug
 */
export const generateTicketSlug = async (
  organizationId: string
): Promise<string> => {
  try {
    const year = new Date().getFullYear();
    const prefix = `TKT-${year}`;

    // Get the latest ticket number for this year and organization
    const query = `
      SELECT ticket_slug 
      FROM mo_support_tickets 
      WHERE organization_id = :organizationId 
        AND ticket_slug LIKE :pattern
      ORDER BY id DESC 
      LIMIT 1
    `;

    const result = await sequelize.query(query, {
      type: QueryTypes.SELECT,
      replacements: {
        organizationId,
        pattern: `${prefix}-%`,
      },
    });

    let nextNumber = 1;
    if (result && result.length > 0) {
      const lastSlug = (result[0] as any).ticket_slug;
      const lastNumber = parseInt(lastSlug.split("-").pop() || "0");
      nextNumber = lastNumber + 1;
    }

    return `${prefix}-${nextNumber.toString().padStart(3, "0")}`;
  } catch (error) {
    console.error("Error generating ticket slug:", error);
    throw error;
  }
};
