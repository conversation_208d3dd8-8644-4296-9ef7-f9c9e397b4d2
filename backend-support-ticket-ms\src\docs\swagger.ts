import swagger<PERSON>SD<PERSON> from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
import { Express } from "express";

const options: any = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "TTH Support Ticket Microservice API",
      version: "1.0.0",
      description: `
        TeamTrainHub Support Ticket Management System API
        
        This microservice provides comprehensive support ticket management functionality including:
        - Public ticket creation for customers
        - Internal ticket management for support agents
        - Messaging and conversation tracking
        - File attachment support via nv_items integration
        - Comprehensive audit trail and history tracking
        - Role-based access control and permissions
        
        ## Authentication
        Most endpoints require authentication via JWT token in the Authorization header:
        \`Authorization: Bearer <token>\`
        
        ## Organization Isolation
        All data is isolated by organization_id to ensure multi-tenant security.
        
        ## File Uploads
        File attachments are handled through the TTH file upload system and stored in the nv_items table.
        
        ## Error Handling
        All endpoints return standardized error responses with appropriate HTTP status codes.
      `,
      contact: {
        name: "TTH Support Team",
        email: "<EMAIL>",
      },
      license: {
        name: "Proprietary",
        url: "https://teamtrainhub.com/license",
      },
    },
    servers: [
      {
        url: process.env.API_BASE_URL || "http://localhost:5007",
        description: "Support Ticket Microservice",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT token for authenticated requests",
        },
        adminAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT token with admin privileges",
        },
      },
      schemas: {
        Error: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: false,
            },
            message: {
              type: "string",
              example: "Error message",
            },
            error_code: {
              type: "string",
              example: "ERROR_CODE",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2024-01-01T00:00:00.000Z",
            },
            path: {
              type: "string",
              example: "/api/tickets",
            },
            method: {
              type: "string",
              example: "POST",
            },
            details: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  field: { type: "string" },
                  message: { type: "string" },
                  value: { type: "string" },
                },
              },
            },
          },
        },
        SuccessResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Operation successful",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2024-01-01T00:00:00.000Z",
            },
            data: {
              type: "object",
              description: "Response data (varies by endpoint)",
            },
          },
        },
        PaginatedResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Data retrieved successfully",
            },
            data: {
              type: "array",
              items: {
                type: "object",
              },
            },
            pagination: {
              type: "object",
              properties: {
                current_page: { type: "integer", example: 1 },
                total_pages: { type: "integer", example: 10 },
                total_records: { type: "integer", example: 100 },
                per_page: { type: "integer", example: 10 },
              },
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2024-01-01T00:00:00.000Z",
            },
          },
        },
        Ticket: {
          type: "object",
          properties: {
            id: { type: "integer", example: 1 },
            ticket_number: { type: "string", example: "TTH-TKT-0001" },
            organization_id: { type: "string", example: "org_001" },
            submitter_name: { type: "string", example: "John Doe" },
            submitter_email: { type: "string", example: "<EMAIL>" },
            submitter_phone: { type: "string", example: "+1234567890" },
            subject: { type: "string", example: "Login Issue" },
            description: {
              type: "string",
              example: "Cannot login to the system",
            },
            ticket_status: {
              type: "string",
              enum: [
                "OPEN",
                "ASSIGNED",
                "IN_PROGRESS",
                "ON_HOLD",
                "QA_REVIEW",
                "UNDER_REVIEW",
                "ESCALATED",
                "RESOLVED",
                "CLOSED",
                "INVOICED",
              ],
              example: "OPEN",
            },
            priority: {
              type: "string",
              enum: ["NONE", "EMERGENCY", "URGENT", "HIGH", "MEDIUM", "LOW"],
              example: "MEDIUM",
            },
            module_type: {
              type: "string",
              enum: ["HRMS", "PMS", "OTHER"],
              example: "HRMS",
            },
            issue_type: {
              type: "string",
              enum: [
                "BUG",
                "FEATURE_REQUEST",
                "GENERAL_QUERY",
                "TECHNICAL",
                "NON_TECHNICAL",
                "EXPORT_HELP",
                "SUPPORT",
                "OTHER",
              ],
              example: "TECHNICAL",
            },
            assigned_to_user_id: {
              type: "integer",
              nullable: true,
              example: 2,
            },
            created_by: { type: "integer", example: 1 },
            updated_by: { type: "integer", example: 1 },
            resolved_by: { type: "integer", nullable: true, example: 2 },
            resolution_note: {
              type: "string",
              nullable: true,
              example: "Issue resolved by updating password",
            },
            rating: {
              type: "integer",
              nullable: true,
              minimum: 1,
              maximum: 5,
              example: 5,
            },
            review_comment: {
              type: "string",
              nullable: true,
              example: "Great support!",
            },
            sla_due_date: {
              type: "string",
              format: "date-time",
              nullable: true,
            },
            resolved_at: {
              type: "string",
              format: "date-time",
              nullable: true,
            },
            has_attachments: { type: "boolean", example: false },
            attachment_count: { type: "integer", example: 0 },
            attachment_item_ids: {
              type: "string",
              nullable: true,
              example: "[1,2,3]",
            },
            created_at: { type: "string", format: "date-time" },
            updated_at: { type: "string", format: "date-time" },
          },
        },
        TicketMessage: {
          type: "object",
          properties: {
            id: { type: "integer", example: 1 },
            ticket_id: { type: "integer", example: 1 },
            message_text: {
              type: "string",
              example: "Thank you for contacting support",
            },
            message_type: {
              type: "string",
              enum: ["USER", "AGENT", "SYSTEM", "INTERNAL_NOTE"],
              example: "AGENT",
            },
            is_private: { type: "boolean", example: false },
            attachment_id: { type: "integer", nullable: true, example: 1 },
            created_by: { type: "integer", example: 2 },
            updated_by: { type: "integer", nullable: true, example: 2 },
            created_at: { type: "string", format: "date-time" },
            updated_at: { type: "string", format: "date-time" },
          },
        },
        TicketHistory: {
          type: "object",
          properties: {
            id: { type: "integer", example: 1 },
            ticket_id: { type: "integer", example: 1 },
            action_type: {
              type: "string",
              enum: [
                "CREATED",
                "STATUS_CHANGED",
                "PRIORITY_CHANGED",
                "ASSIGNED",
                "UNASSIGNED",
                "MESSAGE_ADDED",
                "ATTACHMENT_ADDED",
                "ATTACHMENT_REMOVED",
                "ESCALATED",
                "RESOLVED",
                "CLOSED",
                "REOPENED",
                "UPDATED",
                "DELETED",
              ],
              example: "STATUS_CHANGED",
            },
            previous_status: {
              type: "string",
              nullable: true,
              example: "OPEN",
            },
            new_status: { type: "string", nullable: true, example: "ASSIGNED" },
            previous_priority: {
              type: "string",
              nullable: true,
              example: "LOW",
            },
            new_priority: { type: "string", nullable: true, example: "HIGH" },
            previous_assigned_to: {
              type: "integer",
              nullable: true,
              example: null,
            },
            new_assigned_to: { type: "integer", nullable: true, example: 2 },
            field_changed: {
              type: "string",
              nullable: true,
              example: "status",
            },
            old_value: { type: "string", nullable: true, example: "OPEN" },
            new_value: { type: "string", nullable: true, example: "ASSIGNED" },
            change_note: {
              type: "string",
              nullable: true,
              example: "Assigned to support agent",
            },
            change_reason: {
              type: "string",
              nullable: true,
              example: "Customer request",
            },
            ip_address: {
              type: "string",
              nullable: true,
              example: "***********",
            },
            user_agent: {
              type: "string",
              nullable: true,
              example: "Mozilla/5.0...",
            },
            created_by: { type: "integer", example: 1 },
            created_at: { type: "string", format: "date-time" },
            updated_at: { type: "string", format: "date-time" },
          },
        },
        SupportConfig: {
          type: "object",
          properties: {
            id: { type: "integer", example: 1 },
            organization_id: { type: "string", example: "org_001" },
            support_pin: {
              type: "string",
              nullable: true,
              example: "hashed_pin",
            },
            is_active: { type: "boolean", example: true },
            allow_attachments: { type: "boolean", example: true },
            max_attachment_size: { type: "integer", example: 5242880 },
            allowed_file_types: {
              type: "string",
              example: '["pdf","png","jpg"]',
            },
            auto_assignment_enabled: { type: "boolean", example: false },
            created_by: { type: "integer", example: 1 },
            updated_by: { type: "integer", nullable: true, example: 1 },
            created_at: { type: "string", format: "date-time" },
            updated_at: { type: "string", format: "date-time" },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./src/routes/*.ts", "./src/controller/*.ts", "./src/models/*.ts"],
};

const specs = swaggerJSDoc(options);

export const setupSwagger = (app: Express): void => {
  // Swagger UI setup
  app.use(
    "/api-docs",
    swaggerUi.serve,
    swaggerUi.setup(specs, {
      explorer: true,
      customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #2c3e50; }
      .swagger-ui .scheme-container { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    `,
      customSiteTitle: "TTH Support Ticket API Documentation",
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
      },
    })
  );

  // JSON endpoint for the swagger spec
  app.get("/api-docs.json", (req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(specs);
  });

  console.log(
    `📚 API Documentation available at: ${process.env.API_BASE_URL || "http://localhost:5007"}/api-docs`
  );
};

export default specs;
