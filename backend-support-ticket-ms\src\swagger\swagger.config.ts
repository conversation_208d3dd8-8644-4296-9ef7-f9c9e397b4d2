import swaggerJsdoc from "swagger-jsdoc";

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "TTH Support Ticket Microservice API",
      version: "1.0.0",
      description: `
        TeamTrainHub Support Ticket Management System API

        This microservice provides comprehensive support ticket management functionality including:
        - Public ticket creation for customers
        - Internal ticket management for support agents
        - Real-time messaging and conversation tracking
        - File attachment support via nv_items integration
        - Comprehensive audit trail and history tracking
        - Role-based access control and permissions

        ## Authentication
        Most endpoints require authentication via <PERSON>W<PERSON> token in the Authorization header:
        \`Authorization: Bearer <token>\`

        ## Organization Isolation
        All data is isolated by organization_id to ensure multi-tenant security.

        ## File Uploads
        File attachments are handled through the TTH file upload system and stored in the nv_items table.

        ## Error Handling
        All endpoints return standardized error responses with appropriate HTTP status codes.
      `,
      contact: {
        name: "TTH Support Team",
        email: "<EMAIL>",
      },
      license: {
        name: "Proprietary",
        url: "https://teamtrainhub.com/license",
      },
    },
    servers: [
      {
        url: process.env.API_BASE_URL || "http://localhost:5007",
        description: "Support Ticket Microservice",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT token for authenticated requests",
        },
        adminAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "JWT token with admin privileges",
        },
      },
      schemas: {
        Error: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: false,
            },
            message: {
              type: "string",
              example: "Error message",
            },
            error_code: {
              type: "string",
              example: "ERROR_CODE",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2024-01-01T00:00:00.000Z",
            },
            path: {
              type: "string",
              example: "/api/tickets",
            },
            method: {
              type: "string",
              example: "POST",
            },
            details: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  field: { type: "string" },
                  message: { type: "string" },
                  value: { type: "string" },
                },
              },
            },
          },
        },
        SuccessResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Operation successful",
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2024-01-01T00:00:00.000Z",
            },
            data: {
              type: "object",
              description: "Response data (varies by endpoint)",
            },
          },
        },
        PaginatedResponse: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              example: "Data retrieved successfully",
            },
            data: {
              type: "array",
              items: {
                type: "object",
              },
            },
            pagination: {
              type: "object",
              properties: {
                current_page: { type: "integer", example: 1 },
                total_pages: { type: "integer", example: 10 },
                total_records: { type: "integer", example: 100 },
                per_page: { type: "integer", example: 10 },
              },
            },
            timestamp: {
              type: "string",
              format: "date-time",
              example: "2024-01-01T00:00:00.000Z",
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ["./src/routes/**/*.ts", "./src/controller/*.ts", "./src/models/*.ts"],
};

export const swaggerSpec = swaggerJsdoc(options);
