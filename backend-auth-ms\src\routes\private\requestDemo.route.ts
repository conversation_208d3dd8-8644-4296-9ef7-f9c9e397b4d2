import { Router } from "express";
import requestController from "../../controller/requestDemo.controller";

const router = Router();

/** Get all demo request 
 * @swagger
 * /get-all-demo-requests:
 *   get:
 *     summary: Get all demo requests
 *     description: Fetches all demo requests from the system.
 *     tags:
 *       - Demo Requests
 *     responses:
 *       200:
 *         description: Demo requests fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Demo requests fetched successfully."
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       first_name:
 *                         type: string
 *                         example: "Test"
 *                       last_name:
 *                         type: string
 *                         example: "User"
 *                       email:
 *                         type: string
 *                         format: email
 *                         example: "<EMAIL>"
 *                       mobile_number:
 *                         type: string
 *                         example: "1234567890"
 *                       organization_name:
 *                         type: string
 *                         example: "test_org"
 *                       number_of_employee:
 *                         type: integer
 *                         example: 10
 *                       industry:
 *                         type: string
 *                         example: "Hospitality"
 *                       help_description:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       created_by:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       updated_by:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-02-07T10:20:07.000Z"
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-02-07T10:20:07.000Z"
 *       500:
 *         description: Internal server error
 */
router.get("/get-all-demo-requests", requestController.getAllDemoRequest);

/** Download excel PI */
router.get("/download-excel", requestController.downloadExcel);
export default router;