import rabbitMQ from "./rabbitmq";
import { RABBITMQ_QUEUE } from "../helper/constant";

export const setupConsumers = async () => {
    try {
        console.log("Setting up RabbitMQ consumers...");

        // consume messages from the following queues
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.MAIL_FAILED);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.MAIL_SUCCESS);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.STAFF_CREATION_DETAILS);
        await rabbitMQ.consumeMessage(RABBITMQ_QUEUE.STAFF_PASSWORD_PIN_GENERATE);

        console.log("RabbitMQ consumers are set up successfully.");
    } catch (error) {
        console.error("Error setting up RabbitMQ consumers:", error);
    }
};