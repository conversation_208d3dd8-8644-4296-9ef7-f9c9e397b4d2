# 🍽️ TTH Recipe Module - COMPLETE DOCUMENTATION

## 📋 **TABLE OF CONTENTS**

### **PART I: MODULE OVERVIEW & INTEGRATION**
1. [Module Overview](#module-overview)
2. [TTH System Integration](#tth-system-integration)
3. [Multi-Tenant Architecture](#multi-tenant-architecture)
4. [Internationalization Support](#internationalization-support)

### **PART II: DATABASE & MODELS**
5. [Database Schema](#database-schema)
6. [Model Relationships](#model-relationships)
7. [Status Management](#status-management)

### **PART III: BUSINESS LOGIC & CONTROLLERS**
8. [Controller Architecture](#controller-architecture)
9. [API Reference](#api-reference)
10. [Validation System](#validation-system)

### **PART IV: SYSTEM COMPONENTS**
11. [Middleware & Security](#middleware--security)
12. [File Upload & Storage](#file-upload--storage)
13. [Background Processing](#background-processing)
14. [Notification System](#notification-system)

### **PART V: CONFIGURATION & DEPLOYMENT**
15. [Configuration Management](#configuration-management)
16. [Testing Strategy](#testing-strategy)
17. [Performance Optimization](#performance-optimization)
18. [Deployment Guide](#deployment-guide)

---

## 📋 **Module Overview**

The **TTH Recipe Module** is a comprehensive recipe management system integrated into the TeamTrainHub HRMS platform. It's designed with **multi-tenant architecture**, **internationalization support**, and follows the same architectural patterns as the core TTH system.

### **Module Purpose**
This module manages the complete recipe lifecycle within organizations - from recipe creation and categorization to nutritional analysis and user interactions. It supports multiple organizations with isolated data and customizable settings per tenant.

### **Key Features**
- **Multi-Tenant Recipe Management**: Organization-isolated recipe data
- **International Support**: Multi-language recipe content with i18n
- **Role-Based Access Control**: Integrated with TTH RBAC system
- **Status-Driven Workflow**: Comprehensive status management like other TTH modules
- **Advanced Search & Filtering**: Elasticsearch integration for powerful search
- **Nutritional Analysis**: Automated nutritional calculations
- **Document Management**: Recipe images and attachments via AWS S3
- **Audit Trail**: Complete activity logging and change tracking
- **Background Processing**: RabbitMQ integration for async operations

### **Technology Stack Integration**
- **Backend**: Node.js + Express.js + TypeScript (TTH Standard)
- **Database**: MySQL with Sequelize ORM (TTH Standard)
- **Authentication**: JWT + Keycloak integration (TTH Standard)
- **File Storage**: AWS S3 + Local storage (TTH Standard)
- **Queue System**: RabbitMQ for async processing (TTH Standard)
- **Email**: Nodemailer with template system (TTH Standard)
- **Validation**: Joi validation library (TTH Standard)
- **Internationalization**: i18n for multi-language support (TTH Standard)
- **Search**: Elasticsearch for advanced search capabilities
- **Caching**: Redis for performance optimization

## Architecture

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Recipe        │
│   (React/Vue)   │◄──►│   (Express.js)  │◄──►│   Service       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Auth Service  │    │   Database      │
                       │   (JWT)         │    │   (MongoDB)     │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   File Storage  │    │   Search Engine │
                       │   (AWS S3)      │    │   (Elasticsearch)│
                       └─────────────────┘    └─────────────────┘
```

### Module Structure
```
recipe-module/
├── src/
│   ├── controllers/
│   │   ├── recipeController.js
│   │   ├── ingredientController.js
│   │   └── categoryController.js
│   ├── models/
│   │   ├── Recipe.js
│   │   ├── Ingredient.js
│   │   └── Category.js
│   ├── routes/
│   │   ├── recipes.js
│   │   ├── ingredients.js
│   │   └── categories.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── upload.js
│   ├── services/
│   │   ├── recipeService.js
│   │   ├── nutritionService.js
│   │   └── searchService.js
│   └── utils/
│       ├── helpers.js
│       └── constants.js
├── tests/
├── docs/
├── config/
└── package.json
```

## Features

### Core Features
1. **Recipe CRUD Operations**
   - Create new recipes with detailed information
   - Read recipe details with ingredients and instructions
   - Update existing recipes
   - Delete recipes (soft delete with audit trail)

2. **Ingredient Management**
   - Comprehensive ingredient database
   - Nutritional information per ingredient
   - Unit conversion capabilities
   - Allergen information tracking

3. **Category & Classification**
   - Recipe categories (appetizers, main courses, desserts, etc.)
   - Cuisine types (Italian, Chinese, Mexican, etc.)
   - Dietary restrictions (vegetarian, vegan, gluten-free, etc.)
   - Difficulty levels (beginner, intermediate, advanced)

4. **Search & Discovery**
   - Full-text search across recipes
   - Filter by ingredients, categories, dietary restrictions
   - Advanced search with multiple criteria
   - Recommendation engine based on user preferences

5. **User Interaction**
   - Recipe ratings and reviews
   - Favorite recipes
   - Recipe sharing capabilities
   - Cooking notes and modifications

### Advanced Features
1. **Nutritional Analysis**
   - Automatic calculation of nutritional values
   - Per-serving nutritional breakdown
   - Dietary goal tracking
   - Allergen warnings

2. **Meal Planning**
   - Weekly meal planning
   - Shopping list generation
   - Portion scaling
   - Leftover management

3. **Social Features**
   - Recipe sharing
   - User-generated content
   - Community ratings
   - Recipe collections

## Installation

### Prerequisites
- Node.js (v14 or higher)
- MongoDB (v4.4 or higher)
- Redis (v6 or higher)
- AWS Account (for S3 storage)

### Step-by-Step Installation

1. **Clone the Repository**
```bash
git clone https://github.com/teamtrainhub/recipe-module.git
cd recipe-module
```

2. **Install Dependencies**
```bash
npm install
```

3. **Environment Configuration**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Database Setup**
```bash
# Start MongoDB
mongod --dbpath /path/to/your/db

# Run database migrations
npm run migrate
```

5. **Start the Application**
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## Configuration

### Environment Variables
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/teamtrainhub_recipes
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# AWS Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET=your_s3_bucket
AWS_REGION=us-east-1

# Search Configuration
ELASTICSEARCH_URL=http://localhost:9200

# External APIs
NUTRITION_API_KEY=your_nutrition_api_key
```

### Database Configuration
```javascript
// config/database.js
module.exports = {
  development: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
    }
  },
  production: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 50,
      serverSelectionTimeoutMS: 30000,
    }
  }
};
```

## API Reference

### Authentication Endpoints

#### POST /api/auth/login
Authenticate user and return JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "60d5ecb74b24a1234567890",
    "email": "<EMAIL>",
    "name": "John Doe"
  }
}
```

### Recipe Endpoints

#### GET /api/recipes
Retrieve all recipes with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `category` (string): Filter by category
- `cuisine` (string): Filter by cuisine type
- `dietary` (string): Filter by dietary restrictions
- `search` (string): Search term for recipe name/description

**Response:**
```json
{
  "success": true,
  "data": {
    "recipes": [
      {
        "id": "60d5ecb74b24a1234567890",
        "name": "Spaghetti Carbonara",
        "description": "Classic Italian pasta dish",
        "category": "main-course",
        "cuisine": "italian",
        "difficulty": "intermediate",
        "prepTime": 15,
        "cookTime": 20,
        "servings": 4,
        "image": "https://s3.amazonaws.com/bucket/recipe-image.jpg",
        "rating": 4.5,
        "reviewCount": 23,
        "createdAt": "2023-06-25T10:30:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### GET /api/recipes/:id
Retrieve a specific recipe by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "60d5ecb74b24a1234567890",
    "name": "Spaghetti Carbonara",
    "description": "Classic Italian pasta dish with eggs, cheese, and pancetta",
    "category": "main-course",
    "cuisine": "italian",
    "difficulty": "intermediate",
    "prepTime": 15,
    "cookTime": 20,
    "servings": 4,
    "image": "https://s3.amazonaws.com/bucket/recipe-image.jpg",
    "ingredients": [
      {
        "id": "60d5ecb74b24a1234567891",
        "name": "Spaghetti",
        "amount": 400,
        "unit": "grams",
        "notes": "Use high-quality pasta"
      },
      {
        "id": "60d5ecb74b24a1234567892",
        "name": "Pancetta",
        "amount": 150,
        "unit": "grams",
        "notes": "Diced"
      }
    ],
    "instructions": [
      {
        "step": 1,
        "description": "Bring a large pot of salted water to boil",
        "duration": 5
      },
      {
        "step": 2,
        "description": "Cook spaghetti according to package directions",
        "duration": 10
      }
    ],
    "nutrition": {
      "calories": 520,
      "protein": 22,
      "carbohydrates": 65,
      "fat": 18,
      "fiber": 3,
      "sugar": 4
    },
    "tags": ["pasta", "italian", "quick", "comfort-food"],
    "rating": 4.5,
    "reviewCount": 23,
    "createdBy": "60d5ecb74b24a1234567893",
    "createdAt": "2023-06-25T10:30:00Z",
    "updatedAt": "2023-06-25T10:30:00Z"
  }
}
```

#### POST /api/recipes
Create a new recipe.

**Request Body:**
```json
{
  "name": "Chocolate Chip Cookies",
  "description": "Soft and chewy chocolate chip cookies",
  "category": "dessert",
  "cuisine": "american",
  "difficulty": "beginner",
  "prepTime": 15,
  "cookTime": 12,
  "servings": 24,
  "ingredients": [
    {
      "ingredientId": "60d5ecb74b24a1234567894",
      "amount": 2.25,
      "unit": "cups",
      "notes": "All-purpose flour"
    }
  ],
  "instructions": [
    {
      "step": 1,
      "description": "Preheat oven to 375°F",
      "duration": 5
    }
  ],
  "tags": ["cookies", "dessert", "baking"]
}
```

#### PUT /api/recipes/:id
Update an existing recipe.

#### DELETE /api/recipes/:id
Delete a recipe (soft delete).

### Ingredient Endpoints

#### GET /api/ingredients
Retrieve all ingredients with nutritional information.

#### GET /api/ingredients/:id
Retrieve specific ingredient details.

#### POST /api/ingredients
Create a new ingredient.

### Category Endpoints

#### GET /api/categories
Retrieve all recipe categories.

#### POST /api/categories
Create a new category.

---

## 🗄️ **TTH SYSTEM INTEGRATION**

### **Multi-Tenant Architecture**
The Recipe Module follows TTH's multi-tenant architecture pattern:

```typescript
// All models include organization_id for tenant isolation
interface BaseRecipeAttributes {
  organization_id: string;  // Tenant isolation (TTH Standard)
  created_by: number;       // FK to nv_users
  updated_by: number;       // FK to nv_users
  created_at: Date;         // Auto timestamp
  updated_at: Date;         // Auto timestamp
  deleted_at?: Date;        // Soft delete (TTH Standard)
}
```

### **Status Management System**
Following TTH's comprehensive status management pattern:

```typescript
// Recipe Status Flow (TTH Pattern)
enum RecipeStatus {
  DRAFT = 'DRAFT',           // Recipe being created
  PENDING = 'PENDING',       // Submitted for review
  ACTIVE = 'ACTIVE',         // Published and available
  INACTIVE = 'INACTIVE',     // Temporarily disabled
  REJECTED = 'REJECTED',     // Rejected by admin
  DELETED = 'DELETED'        // Soft deleted
}
```

### **Internationalization Support**
Integrated with TTH's i18n system:

```typescript
// Multi-language content support
interface RecipeI18nAttributes {
  recipe_id: number;
  language_code: string;     // 'en', 'es', 'fr', etc.
  name: string;              // Localized recipe name
  description: text;         // Localized description
  instructions: text;        // Localized cooking instructions
  organization_id: string;   // Tenant isolation
}
```

---

## 🗄️ **DATABASE SCHEMA (TTH MYSQL ARCHITECTURE)**

### **Recipe Model (`nv_recipes` table) - Master Entity**
```typescript
interface recipeAttributes {
  // Primary Identification
  id: number;                    // Primary key, auto-increment
  recipe_code: string;           // Auto-generated: ORG-RECIPE-SEQUENCE (e.g., "TTH-RCP-001")
  recipe_name: string;           // Recipe name (2-100 chars)
  recipe_description: text;      // Detailed description

  // Classification
  category_id: number;           // FK to nv_recipe_categories
  cuisine_type: string;          // italian|chinese|mexican|indian|american|french|other
  difficulty_level: string;     // beginner|intermediate|advanced|expert
  meal_type: string;             // breakfast|lunch|dinner|snack|dessert

  // Timing & Servings
  prep_time_minutes: number;     // Preparation time
  cook_time_minutes: number;     // Cooking time
  total_time_minutes: number;    // Total time (calculated)
  servings: number;              // Number of servings
  serving_size: string;          // Serving size description

  // Media & Documentation
  recipe_image_url: string;      // Main recipe image (AWS S3)
  video_url: string;             // Optional cooking video
  recipe_source: string;         // Source/origin of recipe
  recipe_author: string;         // Recipe author/chef

  // Nutritional Information
  calories_per_serving: decimal; // Calories per serving
  protein_grams: decimal;        // Protein content
  carbs_grams: decimal;          // Carbohydrate content
  fat_grams: decimal;            // Fat content
  fiber_grams: decimal;          // Fiber content
  sugar_grams: decimal;          // Sugar content
  sodium_mg: decimal;            // Sodium content

  // Recipe Metadata
  recipe_tags: text;             // JSON array of tags
  dietary_restrictions: text;    // JSON array (vegetarian, vegan, gluten-free, etc.)
  allergen_info: text;           // JSON array of allergens
  equipment_needed: text;        // Required cooking equipment

  // Status & Workflow (TTH Pattern)
  recipe_status: string;         // DRAFT|PENDING|ACTIVE|INACTIVE|REJECTED|DELETED
  approval_status: string;       // PENDING|APPROVED|REJECTED
  approved_by: number;           // FK to nv_users (approver)
  approved_at: DateTime;         // Approval timestamp
  rejection_reason: text;        // Rejection reason
  last_reject_remark: text;      // Last rejection remark

  // Rating & Reviews
  average_rating: decimal;       // Average rating (0-5)
  total_reviews: number;         // Total number of reviews
  total_favorites: number;       // Total times favorited

  // Multi-tenant & Organizational (TTH Standard)
  organization_id: string;       // Tenant isolation
  branch_id: number;             // FK to nv_branches (optional)
  department_id: number;         // FK to nv_departments (optional)

  // Audit Fields (TTH Standard)
  created_by: number;            // FK to nv_users
  updated_by: number;            // FK to nv_users
  created_at: Date;              // Auto timestamp
  updated_at: Date;              // Auto timestamp
  deleted_at: Date;              // Soft delete timestamp
}
```

### **Recipe Ingredients Model (`nv_recipe_ingredients` table)**
```typescript
interface recipeIngredientAttributes {
  id: number;                    // Primary key
  recipe_id: number;             // FK to nv_recipes
  ingredient_id: number;         // FK to nv_ingredients

  // Quantity & Measurement
  quantity: decimal;             // Amount needed
  unit_of_measurement: string;   // grams|kg|cups|tbsp|tsp|pieces|liters|ml
  ingredient_notes: text;        // Special preparation notes
  is_optional: boolean;          // Optional ingredient flag
  ingredient_order: number;      // Display order in recipe

  // Substitutions
  substitute_ingredients: text;  // JSON array of substitute options

  // Status
  ingredient_status: string;     // ACTIVE|INACTIVE|DELETED

  // Multi-tenant (TTH Standard)
  organization_id: string;       // Tenant isolation

  // Audit (TTH Standard)
  created_by: number;
  updated_by: number;
  created_at: Date;
  updated_at: Date;
}
```

### **Recipe Instructions Model (`nv_recipe_instructions` table)**
```typescript
interface recipeInstructionAttributes {
  id: number;                    // Primary key
  recipe_id: number;             // FK to nv_recipes

  // Instruction Details
  step_number: number;           // Step sequence (1, 2, 3...)
  instruction_title: string;     // Step title/summary
  instruction_description: text; // Detailed instruction
  estimated_time_minutes: number; // Time for this step

  // Media
  step_image_url: string;        // Step illustration image
  step_video_url: string;        // Step video tutorial

  // Temperature & Timing
  temperature_celsius: number;   // Cooking temperature
  temperature_fahrenheit: number; // Cooking temperature (F)
  timer_minutes: number;         // Timer for this step

  // Equipment
  equipment_needed: text;        // Equipment for this step

  // Tips & Notes
  chef_tips: text;               // Professional tips
  common_mistakes: text;         // Common mistakes to avoid

  // Status
  instruction_status: string;    // ACTIVE|INACTIVE|DELETED

  // Multi-tenant (TTH Standard)
  organization_id: string;       // Tenant isolation

  // Audit (TTH Standard)
  created_by: number;
  updated_by: number;
  created_at: Date;
  updated_at: Date;
}
```

### **Ingredients Model (`nv_ingredients` table)**
```typescript
interface ingredientAttributes {
  // Primary Identification
  id: number;                    // Primary key, auto-increment
  ingredient_code: string;       // Auto-generated: ORG-ING-SEQUENCE (e.g., "TTH-ING-001")
  ingredient_name: string;       // Ingredient name (2-100 chars)
  ingredient_description: text;  // Detailed description

  // Classification
  ingredient_category: string;   // vegetables|fruits|grains|proteins|dairy|spices|oils|other
  ingredient_type: string;       // fresh|frozen|canned|dried|processed
  brand_name: string;            // Brand name (optional)

  // Nutritional Information (per 100g)
  calories_per_100g: decimal;    // Calories per 100g
  protein_per_100g: decimal;     // Protein content
  carbs_per_100g: decimal;       // Carbohydrate content
  fat_per_100g: decimal;         // Fat content
  fiber_per_100g: decimal;       // Fiber content
  sugar_per_100g: decimal;       // Sugar content
  sodium_per_100g: decimal;      // Sodium content

  // Allergen & Dietary Information
  allergen_info: text;           // JSON array of allergens
  dietary_flags: text;           // JSON array (vegetarian, vegan, gluten-free, etc.)

  // Storage & Handling
  storage_instructions: text;    // Storage requirements
  shelf_life_days: number;       // Shelf life in days
  preparation_notes: text;       // Preparation instructions

  // Sourcing Information
  supplier_info: text;           // Supplier information
  cost_per_unit: decimal;        // Cost per unit
  unit_of_purchase: string;      // kg|grams|pieces|liters|etc.

  // Status (TTH Pattern)
  ingredient_status: string;     // ACTIVE|INACTIVE|DELETED

  // Multi-tenant (TTH Standard)
  organization_id: string;       // Tenant isolation

  // Audit (TTH Standard)
  created_by: number;
  updated_by: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}
```

### **Recipe Categories Model (`nv_recipe_categories` table)**
```typescript
interface recipeCategoryAttributes {
  // Primary Identification
  id: number;                    // Primary key, auto-increment
  category_code: string;         // Auto-generated: ORG-CAT-SEQUENCE
  category_name: string;         // Category name (2-50 chars)
  category_description: text;    // Category description

  // Visual & Organization
  category_image_url: string;    // Category image (AWS S3)
  category_icon: string;         // Icon class/URL
  category_color: string;        // Color code (#hex)
  display_order: number;         // Display order

  // Hierarchy
  parent_category_id: number;    // FK to parent category (null for root)
  category_level: number;        // Hierarchy level (0=root)
  category_path: string;         // Hierarchical path

  // Status (TTH Pattern)
  category_status: string;       // ACTIVE|INACTIVE|DELETED

  // Multi-tenant (TTH Standard)
  organization_id: string;       // Tenant isolation

  // Audit (TTH Standard)
  created_by: number;
  updated_by: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}
```

### **Recipe Reviews Model (`nv_recipe_reviews` table)**
```typescript
interface recipeReviewAttributes {
  // Primary Identification
  id: number;                    // Primary key, auto-increment
  recipe_id: number;             // FK to nv_recipes
  user_id: number;               // FK to nv_users (reviewer)

  // Review Content
  rating: number;                // Rating (1-5 stars)
  review_title: string;          // Review title
  review_content: text;          // Review content

  // Review Metadata
  would_make_again: boolean;     // Would make again flag
  difficulty_rating: number;     // Difficulty rating (1-5)
  taste_rating: number;          // Taste rating (1-5)
  preparation_time_actual: number; // Actual prep time taken

  // Review Media
  review_images: text;           // JSON array of image URLs

  // Moderation
  is_verified_purchase: boolean; // Verified reviewer flag
  is_featured: boolean;          // Featured review flag
  moderation_status: string;     // PENDING|APPROVED|REJECTED
  moderated_by: number;          // FK to moderator user
  moderated_at: DateTime;        // Moderation timestamp

  // Interaction
  helpful_votes: number;         // Helpful votes count
  total_votes: number;           // Total votes count

  // Status (TTH Pattern)
  review_status: string;         // ACTIVE|INACTIVE|DELETED

  // Multi-tenant (TTH Standard)
  organization_id: string;       // Tenant isolation

  // Audit (TTH Standard)
  created_by: number;
  updated_by: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}
```

### **Recipe Favorites Model (`nv_recipe_favorites` table)**
```typescript
interface recipeFavoriteAttributes {
  id: number;                    // Primary key
  recipe_id: number;             // FK to nv_recipes
  user_id: number;               // FK to nv_users

  // Favorite Metadata
  favorite_notes: text;          // Personal notes
  favorite_tags: text;           // Personal tags (JSON array)

  // Status
  favorite_status: string;       // ACTIVE|INACTIVE

  // Multi-tenant (TTH Standard)
  organization_id: string;       // Tenant isolation

  // Audit (TTH Standard)
  created_at: Date;
  updated_at: Date;
}
```

---

## 🔄 **MODEL RELATIONSHIPS (TTH SEQUELIZE PATTERN)**

### **Recipe Model Associations**
```typescript
// Recipe.ts - Model Relationships
export class Recipe extends Model<recipeAttributes> implements recipeAttributes {
  // ... model definition ...

  static associate(models: any) {
    // Many-to-One Relationships
    Recipe.belongsTo(models.RecipeCategory, {
      foreignKey: 'category_id',
      as: 'category'
    });
    Recipe.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator'
    });
    Recipe.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver'
    });
    Recipe.belongsTo(models.Branch, {
      foreignKey: 'branch_id',
      as: 'branch'
    });

    // One-to-Many Relationships
    Recipe.hasMany(models.RecipeIngredient, {
      foreignKey: 'recipe_id',
      as: 'recipeIngredients'
    });
    Recipe.hasMany(models.RecipeInstruction, {
      foreignKey: 'recipe_id',
      as: 'instructions'
    });
    Recipe.hasMany(models.RecipeReview, {
      foreignKey: 'recipe_id',
      as: 'reviews'
    });
    Recipe.hasMany(models.RecipeFavorite, {
      foreignKey: 'recipe_id',
      as: 'favorites'
    });
    Recipe.hasMany(models.Activity, {
      foreignKey: 'reference_id',
      as: 'activities',
      scope: { reference_type: 'recipe' }
    });
  }
}
```

### **Complete Model List (TTH Recipe Module)**
```typescript
// Recipe Module Models (14 Total)
1. Recipe                    // nv_recipes - Master recipe entity
2. RecipeIngredient         // nv_recipe_ingredients - Recipe-ingredient junction
3. RecipeInstruction        // nv_recipe_instructions - Cooking steps
4. RecipeCategory           // nv_recipe_categories - Recipe categories
5. RecipeReview             // nv_recipe_reviews - User reviews
6. RecipeFavorite           // nv_recipe_favorites - User favorites
7. RecipeI18n               // nv_recipe_i18n - Multi-language content
8. Ingredient               // nv_ingredients - Master ingredient list
9. IngredientCategory       // nv_ingredient_categories - Ingredient categories
10. RecipeRequest           // nv_recipe_requests - Change requests
11. RecipeRequestItem       // nv_recipe_request_items - Request details
12. RecipeMedia             // nv_recipe_media - Additional media files
13. RecipeNutrition         // nv_recipe_nutrition - Detailed nutrition
14. RecipeCollection        // nv_recipe_collections - Recipe collections
```

## Usage Examples

### Creating a Recipe
```javascript
const express = require('express');
const Recipe = require('../models/Recipe');
const auth = require('../middleware/auth');

// Create a new recipe
app.post('/api/recipes', auth, async (req, res) => {
  try {
    const recipe = new Recipe({
      ...req.body,
      createdBy: req.user.id
    });

    await recipe.save();
    await recipe.populate('ingredients.ingredient category');

    res.status(201).json({
      success: true,
      data: recipe
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});
```

### Searching Recipes
```javascript
// Advanced recipe search with filters
app.get('/api/recipes/search', async (req, res) => {
  try {
    const {
      search,
      category,
      cuisine,
      difficulty,
      maxPrepTime,
      dietary,
      page = 1,
      limit = 10
    } = req.query;

    let query = { isActive: true };

    // Text search
    if (search) {
      query.$text = { $search: search };
    }

    // Filter by category
    if (category) {
      query.category = category;
    }

    // Filter by cuisine
    if (cuisine) {
      query.cuisine = cuisine;
    }

    // Filter by difficulty
    if (difficulty) {
      query.difficulty = difficulty;
    }

    // Filter by preparation time
    if (maxPrepTime) {
      query.prepTime = { $lte: parseInt(maxPrepTime) };
    }

    // Filter by dietary restrictions
    if (dietary) {
      query.tags = { $in: [dietary] };
    }

    const recipes = await Recipe.find(query)
      .populate('category', 'name')
      .populate('ingredients.ingredient', 'name')
      .sort({ rating: -1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .exec();

    const total = await Recipe.countDocuments(query);

    res.json({
      success: true,
      data: {
        recipes,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});
```

### Frontend Integration Example (React)
```jsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';

const RecipeList = () => {
  const [recipes, setRecipes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    cuisine: '',
    difficulty: ''
  });

  useEffect(() => {
    fetchRecipes();
  }, [filters]);

  const fetchRecipes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams(filters).toString();
      const response = await axios.get(`/api/recipes?${params}`);
      setRecipes(response.data.data.recipes);
    } catch (error) {
      console.error('Error fetching recipes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="recipe-list">
      <div className="filters">
        <input
          type="text"
          placeholder="Search recipes..."
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
        />
        <select
          value={filters.category}
          onChange={(e) => handleFilterChange('category', e.target.value)}
        >
          <option value="">All Categories</option>
          <option value="appetizer">Appetizers</option>
          <option value="main-course">Main Courses</option>
          <option value="dessert">Desserts</option>
        </select>
      </div>

      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="recipes-grid">
          {recipes.map(recipe => (
            <div key={recipe.id} className="recipe-card">
              <img src={recipe.image} alt={recipe.name} />
              <h3>{recipe.name}</h3>
              <p>{recipe.description}</p>
              <div className="recipe-meta">
                <span>⏱️ {recipe.prepTime + recipe.cookTime} min</span>
                <span>👥 {recipe.servings} servings</span>
                <span>⭐ {recipe.rating}/5</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecipeList;
```

## Testing

### Unit Tests Example
```javascript
const request = require('supertest');
const app = require('../app');
const Recipe = require('../models/Recipe');
const User = require('../models/User');

describe('Recipe API', () => {
  let authToken;
  let userId;

  beforeEach(async () => {
    // Create test user and get auth token
    const user = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123'
    });
    userId = user._id;
    authToken = user.generateAuthToken();
  });

  afterEach(async () => {
    await Recipe.deleteMany({});
    await User.deleteMany({});
  });

  describe('POST /api/recipes', () => {
    it('should create a new recipe', async () => {
      const recipeData = {
        name: 'Test Recipe',
        description: 'A test recipe',
        category: 'main-course',
        cuisine: 'american',
        difficulty: 'beginner',
        prepTime: 15,
        cookTime: 30,
        servings: 4,
        ingredients: [],
        instructions: []
      };

      const response = await request(app)
        .post('/api/recipes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(recipeData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(recipeData.name);
      expect(response.body.data.createdBy).toBe(userId.toString());
    });

    it('should return 400 for invalid recipe data', async () => {
      const invalidData = {
        name: '', // Empty name should fail validation
        description: 'A test recipe'
      };

      const response = await request(app)
        .post('/api/recipes')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/recipes', () => {
    beforeEach(async () => {
      await Recipe.create([
        {
          name: 'Recipe 1',
          description: 'First recipe',
          category: 'appetizer',
          cuisine: 'italian',
          difficulty: 'beginner',
          prepTime: 10,
          cookTime: 15,
          servings: 2,
          createdBy: userId
        },
        {
          name: 'Recipe 2',
          description: 'Second recipe',
          category: 'main-course',
          cuisine: 'chinese',
          difficulty: 'intermediate',
          prepTime: 20,
          cookTime: 25,
          servings: 4,
          createdBy: userId
        }
      ]);
    });

    it('should return all recipes', async () => {
      const response = await request(app)
        .get('/api/recipes')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.recipes).toHaveLength(2);
    });

    it('should filter recipes by category', async () => {
      const response = await request(app)
        .get('/api/recipes?category=appetizer')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.recipes).toHaveLength(1);
      expect(response.body.data.recipes[0].category).toBe('appetizer');
    });
  });
});
```

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- --grep "Recipe API"
```

## Deployment

### Production Deployment with Docker

#### Dockerfile
```dockerfile
FROM node:16-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 3000

CMD ["npm", "start"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/teamtrainhub_recipes
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongo
      - redis
    restart: unless-stopped

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  mongo_data:
```

### Environment-Specific Configurations

#### Production Environment
```javascript
// config/production.js
module.exports = {
  database: {
    uri: process.env.MONGODB_URI,
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 50,
      serverSelectionTimeoutMS: 30000,
      socketTimeoutMS: 45000,
    }
  },
  redis: {
    url: process.env.REDIS_URL,
    options: {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    }
  },
  logging: {
    level: 'info',
    format: 'json'
  },
  security: {
    cors: {
      origin: process.env.ALLOWED_ORIGINS?.split(',') || [],
      credentials: true
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // limit each IP to 100 requests per windowMs
    }
  }
};
```

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Write tests for your changes
5. Run tests: `npm test`
6. Commit your changes: `git commit -am 'Add new feature'`
7. Push to the branch: `git push origin feature/new-feature`
8. Submit a pull request

### Code Style Guidelines
- Use ESLint configuration provided in the project
- Follow JavaScript Standard Style
- Write meaningful commit messages
- Add JSDoc comments for functions and classes
- Maintain test coverage above 80%

### Pull Request Process
1. Ensure all tests pass
2. Update documentation if needed
3. Add appropriate labels to your PR
4. Request review from maintainers
5. Address any feedback promptly

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check MongoDB connection
mongosh "mongodb://localhost:27017/teamtrainhub_recipes"

# Check if MongoDB is running
sudo systemctl status mongod

# Restart MongoDB
sudo systemctl restart mongod
```

#### Redis Connection Issues
```bash
# Check Redis connection
redis-cli ping

# Check Redis status
sudo systemctl status redis

# Clear Redis cache
redis-cli flushall
```

#### Performance Issues
1. **Slow Queries**: Check database indexes
   ```javascript
   // Add indexes for frequently queried fields
   db.recipes.createIndex({ "name": "text", "description": "text" });
   db.recipes.createIndex({ "category": 1, "cuisine": 1 });
   ```

2. **Memory Issues**: Monitor Node.js memory usage
   ```bash
   # Check memory usage
   node --inspect app.js
   ```

3. **High CPU Usage**: Profile your application
   ```bash
   # Use clinic.js for profiling
   npm install -g clinic
   clinic doctor -- node app.js
   ```

### Error Codes Reference

| Error Code | Description | Solution |
|------------|-------------|----------|
| RECIPE_001 | Recipe not found | Check recipe ID validity |
| RECIPE_002 | Invalid recipe data | Validate input data |
| RECIPE_003 | Duplicate recipe name | Use unique recipe names |
| AUTH_001 | Invalid authentication token | Refresh authentication token |
| AUTH_002 | Insufficient permissions | Check user permissions |
| DB_001 | Database connection failed | Check database configuration |
| CACHE_001 | Redis connection failed | Check Redis configuration |

### Logging and Monitoring

#### Log Levels
- **ERROR**: System errors, exceptions
- **WARN**: Warning messages, deprecated features
- **INFO**: General information, API requests
- **DEBUG**: Detailed debugging information

#### Monitoring Endpoints
```javascript
// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version
  });
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
  res.json({
    requests: requestCount,
    errors: errorCount,
    responseTime: averageResponseTime,
    activeConnections: activeConnections
  });
});
```

### Support

For additional support:
- 📧 Email: <EMAIL>
- 📖 Documentation: https://docs.teamtrainhub.com
- 🐛 Bug Reports: https://github.com/teamtrainhub/recipe-module/issues
- 💬 Community: https://discord.gg/teamtrainhub

---

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Changelog

### v1.2.0 (2023-12-01)
- Added nutritional analysis feature
- Improved search performance with Elasticsearch
- Added recipe rating and review system
- Enhanced security with rate limiting

### v1.1.0 (2023-10-15)
- Added meal planning functionality
- Implemented recipe sharing features
- Added support for recipe collections
- Performance optimizations

### v1.0.0 (2023-08-01)
- Initial release
- Basic CRUD operations for recipes
- Ingredient management
- Category system
- User authentication
