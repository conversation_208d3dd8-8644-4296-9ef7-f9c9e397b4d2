import { StatusCodes } from "http-status-codes";
import { getData, updateOrg } from "../keycloak/common";
import orgValidator from "../validator/organization.validator";
import { EMAIL_ADDRESS, FILE_UPLOAD_CONSTANT, RABBITMQ_QUEUE } from "../helper/constant";
import rabbitmqPublisher from '../rabbitmq/rabbitmq';
import { checkUserRole } from "../helper/common";
import { moveFileInBucket } from "../helper/upload.helper";
import { Item } from "../models/Item";

/** Get organization By id 
 * @param req
 * @param res
 * @returns
*/
const getOrganizationById = async (req: any, res: any) => {
    try {
        let { orgId } = req.params
        let token = req.token
        /** Make URL for get organization data. */
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${orgId}`;

        /** Fetch organization data */
        let getOrgData: any = await getData(keycloakRealmUrl, token)
        if (getOrgData.status == false) {
            return res.status(getOrgData.statusCode).json({
                status: getOrgData.status,
                message: getOrgData.message,
                data: getOrgData.statusText
            });
        }

        getOrgData = getOrgData?.data

        /** check ig organization name contains _ then update with space. */
        if (getOrgData.name && getOrgData.name.includes('_')) {
            /** Replace underscore with space */
            getOrgData.name = getOrgData.name.replace(/_/g, ' ')
            /** set alias same as name, because both are same. */
            getOrgData.alias = getOrgData.name
        }

        if (getOrgData.attributes.organization_logo && getOrgData.attributes.organization_logo.trim() !== "" && getOrgData.attributes.organization_logo !== "\"\"") {
            if (!isNaN(getOrgData.attributes.organization_logo)) {
                const getItem = await Item.findOne({ where: { id: getOrgData.attributes.organization_logo } });
                if (getItem) {
                    getOrgData.attributes.organization_logo = global.config.API_BASE_URL + getItem?.item_location
                }
            } else {
                getOrgData.attributes.organization_logo = global.config.API_BASE_URL + `organization_images/` + getOrgData.attributes.organization_logo;
            }
        }

        /** If user is successfully created, return success response */
        if (getOrgData) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_DATA_FETCHED"),
                data: getOrgData
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/**
 * Update Organization API response
 * @param req
 * @param res
 * @returns
 */
const updateOrganization = async (req: any, res: any) => {
    try {
        let userId = req.user.sub
        /** Validate req.body with Joi  */
        const { error } = await orgValidator.updateOrganization.validate(req.body);
        if (error) {
            return res
                .status(400)
                .json({ status: false, message: error.details[0].message });
        }

        if (req.files[0]?.item_id) {
            req.body.organization_logo = req.files[0]?.item_id;
            if(req.files[0]?.isMovable){
                await moveFileInBucket(
                    req.files[0].bucket,
                    req.files[0].path,
                    FILE_UPLOAD_CONSTANT.ORGANIZATION_LOGO.destinationPath(
                        req.body.id,
                        req.files[0].filename,
                    ),
                    req.files[0]?.item_id,
                    true,
                    req.body.id
                );
            }
        } else {
            if (req.body.organization_logo) {
                const findItem = await Item.findOne({ where: { item_name: req.body.organization_logo, item_organization_id: req.body.id } })
                if (findItem && findItem.item_location) {
                    req.body.organization_logo = findItem?.id
                } else {
                    req.body.organization_logo = req.body.organization_logo
                }
            }
        }
        /** Check User role, if it's super admin then don't update createdBy value */
        let checkUserRoleStatus: any = await checkUserRole(userId)
        if (!checkUserRoleStatus) {
            req.body.createdBy = userId
        }
        req.body.updatedBy = userId
        req.body.domains = new URL(req.body.website).hostname;
        req.body.type = 'body';
        /** Update organization data */
        let updateOrgData: any = await updateOrg(req.body, req.token);

        if (updateOrgData.status == false) {
            return res.status(updateOrgData.statusCode).json({
                status: updateOrgData.status,
                message: updateOrgData.message,
                data: updateOrgData.statusText
            });
        }
        if (updateOrgData.status == true) {
            /** check if super admin update organization account from inactive to active, then send mail */
            if (req.body.role === 'super_admin') {
                /** Send mail to user when super admin update organization status from inactive to active */
                const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${req.body.createdBy}`;
                let getUsersData: any = await getData(keycloakRealmUrl);
                getUsersData = getUsersData.data

                if (getUsersData && getUsersData.email) {
                    /** Prepare message for org master  */
                    const message = {
                        name: `${getUsersData.firstName} ${getUsersData.lastName}`,
                        email: getUsersData.email,
                        LOGO: global.config.API_BASE_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'AUTH'
                    };

                    /** Publish a message to the "org_master_user" queue */
                    const queue: any = RABBITMQ_QUEUE.ORGANIZATION_ACCOUNT_UPDATE_MAIL;
                    await rabbitmqPublisher.publishMessage(queue, message);
                }
            }

            return res.status(StatusCodes.OK).json({
                status: updateOrgData.status,
                message: res.__("SUCCESS_DATA_UPDATED"),
                data: null,
            });
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Closed Organization account 
 * @param req
 * @param res
 * @returns
 * 
*/
const closeOrganization = async (req: any, res: any) => {
    try {
        let { organizationId } = req.params
        let token = req.token
        /** Make URL for get organization data. */
        const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${organizationId}`;
        let getOrgData: any = await getData(keycloakRealmUrl, token)
        if (getOrgData.status == false) {
            return res.status(getOrgData.statusCode).json({
                status: getOrgData.status,
                message: getOrgData.message,
                data: getOrgData.statusText
            });
        }
        getOrgData = getOrgData?.data
        getOrgData.attributes.status = 'inactive'

        /** Update organization data */
        let updateOrgData: any = await updateOrg(getOrgData, req.token);
        if (updateOrgData.status == false) {
            return res.status(updateOrgData.statusCode).json({
                status: updateOrgData.status,
                message: updateOrgData.message,
                data: updateOrgData.statusText
            });
        }

        /** If user purchased subscription then expired it */
        /** Prepare message for org master  */
        const message = {
            organizationId: organizationId,
        };

        /** Publish a message to the "org_master_user" queue */
        const queue: any = RABBITMQ_QUEUE.SUBSCRIPTION_EXPIRY;
        await rabbitmqPublisher.publishMessage(queue, message);
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_ORGANIZATION_CLOSED"),
            data: null,
        });
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

export default {
    getOrganizationById,
    updateOrganization,
    closeOrganization
}