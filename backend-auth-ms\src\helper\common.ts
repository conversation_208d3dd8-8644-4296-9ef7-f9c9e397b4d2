import fs from "fs";
import { Mail } from '../models/Mail';
import moment from 'moment';
import { getUserRoles } from "../keycloak/common";
import { RABBITMQ_QUEUE } from "./constant";
import rabbitmqPublisher from '../rabbitmq/rabbitmq';
import { crc32 } from "crc";
import mmm from 'mmmagic'
import CryptoJS from 'crypto-js'
import { QueryTypes } from "sequelize";
import { sequelize } from "../models";

const readHTMLFile = function (path: any, cb: any) {
  /** Read the HTML file at the specified path using UTF-8 encoding */
  fs.readFile(path, "utf-8", function (err, data) {
    /** If an error occurs during reading, log the error and throw it */
    if (err) {
      console.log(err);
      throw err; // Stop the execution and throw the error
    } else {
      /** If no error, pass the file content to the callback function */
      cb(null, data); // call the callback function with the file data
    }
  });
};


const GeneratePassword = () => {
  const length = 8;
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const specialCharacters = '@$!%*?&';
  const allCharacters = lowercase + uppercase + numbers + specialCharacters;

  let password = '';
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password +=
    specialCharacters[Math.floor(Math.random() * specialCharacters.length)];

  for (let i = password.length; i < length; i++) {
    password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
  }
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
};


const otpAttemptValidation = async (user_email: any, mail_subject: any, attempt: any) => {
  try {
    /** get all mails on that user with particular subject */
    const findMail: any = await Mail.findAll({
      attributes: ['createdAt'],
      where: {
        notify_mail_to: user_email,
        notify_mail_subject: mail_subject,
      },
      order: [['createdAt', 'DESC']],
      raw: true, nest: true
    });
    /* Check if more than 5 OTPs were sent in the current day */
    const startOfDay = moment().startOf('day');
    const todayAttempts = findMail.filter((mail: any) => { return moment(mail.createdAt).isAfter(startOfDay) });

    /** if yes, then return error message */
    if (todayAttempts.length >= attempt) {
      return "ERROR_TOO_MANY_ATTEMPTS_TODAY"
    }

    /* Check if the last OTP was sent less than 3 minutes ago */
    if (findMail.length > 0) {
      const lastAttempt = findMail[0];
      const threeMinutesAgo = moment().subtract(1, 'minutes');

      /** if yes, then return error message */
      if (moment(lastAttempt.createdAt).isAfter(threeMinutesAgo)) {
        return "ERROR_WAIT_BEFORE_RESEND"
      }
    }
    return true
  } catch (e) {
    console.log(e);
    return { status: false, message: e }
  }
}

/** Check user role status and return response accordingly. */
const checkUserRole = async (userId: any) => {
  try {
    let getRoles: any = await getUserRoles(userId)
    const masterRole: any = getRoles.data.realmMappings.find((role: any) => role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
      role.description === global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION);

    if (!masterRole) {
      return false
    }
    return true
  } catch (e) {
    console.log("user role status Exception: ", e);
    return { status: false, message: e }
  }
}

/** Store activity log */
const storeActivityLog = async (message: any, header: any) => {
  try {
    // Combine the message and header into a single object
    const combinedData = {
      message: message,
      header: header
    };
    // Publish a message to the "user activity log" queue for store log
    const verificationQueue: any = RABBITMQ_QUEUE.USER_ACTIVITY_LOG;
    await rabbitmqPublisher.publishMessage(verificationQueue, combinedData);
  } catch (e) {
    console.log("Activity log Exception ", e);
    return { status: false, message: e }
  }
}

const getHash = function (originalFile: any, file: any, mimeType?: string) {
  //** create hash using crc32 */
  const fileHash = crc32(originalFile);
  const Magic = mmm.Magic;
  const magic = new Magic(mmm.MAGIC_MIME_TYPE);
  let hashObject = {};
  if (mimeType) {
    const hash = fileHash + file.size + mimeType;
    /*** SHA1 Logic for re hashing         */
    const reHashing = CryptoJS.SHA1(hash); // hash = 10b5557058836image/png

    /** Generate final hash code using CryptoJs */
    const cryptoHash = CryptoJS.enc.Hex.stringify(reHashing);
    return {
      status: true,
      hash: cryptoHash,
      actualMimeType: mimeType,
    };
  } else {
    return new Promise((resolve, reject) =>
      magic.detectFile(file.path, (err: any, File_mimeType: any) => {
        if (err) {
          hashObject = { status: false, message: err };
          return reject(hashObject);
        } else {
          //============ crc[32|64](hash) + bytes(fileSize) + MimeType ===============;
          const hash = fileHash + file.size + File_mimeType;
          /*** SHA1 Logic for re hashing         */
          const reHashing = CryptoJS.SHA1(hash); // hash = 10b5557058836image/png

          /** Generate final hash code using CryptoJs */
          const cryptoHash = CryptoJS.enc.Hex.stringify(reHashing);
          hashObject = {
            status: true,
            hash: cryptoHash,
            actualMimeType: File_mimeType,
          };
          return resolve(hashObject);
        }
      }),
    );
  }
};

const ReadingFile = (path: string) => {
  return new Promise(function (resolve, reject) {
    let FileObject: any;
    const readStream = fs.createReadStream(path, "utf-8");
    readStream.on("error", (error: any) => {
      console.log(error);
      FileObject = { status: false, data: error };
      reject(FileObject);
    });
    const chunk: any = [];
    readStream.on("data", (data: any) => chunk.push(data));
    readStream.on("end", () => {
      console.log("Reading complete");
      FileObject = { status: true, data: chunk };
      resolve(FileObject);
    });
  });
};


const generateUniqueUsername = async (baseUsername: string, suffix: number = 0) => {
  // Build the candidate: either "baseUsername" or "baseUsername1", "baseUsername2", …
  const attempt = suffix === 0 ? baseUsername : `${baseUsername}${suffix}`;
  // const existing = 'SELECT * FROM USER_ENTITY WHERE USERNAME = ' + `'${attempt}'`;
  const existing = 'SELECT id FROM nv_users WHERE username = ' + `'${attempt}'`;
  const checkUserExist: any = await sequelize.query(existing, {
    type: QueryTypes.SELECT,
  });
  if (checkUserExist.length > 0) {
    return generateUniqueUsername(baseUsername, suffix + 1);
  }
  return attempt;
}

export {
  readHTMLFile,
  GeneratePassword,
  otpAttemptValidation,
  checkUserRole,
  storeActivityLog,
  getHash,
  ReadingFile,
  generateUniqueUsername
}