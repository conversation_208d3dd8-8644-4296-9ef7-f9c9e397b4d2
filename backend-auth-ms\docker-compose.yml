
# version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: keycloak-db
    environment:
      MYSQL_DATABASE: ${DATABASE_NAME}
      MYSQL_USER: ${DATABASE_USER}
      MYSQL_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    ports:
      - '3307:3306'    #Maps port 3306 inside the container (MySQL default) to port 3307 on the host
    volumes:
      - keycloak-db-data:/var/lib/mysql
    networks:
      - keycloak-network

  keycloak:
    image: quay.io/keycloak/keycloak:latest
    container_name: keycloak
    environment:
      KC_BOOTSTRAP_ADMIN_USERNAME: ${KC_BOOTSTRAP_ADMIN_USERNAME}
      KC_BOOTSTRAP_ADMIN_PASSWORD: ${KC_BOOTSTRAP_ADMIN_PASSWORD}
      KC_DB: mysql
      KC_DB_URL_HOST: ${DATABASE_HOST}
      KC_DB_URL_PORT: ${DATABASE_PORT}
      KC_DB_URL_DATABASE: ${DATABASE_NAME}
      KC_DB_USERNAME: ${DATABASE_USER}
      KC_DB_PASSWORD: ${DATABASE_PASSWORD}
    command:
      - start-dev
    ports:
      - 8080:8080
    env_file:
      - ./.env 
    depends_on:
      - mysql
    networks:
      - keycloak-network
    volumes:
      - keycloak-data:/opt/keycloak/data
      
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5673:5672"    # RabbitMQ port
      - "15672:15672"  # RabbitMQ Management UI port
    env_file:
      - ./.env 
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
    networks:
      - keycloak-network

  nginx:
    image: nginx:latest
    ports:
      - '3050:81'  # Map host port 3050 to container port 80
      - '3443:443'  # HTTPS
    volumes:
      -  /d/nginx/nginx-selfsigned.crt:/etc/nginx/nginx-selfsigned.crt
      -  /d/nginx/nginx-selfsigned.key:/etc/nginx/nginx-selfsigned.key
      -  ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - auth-ms
    restart: always
    networks:
      - keycloak-network

  auth-ms:
    image: localhost:5000/backend-auth-ms:1.0.0
    container_name: backend-auth-ms
    ports:
      - '8024:8023'       # port 8023 on the host will forward traffic to port 8023 inside the container
    env_file:
      - ./.env     # Mounts the server .env file    
    command: node ./build/src/index.js  # Start the auth-ms service in development mode
    depends_on:
      - keycloak
    networks:
      - keycloak-network
    
  phpmyadmin:
    image: phpmyadmin/phpmyadmin   # Uses the phpMyAdmin image from Docker Hub
    ports:
      - '8181:80'                 # Maps port 80 inside the container to port 8181 on the host, for phpMyAdmin access
    environment:
      PMA_HOST: mysql            # Connect phpMyAdmin to the MySQL container
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    depends_on:
      - mysql       # Ensures that the 'mysql' service is started before 'phpmyadmin'
    networks:
      - keycloak-network
 
 
  # subscription-ms:
  #   image: localhost:5000/backend-subscription-ms:1.0.0
  #   container_name: backend-subscription-ms
  #   ports:
  #     - '9024:9024'       # port 9024 on the host will forward traffic to port 9024 inside the container
  #   env_file:
  #     - ./.env     # Mounts the server .env file    
  #   command: node ./build/src/index.js  # Start the subscription-ms service in development mode
  #   depends_on:
  #     - mysql
  #   networks:
  #     - keycloak-network

  # payment-ms:
  #   image: localhost:5000/backend-payment-ms:1.0.0
  #   container_name: backend-payment-ms
  #   ports:
  #     - '9026:9026'       # port 9026 on the host will forward traffic to port 9026 inside the container
  #   env_file:
  #     - ./.env     # Mounts the server .env file    
  #   command: node ./build/src/index.js  # Start the auth-ms service in development mode
  #   depends_on:
  #     - mysql
  #   networks:
  #     - keycloak-network 

volumes:
  keycloak-db-data:
  keycloak-data:

networks:
  keycloak-network:
