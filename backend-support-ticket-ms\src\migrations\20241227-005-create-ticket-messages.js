"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("mo_support_ticket_messages", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      ticket_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "mo_support_tickets",
          key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        comment: "Reference to the support ticket",
      },
      message_text: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: "Content of the message",
      },
      message_type: {
        type: Sequelize.ENUM("USER", "AGENT", "SYSTEM", "INTERNAL_NOTE"),
        allowNull: false,
        defaultValue: "USER",
        comment: "Type of message sender",
      },
      is_private: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Whether message is private (internal only)",
      },
      attachment_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: "nv_items",
          key: "id",
        },
        onDelete: "SET NULL",
        onUpdate: "CASCADE",
        comment: "File uploaded via nv_items",
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "nv_users",
          key: "id",
        },
        comment: "ID of user who created the message",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Soft delete timestamp",
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex("mo_support_ticket_messages", ["ticket_id"], {
      name: "idx_ticket_messages_ticket_id",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["message_type"], {
      name: "idx_ticket_messages_type",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["created_by"], {
      name: "idx_ticket_messages_created_by",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["is_private"], {
      name: "idx_ticket_messages_is_private",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["attachment_id"], {
      name: "idx_ticket_messages_attachment_id",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["created_at"], {
      name: "idx_ticket_messages_created_at",
    });

    // Composite indexes for common queries
    await queryInterface.addIndex("mo_support_ticket_messages", ["ticket_id", "message_type"], {
      name: "idx_ticket_messages_ticket_type",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["ticket_id", "is_private"], {
      name: "idx_ticket_messages_ticket_private",
    });

    await queryInterface.addIndex("mo_support_ticket_messages", ["ticket_id", "created_at"], {
      name: "idx_ticket_messages_ticket_created",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_ticket_id");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_type");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_created_by");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_is_private");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_attachment_id");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_created_at");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_ticket_type");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_ticket_private");
    await queryInterface.removeIndex("mo_support_ticket_messages", "idx_ticket_messages_ticket_created");

    // Remove table
    await queryInterface.dropTable("mo_support_ticket_messages");
  },
};
