import multer from "multer";
import multerS3 from "multer-s3";
import { S3Client } from "@aws-sdk/client-s3";
import path from "path";
import crypto from "crypto";
import {
  Item,
  item_status,
  item_type,
  item_external_location,
  item_IEC,
  item_category,
} from "../models/Item";

// AWS S3 Configuration
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

class UploadService {
  // Configure multer for S3 upload
  public multerS3(bucketName: string, folder: string) {
    return multer({
      storage: multerS3({
        s3: s3Client,
        bucket: bucketName,
        metadata: (req: any, file: Express.Multer.File, cb: any) => {
          cb(null, { fieldName: file.fieldname });
        },
        key: (req: any, file: Express.Multer.File, cb: any) => {
          const uniqueSuffix =
            Date.now() + "-" + Math.round(Math.random() * 1e9);
          const fileName = `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`;
          const filePath = `${folder}/${fileName}`;
          cb(null, filePath);
        },
      }),
      fileFilter: this.fileFilter,
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        files: 10, // Maximum 10 files
      },
    });
  }

  // File filter for allowed types
  private fileFilter(
    req: any,
    file: Express.Multer.File,
    cb: (error: Error | null, acceptFile?: boolean) => void
  ) {
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/plain",
      "text/csv",
    ];

    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} is not allowed`));
    }
  }

  // Get file metadata for saving to nv_items table
  public getFileMetadata(
    file: Express.Multer.File & { location?: string },
    organization_id: string,
    created_by: number
  ) {
    try {
      // Generate file hash
      const fileHash = crypto
        .createHash("md5")
        .update(file.originalname + Date.now().toString())
        .digest("hex");

      // Determine item type based on MIME type
      let itemType = item_type.BLOB;
      if (file.mimetype.startsWith("image/")) {
        itemType = item_type.IMAGE;
      } else if (file.mimetype.startsWith("video/")) {
        itemType = item_type.VIDEO;
      } else if (file.mimetype.startsWith("audio/")) {
        itemType = item_type.AUDIO;
      } else if (file.mimetype === "application/pdf") {
        itemType = item_type.PDF;
      }

      // Determine file size unit
      let sizeUnit = item_IEC.B;
      if (file.size > 1024 * 1024 * 1024) {
        sizeUnit = item_IEC.GB;
      } else if (file.size > 1024 * 1024) {
        sizeUnit = item_IEC.MB;
      } else if (file.size > 1024) {
        sizeUnit = item_IEC.KB;
      }

      return {
        item_type: itemType,
        item_name: file.filename || file.originalname,
        item_hash: fileHash,
        item_mime_type: file.mimetype,
        item_extension: path.extname(file.originalname),
        item_size: file.size,
        item_IEC: sizeUnit,
        item_status: item_status.ACTIVE,
        item_external_location: item_external_location.NO,
        item_location: file.location || (file as any).key,
        item_organization_id: organization_id,
        item_category: item_category.SUPPORT_ATTACHMENT,
        created_by: created_by,
        updated_by: created_by,
      };
    } catch (error) {
      console.error("Error generating file metadata:", error);
      return null;
    }
  }

  // Move file in S3 bucket (placeholder for future implementation)
  public async moveFileInBucket(destinationPath: string, itemId: number) {
    try {
      // This would use S3 copy and delete operations in production
      // For now, we'll just update the item location
      return {
        success: true,
        newPath: destinationPath,
      };
    } catch (error) {
      console.error("Error moving file in bucket:", error);
      return {
        success: false,
        error: error,
      };
    }
  }

  // Get file URL
  public getFileUrl(itemId: number): string {
    return `/api/files/${itemId}`;
  }

  // Get download URL
  public getDownloadUrl(itemId: number): string {
    return `/api/files/${itemId}/download`;
  }

  // Check if file is image
  public isImageFile(mimeType: string): boolean {
    return mimeType.startsWith("image/");
  }

  // Check if file is document
  public isDocumentFile(mimeType: string): boolean {
    const docTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/plain",
      "text/csv",
    ];
    return docTypes.includes(mimeType);
  }

  // Format file size
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}

export default new UploadService();
