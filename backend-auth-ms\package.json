{"name": "auth-ms", "version": "1.0.0", "main": "index.js", "scripts": {"start:dev": "nodemon", "dev": "ts-node --files ./src/index.ts", "build": "npx tsc -p .", "generate-swagger": "ts-node src/swagger/swagger-config.ts", "postbuild": "cp -r ./src/locales ./build/src/locales"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@types/swagger-ui-express": "^4.1.7", "amqplib": "^0.10.5", "argon2": "^0.41.1", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "cors": "^2.8.5", "crc": "^4.3.2", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.21.2", "express-session": "^1.18.1", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "keycloak-connect": "^26.0.6", "mmmagic": "^0.5.3", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.12.0", "nodemon": "^3.1.7", "sequelize": "^6.37.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.3"}, "devDependencies": {"@types/express": "^5.0.0", "@types/keycloak-connect": "^7.0.0", "@types/node": "^22.9.0", "@types/swagger-jsdoc": "^6.0.4", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}