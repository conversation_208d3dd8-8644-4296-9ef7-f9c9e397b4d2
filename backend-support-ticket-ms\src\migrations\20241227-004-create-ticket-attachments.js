"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("mo_support_ticket_attachments", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      ticket_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "mo_support_tickets",
          key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        comment: "Reference to the support ticket",
      },
      item_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: "nv_items",
          key: "id",
        },
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
        comment: "Reference to the file in nv_items table",
      },
      attachment_type: {
        type: Sequelize.ENUM("INITIAL", "RESPONSE", "INTERNAL"),
        allowNull: false,
        defaultValue: "INITIAL",
        comment: "Type of attachment (initial submission, response, or internal)",
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: "Optional description of the attachment",
      },
      is_public: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether attachment is visible to customer",
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: "ID of user who uploaded the attachment",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Soft delete timestamp",
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex("mo_support_ticket_attachments", ["ticket_id"], {
      name: "idx_ticket_attachments_ticket_id",
    });

    await queryInterface.addIndex("mo_support_ticket_attachments", ["item_id"], {
      name: "idx_ticket_attachments_item_id",
    });

    await queryInterface.addIndex("mo_support_ticket_attachments", ["attachment_type"], {
      name: "idx_ticket_attachments_type",
    });

    await queryInterface.addIndex("mo_support_ticket_attachments", ["is_public"], {
      name: "idx_ticket_attachments_is_public",
    });

    await queryInterface.addIndex("mo_support_ticket_attachments", ["created_by"], {
      name: "idx_ticket_attachments_created_by",
    });

    await queryInterface.addIndex("mo_support_ticket_attachments", ["created_at"], {
      name: "idx_ticket_attachments_created_at",
    });

    // Composite index for common queries
    await queryInterface.addIndex("mo_support_ticket_attachments", ["ticket_id", "attachment_type"], {
      name: "idx_ticket_attachments_ticket_type",
    });

    await queryInterface.addIndex("mo_support_ticket_attachments", ["ticket_id", "is_public"], {
      name: "idx_ticket_attachments_ticket_public",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_ticket_id");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_item_id");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_type");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_is_public");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_created_by");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_created_at");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_ticket_type");
    await queryInterface.removeIndex("mo_support_ticket_attachments", "idx_ticket_attachments_ticket_public");

    // Remove table
    await queryInterface.dropTable("mo_support_ticket_attachments");
  },
};
