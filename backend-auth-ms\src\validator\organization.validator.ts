import { Jo<PERSON> } from "celebrate";
export default {
    updateOrganization: Joi.object().keys({
        id: Joi.string().required(),
        name: Jo<PERSON>.string().required(),
        alias: Jo<PERSON>.string(),
        email: Joi.string().email().required(),
        description: Joi.string(),
        multiple_location: Joi.number().required(),
        address: Joi.string().required(),
        address1: Joi.string(),
        geo_country: Joi.string().required(),
        geo_state: Joi.string().allow('', null),
        geo_city: Joi.string().allow('', null),
        pin_code: Joi.string().required(),
        contact_person: Joi.string().required(),
        vat_number: Joi.string().required(),
        currency: Joi.string(),
        timezone: Joi.string(),
        facebook_url: Joi.string().uri().allow('', null),
        linkdin_url: Joi.string().uri().allow('', null),
        twitter_url: Joi.string().uri().allow('', null),
        website: Joi.string().uri().required(),
        organization_logo: Joi.string(),
        status: Joi.string().required(),
        created_at: Joi.string().required(),
        createdBy: Joi.string(),
        role: Joi.string(),
        customer_id: Joi.string().required(),
        support_pin: Joi.string().required()
    })
}