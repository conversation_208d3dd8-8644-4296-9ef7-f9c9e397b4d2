import { Router } from "express";
import authController from "../../controller/auth.controller";
import { uploadMulter } from "../../helper/utils";
import authValidator from "../../validator/auth.validator";

import { multerS3 } from "../../helper/upload.helper"
import { FILE_UPLOAD_CONSTANT } from "../../helper/constant";

const multerS3Upload = multerS3(
  process.env.NODE_ENV || "development",
  FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.folder,
);

const router = Router();

/** Change Password API */
/**
 * @swagger
 * /v1/private/auth/change-password:
 *   put:
 *     summary: Change user password
 *     description: Allows an authenticated user to change their password.
 *     tags:
 *       - Auth
 *     security:
 *       - BearerAuth: [] # JWT token authorization
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *                 description: The new password for the user.
 *     responses:
 *       200:
 *         description: Password successfully changed.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Password changed successfully.
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid request payload.
 *       401:
 *         description: Unauthorized, JWT token is missing or invalid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Unauthorized.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Something went wrong.
 */
router.put("/change-password", authValidator.changePassword(), authController.changePassword);

/** Create Staff Member */
/**
 * @swagger
 * /v1/private/auth/create-staff:
 *   post:
 *     summary: Create a new staff member
 *     description: Allows an admin to create a new staff member by providing details such as first name, last name, email, etc.
 *     tags:
 *       - Auth
 *     security:
 *       - BearerAuth: [] # JWT token authorization
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - username
 *               - email
 *               - organization_id
 *               - role
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: The first name of the staff member.
 *               lastName:
 *                 type: string
 *                 description: The last name of the staff member.
 *               username:
 *                 type: string
 *                 description: The username for the staff member (must be unique).
 *               email:
 *                 type: string
 *                 format: email
 *                 description: The email address of the staff member (must be unique).
 *               organization_id:
 *                 type: string
 *                 description: The unique identifier of the organization to which the staff member belongs.
 *               role:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The unique identifier of the role.
 *                     name:
 *                       type: string
 *                       description: The name of the role.
 *                     description:
 *                       type: string
 *                       description: A brief description of the role.
 *                     composite:
 *                       type: boolean
 *                       description: Whether the role is a composite role.
 *                     clientRole:
 *                       type: boolean
 *                       description: Whether the role is a client role.
 *                     containerId:
 *                       type: string
 *                       description: The unique identifier of the container that holds the role.
 *     responses:
 *       201:
 *         description: Staff member successfully created.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Staff member created successfully.
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid request payload.
 *       401:
 *         description: Unauthorized, JWT token is missing or invalid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Unauthorized.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Something went wrong.
 */
router.post("/create-staff", authValidator.createStaffMember(), authController.createStaffMember);

/** Update User API */
/**
 * @swagger
 * /v1/private/auth/update-user:
 *   put:
 *     summary: Update user data
 *     description: This API accepts form data to upload user details.
 *     tags:
 *       - Auth
 *     security:
 *       - BearerAuth: [] # JWT token authorization
 *     requestBody:
 *       required: true
 *       content:
 *          multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Unique identifier for the user.
 *                 example: "12345"
 *               firstName:
 *                 type: string
 *                 description: The first name of the user.
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 description: The last name of the user.
 *                 example: "Doe"
 *               email:
 *                 type: string
 *                 description: The email address of the user.
 *                 example: "<EMAIL>"
 *               userStatus:
 *                 type: string
 *                 description: The status of the user.
 *                 example: "active"
 *               userPhoneNumber:
 *                 type: string
 *                 description: The phone number of the user.
 *                 example: "1234567890"
 *               userCountryCode:
 *                 type: string
 *                 description: The country code of the user's phone number.
 *                 example: "3"
 *               organizationId:
 *                 type: string
 *                 description: The ID of the organization the user belongs to.
 *                 example: "6ed78ffd-629b-4779-b0cc-f8c3a5c2d66c"
 *               userToken:
 *                 type: string
 *                 description: A unique token for the user.
 *                 example: ""
 *               isLoginpin:
 *                 type: boolean
 *                 description: Indicates if the user has a login PIN.
 *                 example: false
 *               updatedBy:
 *                 type: string
 *                 description: The ID of the user who created this record.
 *                 example: "6ed78ffd-629b-4779-b0cc-f8c3a5c2d66c"
 *               userSignature:
 *                 type: string
 *                 format: binary
 *                 description: The user's signature file.
 *     responses:
 *       200:
 *         description: User data successfully uploaded.
 *       400:
 *         description: Bad request.
 */
router.put("/update-user", multerS3Upload.upload("userSignature"), authController.updateUser);

export default router;