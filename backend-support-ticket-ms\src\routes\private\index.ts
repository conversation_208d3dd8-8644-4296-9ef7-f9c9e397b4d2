import express, { Router } from "express";

const routes: Router = express.Router();

import supportTicketRoute from "./supportTicket.routes";
import supportMessageRoute from "./supportMessage.routes";
import supportConfigRoute from "./supportConfig.routes";
import adminRoute from "./admin.routes";

// Support ticket routes (following recipe-ms pattern)
routes.use("/tickets", supportTicketRoute);

// Support message routes (following recipe-ms pattern)
routes.use("/messages", supportMessageRoute);

// Support configuration routes (following recipe-ms pattern)
routes.use("/config", supportConfigRoute);

// Admin routes (following recipe-ms pattern)
routes.use("/admin", adminRoute);

export default routes;
