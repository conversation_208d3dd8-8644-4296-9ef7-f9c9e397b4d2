# 🍽️ TTH Recipe Module - Complete Integration Guide

## 📋 **OVERVIEW**

This document provides a complete integration guide for implementing the TTH Recipe Module into your existing TeamTrainHub HRMS platform. The module has been designed to seamlessly integrate with TTH's existing architecture, following all established patterns and standards.

## 🎯 **INTEGRATION CHECKLIST**

### ✅ **Phase 1: Database Integration**
- [ ] Create MySQL database tables using TTH naming conventions (`nv_*`)
- [ ] Implement Sequelize models with proper associations
- [ ] Add foreign key relationships to existing TTH tables
- [ ] Create database migrations and seeders
- [ ] Set up proper indexes for performance

### ✅ **Phase 2: Backend Integration**
- [ ] Implement TypeScript controllers following TTH patterns
- [ ] Add recipe routes to existing API structure
- [ ] Integrate with TTH's RBAC system
- [ ] Implement multi-tenant data isolation
- [ ] Add internationalization support

### ✅ **Phase 3: System Integration**
- [ ] Integrate with TTH's notification system
- [ ] Connect to RabbitMQ for background processing
- [ ] Add recipe-related activities to audit trail
- [ ] Implement file upload with AWS S3
- [ ] Add search functionality with Elasticsearch

### ✅ **Phase 4: Frontend Integration**
- [ ] Create recipe management UI components
- [ ] Add recipe module to TTH admin panel
- [ ] Implement role-based access controls
- [ ] Add recipe widgets to dashboard
- [ ] Create mobile-responsive interfaces

## 🗄️ **DATABASE INTEGRATION STEPS**

### **Step 1: Create Migration Files**

```bash
# Create recipe module migrations
npx sequelize-cli migration:generate --name create-recipe-tables
npx sequelize-cli migration:generate --name create-recipe-categories
npx sequelize-cli migration:generate --name create-ingredients
npx sequelize-cli migration:generate --name create-recipe-ingredients
npx sequelize-cli migration:generate --name create-recipe-instructions
npx sequelize-cli migration:generate --name create-recipe-reviews
```

### **Step 2: Update TTH Models**

Add recipe relationships to existing TTH models:

```typescript
// models/User.ts - Add recipe relationships
static associate(models: any) {
  // Existing associations...
  
  // Recipe Module Associations
  User.hasMany(models.Recipe, { 
    foreignKey: 'created_by', 
    as: 'createdRecipes' 
  });
  User.hasMany(models.RecipeReview, { 
    foreignKey: 'user_id', 
    as: 'recipeReviews' 
  });
  User.hasMany(models.RecipeFavorite, { 
    foreignKey: 'user_id', 
    as: 'favoriteRecipes' 
  });
}

// models/Branch.ts - Add recipe relationships
static associate(models: any) {
  // Existing associations...
  
  // Recipe Module Associations
  Branch.hasMany(models.Recipe, { 
    foreignKey: 'branch_id', 
    as: 'branchRecipes' 
  });
}
```

### **Step 3: Add Permissions**

```sql
-- Add recipe permissions to nv_permissions table
INSERT INTO nv_permissions (permission_name, permission_module, permission_action, permission_status, organization_id) VALUES
('Recipe Create', 'recipe', 'CREATE', 'ACTIVE', 'ORG_ID'),
('Recipe Read', 'recipe', 'READ', 'ACTIVE', 'ORG_ID'),
('Recipe Update', 'recipe', 'UPDATE', 'ACTIVE', 'ORG_ID'),
('Recipe Delete', 'recipe', 'DELETE', 'ACTIVE', 'ORG_ID'),
('Recipe Approve', 'recipe', 'APPROVE', 'ACTIVE', 'ORG_ID');
```

## 🎮 **CONTROLLER INTEGRATION**

### **Step 1: Add Recipe Routes**

```typescript
// routes/index.ts - Add recipe routes
import recipeRoutes from './recipe.routes';
import ingredientRoutes from './ingredient.routes';
import recipeCategoryRoutes from './recipeCategory.routes';

// Add to existing route structure
app.use('/api/v1/recipes', recipeRoutes);
app.use('/api/v1/ingredients', ingredientRoutes);
app.use('/api/v1/recipe-categories', recipeCategoryRoutes);
```

### **Step 2: Update Middleware**

```typescript
// middleware/auth.ts - Add recipe permissions
export const checkRecipePermission = (action: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const hasPermission = await validateRecipePermission(
        req.user.id,
        action,
        req.user.organization_id
      );

      if (!hasPermission) {
        return res.status(403).json({
          status: false,
          message: res.__('ERROR_INSUFFICIENT_PERMISSION')
        });
      }

      next();
    } catch (error) {
      return res.status(500).json({
        status: false,
        message: res.__('ERROR_INTERNAL_SERVER')
      });
    }
  };
};
```

## 🔔 **NOTIFICATION INTEGRATION**

### **Recipe Event Notifications**

```typescript
// services/recipeNotificationService.ts
export const sendRecipeNotifications = async (
  recipeId: number,
  eventType: string,
  userId: number,
  organizationId: string
) => {
  const recipe = await Recipe.findByPk(recipeId);
  
  switch (eventType) {
    case 'RECIPE_CREATED':
      await createNotification({
        notification_content: `New recipe "${recipe.recipe_name}" created`,
        notification_subject: 'New Recipe Created',
        notification_type: 'RECIPE_CREATED',
        from_user_id: userId,
        role_ids: [ROLE_CONSTANT.BRANCH_MANAGER],
        organization_id: organizationId
      });
      break;

    case 'RECIPE_APPROVED':
      await createNotification({
        notification_content: `Recipe "${recipe.recipe_name}" has been approved`,
        notification_subject: 'Recipe Approved',
        notification_type: 'RECIPE_APPROVED',
        to_user_id: recipe.created_by,
        organization_id: organizationId
      });
      break;
  }
};
```

## 🔍 **SEARCH INTEGRATION**

### **Elasticsearch Configuration**

```typescript
// config/elasticsearch.ts
import { Client } from '@elastic/elasticsearch';

export const elasticsearchClient = new Client({
  node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200'
});

// Index recipe data
export const indexRecipe = async (recipe: any) => {
  await elasticsearchClient.index({
    index: 'tth_recipes',
    id: recipe.id,
    body: {
      recipe_name: recipe.recipe_name,
      recipe_description: recipe.recipe_description,
      cuisine_type: recipe.cuisine_type,
      difficulty_level: recipe.difficulty_level,
      meal_type: recipe.meal_type,
      recipe_tags: JSON.parse(recipe.recipe_tags || '[]'),
      dietary_restrictions: JSON.parse(recipe.dietary_restrictions || '[]'),
      organization_id: recipe.organization_id,
      created_at: recipe.created_at
    }
  });
};
```

## 📱 **FRONTEND INTEGRATION**

### **Admin Panel Integration**

```typescript
// admin/components/RecipeManagement.tsx
import React from 'react';
import { RecipeList } from './RecipeList';
import { RecipeForm } from './RecipeForm';
import { usePermissions } from '../hooks/usePermissions';

export const RecipeManagement: React.FC = () => {
  const { hasPermission } = usePermissions();

  return (
    <div className="recipe-management">
      <div className="page-header">
        <h1>Recipe Management</h1>
        {hasPermission('recipe', 'CREATE') && (
          <button className="btn btn-primary">
            Add New Recipe
          </button>
        )}
      </div>
      
      <RecipeList />
    </div>
  );
};
```

### **Dashboard Widget**

```typescript
// dashboard/widgets/RecipeWidget.tsx
export const RecipeWidget: React.FC = () => {
  const [recentRecipes, setRecentRecipes] = useState([]);

  useEffect(() => {
    fetchRecentRecipes();
  }, []);

  return (
    <div className="widget recipe-widget">
      <div className="widget-header">
        <h3>Recent Recipes</h3>
        <Link to="/recipes">View All</Link>
      </div>
      
      <div className="widget-content">
        {recentRecipes.map(recipe => (
          <div key={recipe.id} className="recipe-item">
            <img src={recipe.recipe_image_url} alt={recipe.recipe_name} />
            <div className="recipe-info">
              <h4>{recipe.recipe_name}</h4>
              <span className="cuisine">{recipe.cuisine_type}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 🔧 **CONFIGURATION UPDATES**

### **Environment Variables**

```env
# Recipe Module Configuration
RECIPE_IMAGE_UPLOAD_PATH=/uploads/recipes
RECIPE_MAX_FILE_SIZE=5MB
RECIPE_ALLOWED_EXTENSIONS=jpg,jpeg,png,webp

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=tth_

# Nutrition API (Optional)
NUTRITION_API_KEY=your_nutrition_api_key
NUTRITION_API_URL=https://api.nutritionix.com/v1_1
```

### **Package.json Updates**

Add recipe-specific scripts:

```json
{
  "scripts": {
    "recipe:migrate": "npx sequelize-cli db:migrate --migrations-path src/migrations/recipe",
    "recipe:seed": "npx sequelize-cli db:seed --seeders-path src/seeders/recipe",
    "recipe:index": "ts-node src/scripts/indexRecipes.ts"
  }
}
```

## 🚀 **DEPLOYMENT STEPS**

### **Step 1: Database Migration**
```bash
npm run recipe:migrate
npm run recipe:seed
```

### **Step 2: Elasticsearch Setup**
```bash
npm run recipe:index
```

### **Step 3: Permission Assignment**
```bash
# Assign recipe permissions to roles
npm run assign-recipe-permissions
```

### **Step 4: Test Integration**
```bash
npm run test:recipe-integration
```

## 📊 **MONITORING & ANALYTICS**

### **Recipe Analytics Dashboard**

Track key metrics:
- Total recipes created per organization
- Most popular recipes (by views/favorites)
- Recipe approval rates
- User engagement with recipes
- Nutritional analysis trends

### **Performance Monitoring**

Monitor:
- Recipe search response times
- Image upload performance
- Database query optimization
- Elasticsearch indexing performance

## 🔒 **SECURITY CONSIDERATIONS**

1. **Data Isolation**: Ensure organization-level data isolation
2. **File Upload Security**: Validate file types and sizes
3. **Input Sanitization**: Sanitize all recipe content
4. **Permission Validation**: Check permissions on every request
5. **Rate Limiting**: Implement rate limiting for recipe operations

## 📚 **DOCUMENTATION UPDATES**

Update existing TTH documentation:
1. Add recipe module to API documentation
2. Update user manual with recipe features
3. Add recipe permissions to admin guide
4. Update database schema documentation

---

## 🎉 **CONCLUSION**

The TTH Recipe Module is now fully integrated with your TeamTrainHub HRMS platform, following all established patterns and maintaining consistency with the existing system architecture. The module provides comprehensive recipe management capabilities while respecting multi-tenancy, internationalization, and security requirements.

For any questions or support, refer to the detailed documentation files:
- `README.md` - Complete module documentation
- `TTH_RECIPE_CONTROLLER_EXAMPLE.md` - Implementation examples
- `API_REFERENCE.md` - API documentation
- `EXAMPLE_USAGE.md` - Practical usage examples
