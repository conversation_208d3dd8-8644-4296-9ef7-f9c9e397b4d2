
import moment from "moment";
import multer from "multer";
import { v4 as uuidv4 } from 'uuid';
import path from "path";
import fs from "fs";
import jwt from "jsonwebtoken";


/** Generate OTP Functions */
const generateOtp = async (n: number) => {
    /** Generate a random number with exactly 'n' digits */
    const val =
        Math.floor(Math.random() * (9 * Math.pow(10, n - 1))) + Math.pow(10, n - 1);
    return val;
};

/** Function to generate a OTP Expiration Time */
const otpExpTime = async () => {
    /**  Get the current date and time and add OTP expiration time (in seconds) to it */
    const time = moment(new Date())  // Get the current date and time
        .add(global.config.OTP_EXPIRE_TIME, "seconds")  // Add the configured OTP expiration time
        .format("YYYY-MM-DD HH:mm:ss"); // Format the time to a readable string format (YYYY-MM-DD HH:mm:ss)
    return time;
};

/* Function to generate a UUID for email verification */
const generateVerificationToken = (): string => uuidv4();

const uploadMulter = (destination_path: string) => {
    /** Configure Multer's disk storage */
    const storage = multer.diskStorage({
        /** Define the destination folder for file uploads */
        destination: (req: any, file: any, cb: any) => {
            /** Resolve the upload path relative to the current directory */
            const uploadPath = path.resolve(__dirname, "../uploads/", destination_path);
            try {
                /** Check if the directory exists, if not, create it recursively */
                if (!fs.existsSync(uploadPath)) {
                    fs.mkdirSync(uploadPath, { recursive: true });
                }
            } catch (error) {
                /** Log an error if directory creation fails */
                console.log("Error creating directory:", error);
            }
            /** Pass the resolved upload path to Multer */
            cb(null, uploadPath);
        },
        /** Define the naming convention for uploaded files */
        filename: (req: any, file: any, cb: any) => {
            /**  Generate a unique filename using the current timestamp and original file name */
            cb(null, Date.now() + "-" + file.originalname); // File naming
        },
    });
    /** Return the Multer instance with the configured storage */
    return multer({ storage: storage });
};

const generateToken = async function (user: any) {
    try {
        const payload = user;
        const token = jwt.sign(payload, global.config.JWT_SECRET, {
            expiresIn: global.config.JWT_EXIPIRATION_TIME,
        });
        return token;
    } catch (e: any) {
        console.log(e);
    }
};

export {
    generateOtp,
    otpExpTime,
    generateVerificationToken,
    uploadMulter, generateToken
};