'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);
    const slaDate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours from now

    await queryInterface.bulkInsert('mo_support_tickets', [
      {
        ticket_number: 'ORG-001-TKT-001',
        organization_id: 'org_001',
        submitter_name: '<PERSON>',
        submitter_email: '<EMAIL>',
        submitter_phone: '+1234567890',
        subject: 'Unable to export employee reports',
        description: 'I am trying to export the monthly employee report but getting an error message. The export button seems to be not working properly.',
        module_type: 'HRMS',
        issue_type: 'EXPORT_ISSUE',
        priority: 'HIGH',
        ticket_status: 'OPEN',
        is_private: true,
        sla_due_date: slaDate,
        created_by: 0, // System user for public submissions
        created_at: twoDaysAgo,
        updated_at: twoDaysAgo
      },
      {
        ticket_number: 'ORG-001-TKT-002',
        organization_id: 'org_001',
        submitter_name: '<PERSON>',
        submitter_email: '<EMAIL>',
        submitter_phone: null,
        subject: 'Feature request: Dark mode for dashboard',
        description: 'It would be great to have a dark mode option for the dashboard. Many users work late hours and would appreciate this feature.',
        module_type: 'OTHER',
        issue_type: 'FEATURE_REQUEST',
        priority: 'LOW',
        ticket_status: 'ASSIGNED',
        is_private: true,
        assigned_to_user_id: 2,
        assigned_at: yesterday,
        assigned_by: 1,
        sla_due_date: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days
        created_by: 0,
        created_at: twoDaysAgo,
        updated_at: yesterday
      },
      {
        ticket_number: 'ORG-002-TKT-001',
        organization_id: 'org_002',
        submitter_name: 'Mike Johnson',
        submitter_email: '<EMAIL>',
        submitter_phone: '+1987654321',
        subject: 'Critical bug: System crashes on project creation',
        description: 'Every time I try to create a new project, the system crashes and I get logged out. This is blocking our team from starting new projects.',
        module_type: 'PMS',
        issue_type: 'BUG',
        priority: 'EMERGENCY',
        ticket_status: 'IN_PROGRESS',
        is_private: true,
        assigned_to_user_id: 3,
        assigned_at: yesterday,
        assigned_by: 1,
        first_response_at: yesterday,
        sla_due_date: new Date(now.getTime() + 4 * 60 * 60 * 1000), // 4 hours
        created_by: 0,
        created_at: yesterday,
        updated_at: now
      },
      {
        ticket_number: 'ORG-002-TKT-002',
        organization_id: 'org_002',
        submitter_name: 'Sarah Wilson',
        submitter_email: '<EMAIL>',
        submitter_phone: '+1555123456',
        subject: 'How to setup automated reports?',
        description: 'I need help setting up automated monthly reports for our department. Can someone guide me through the process?',
        module_type: 'HRMS',
        issue_type: 'QUERY',
        priority: 'MEDIUM',
        ticket_status: 'RESOLVED',
        is_private: true,
        assigned_to_user_id: 2,
        assigned_at: twoDaysAgo,
        assigned_by: 1,
        first_response_at: twoDaysAgo,
        resolved_at: yesterday,
        resolved_by: 2,
        resolution_note: 'Provided step-by-step guide for setting up automated reports. User confirmed the solution works.',
        rating: 5,
        review_comment: 'Excellent support! Very helpful and quick response.',
        reviewed_at: yesterday,
        sla_due_date: new Date(twoDaysAgo.getTime() + 24 * 60 * 60 * 1000),
        created_by: 0,
        created_at: twoDaysAgo,
        updated_at: yesterday
      },
      {
        ticket_number: 'ORG-003-TKT-001',
        organization_id: 'org_003',
        submitter_name: 'David Brown',
        submitter_email: '<EMAIL>',
        submitter_phone: null,
        subject: 'Integration with third-party API',
        description: 'We need to integrate our system with a third-party payroll API. What are the available options and documentation?',
        module_type: 'HRMS',
        issue_type: 'TECHNICAL_SUPPORT',
        priority: 'MEDIUM',
        ticket_status: 'UNDER_REVIEW',
        is_private: true,
        assigned_to_user_id: 3,
        assigned_at: yesterday,
        assigned_by: 1,
        first_response_at: yesterday,
        sla_due_date: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days
        created_by: 0,
        created_at: yesterday,
        updated_at: now
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('mo_support_tickets', null, {});
  }
};
