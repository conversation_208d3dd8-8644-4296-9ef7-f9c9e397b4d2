import axios from "axios";
import jwt from "jsonwebtoken";
import moment from "moment";

/** get Keycloak admin token */
const getKeycloakAdminToken = async () => {
  try {
    const keycloakAdminUrl = `${global.config.KEYCLOAK_TOKEN_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token`;
    const keycloakAdminClientSecret = global.config.KEYCLOAK_SECRET_KEY;
    const keycloakAdminClientId = global.config.KEYCLOAK_CLIENT_ID;

    // Define the token exchange URL and data
    const tokenData = new URLSearchParams();
    tokenData.append("grant_type", "client_credentials");
    tokenData.append("client_id", keycloakAdminClientId);
    tokenData.append("client_secret", keycloakAdminClientSecret); // Use your actual client secret

    // Make a request to exchange the code for a token
    const response = await axios.post(keycloakAdminUrl, tokenData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
    // Return the access token
    return response.data.access_token;
  } catch (e: any) {
    console.log("Exception Token: ", e.response); // Log any exceptions that occur
    if (e.response && e.response.status !== 201) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    } else {
      return { status: false };
    }
  }
};

/** Get User token from username and password */
const getUserToken = async (username: any, password: any) => {
  try {
    const keycloakAdminUrl = `${global.config.KEYCLOAK_TOKEN_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token`;
    const keycloakAdminClientId = global.config.KEYCLOAK_CLIENT_ID;
    const keycloakAdminClientSecret = global.config.KEYCLOAK_SECRET_KEY;

    // Define the token exchange URL and data
    const tokenData = new URLSearchParams();
    tokenData.append("grant_type", "password");
    tokenData.append("client_id", keycloakAdminClientId);
    tokenData.append("client_secret", keycloakAdminClientSecret); // Add secret if required
    tokenData.append("username", username);
    tokenData.append("password", password);

    // Make a request to exchange the code for a token
    const response = await axios.post(keycloakAdminUrl, tokenData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
    // Return the access token
    return response.data;
  } catch (e: any) {
    console.log("Exception User Token: ", e.response); // Log any exceptions that occur
    if (e.response && e.response.status !== 201) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.error_description,
        statusText: e.response.error,
      }; // Return false if an exception is caught
    }
  }
};

/** Create User in Keycloak */
const createUsers = async (user: any) => {
  const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users`;
  const token = await getKeycloakAdminToken(); // Fetch the token

  if (!token) {
    console.error("Failed to retrieve Keycloak admin token.");
    return false; // Return early if token retrieval fails
  }
  // Prepare the user payload with the necessary fields
  try {
    const userPayload: any = {
      username: user.username,
      enabled: user.enabled ?? true,
      emailVerified: user.emailVerified ?? false,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      attributes: {
        userCountryCode: user.userCountryCode,
        userPhoneNumber: user.userPhoneNumber,
        isLoginpin: 0,
        webAppToken: "",
        appToken: "",
        organizationId: user.organization_id,
        createdBy: user.createdBy ?? "",
        updatedBy: user.updatedBy ?? "",
        userStatus: user.userStatus ? user.userStatus : "pending",
        userToken: user.userToken,
      },
    };
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "POST",
      userPayload,
      { Authorization: `Bearer ${token}`, "Content-Type": "application/json" }
    );
    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    /** Get the user data from URL */
    let userData: any = await getData(response.headers.location);
    return {
      status: true,
      statusText: response.statusText,
      data: userData.data,
    }; // Return true if the user is created successfully in Keycloak
  } catch (e: any) {
    console.log("User Creation Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 201) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Get user data from token */
const getUserFromToken = async (token: any) => {
  try {
    const decodedToken = jwt.decode(token);
    return decodedToken;
  } catch (e: any) {
    console.log("Get User from token Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Create Organization */
const createOrganization = async (org: any) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations`;
    const token = await getKeycloakAdminToken(); // Fetch the token
    const orgPayload: any = {
      name: org.name,
      redirectUrl: org.redirectUrl ?? "",
      description: org.description ?? "",
      domains: [
        {
          name: org.domain,
        },
      ],
      attributes: {
        multiple_location: [org.multiple_location],
        email: [org.email],
        website: [org.website],
        created_at: [moment().format("YYYY-MM-DD HH:mm:ss")],
        status: ["active"],
        customer_id: [await generateUniqueDigitNumber(8)],
        support_pin: [await generateUniqueDigitNumber(4)],
      },
    };
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "POST",
      orgPayload,
      { Authorization: `Bearer ${token}`, "Content-Type": "application/json" }
    );
    if (response?.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    /** Get the organization data from URL */
    let orgData: any = await getData(response.headers.location);
    return {
      status: true,
      statusText: response.statusText,
      orgData: orgData.data,
    }; // Return true if the user is created successfully in Keycloak
  } catch (e: any) {
    console.log("Create Organization Exception: ", e); // Log any exceptions that occur
    if (e.response && e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Get Data from URL of organization/User */
const getData = async (url: any, token: any = null) => {
  try {
    token = token ? token : await getKeycloakAdminToken(); // Fetch the token

    const response: any = await makeRequest(url, "GET", null, {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    });
    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    if (response.data && response.data.attributes) {
      const attributes = response.data.attributes;
      // Transform the attributes by converting array values to single values
      const transformedAttributes: any = {};
      Object.keys(attributes).forEach((key) => {
        // Take the first element of the array or keep the value if it's already a single value
        transformedAttributes[key] = Array.isArray(attributes[key])
          ? attributes[key][0]
          : attributes[key];
      });

      // Update the response with the transformed attributes
      response.data.attributes = transformedAttributes;
    }
    return {
      status: true,
      message: response,
      statusText: response.statusText,
      data: response.data,
    }; // Return true if the user is created successfully in Keycloak
  } catch (e: any) {
    console.log("Get data By Id Exception: ", e); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Make HTTP request */
const makeRequest = async (
  url: any,
  method: any,
  payload: any = null,
  headers: any = null
) => {
  try {
    const token = await getKeycloakAdminToken(); // Fetch the token
    headers = headers
      ? headers
      : {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        };
    const config: any = {
      method,
      url,
      headers: headers,
    };
    if (payload) {
      config.data = payload; // Attach the payload for methods like POST, PUT, etc.
    }

    const response = await axios(config);
    return response; // Return the response data
  } catch (e: any) {
    console.log("axios Exception: ", e); // Log any exceptions that occur
    if (e.response && e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.error_description
          ? e.response.data.error_description
          : e.response.data.errorMessage,
        statusText: e.response.statusText,
        field: e.response.data?.field,
      }; // Return false if an exception is caught
    }
  }
};

/** Update User Information */
const updateUsers = async (user: any, token: any = null) => {
  try {
    let userId = user.userId;
    if (!userId) {
      return { status: false, statusCode: 404, message: "User not found" };
    }
    // Construct the payload directly from the provided user object
    const userPayload: any = {
      emailVerified: user.emailVerified, // Static field
      firstName: user.firstName,
      lastName: user.lastName,
      username: user.username,
      email: user.email,
      attributes: {
        userCountryCode: user.attributes?.userCountryCode || "",
        userPhoneNumber: user.attributes?.userPhoneNumber || "",
        organizationId: user.attributes?.organizationId || "",
        userStatus: user.attributes?.userStatus || "active",
        isLoginpin: user.attributes?.isLoginpin || "0",
        userToken: user.attributes?.userToken || "",
        createdBy: user.attributes?.createdBy || "",
        updatedBy: userId, // Updated by current user,
        otp: user.attributes?.otp || "",
        otpExpireTime: user.attributes?.otpExpireTime || "",
      },
    };
    if (user.attributes?.userSignature) {
      userPayload.attributes.userSignature = user.attributes?.userSignature;
    }

    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}`;
    token = token ? token : await getKeycloakAdminToken(); // Fetch the token

    const response: any = await makeRequest(
      keycloakRealmUrl,
      "PUT",
      userPayload,
      { Authorization: `Bearer ${token}`, "Content-Type": "application/json" }
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    return { status: true, statusText: response.statusText }; // Return true if the user is Updated successfully in Keycloak
  } catch (e: any) {
    console.log("Update User Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Reset password  */
const resetPassword = async (
  userId: any,
  newPassword: any,
  token: any = null
) => {
  try {
    let headers = token
      ? { Authorization: `Bearer ${token}`, "Content-Type": "application/json" }
      : null;
    if (!userId || !newPassword) {
      return {
        status: false,
        message: "User ID and new password are required.",
      };
    }

    // Prepare the payload for password reset
    const payload: any = {
      temporary: false, // Set to false so the password is permanent
      type: "password",
      value: newPassword, // The new password
    };

    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}/reset-password`; // URL for resetting password
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "PUT",
      payload,
      headers
    );
    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return { status: true, statusText: response.statusText };
  } catch (e: any) {
    console.log("Reset Password Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Get All Realm Roles */
const getRealmsRoles = async () => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/roles`;
    const response: any = await makeRequest(keycloakRealmUrl, "GET", null);

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return {
      status: true,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (e: any) {
    console.log("Get Roles Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Assign Roles to Users */
const assignRolesToUser = async (userId: any, roles: any[]) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}/role-mappings/realm`;
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "POST",
      roles,
      null
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return { status: true, statusText: response.statusText };
  } catch (e: any) {
    console.log("Assign Roles Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Join user to organization */
const joinUserToOrg = async (orgId: any, userId: any) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${orgId}/members `;
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "POST",
      userId,
      null
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return { status: true, statusText: response.statusText };
  } catch (e: any) {
    console.log("Join User Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Update organization Data */
const updateOrg = async (organization: any, token: any) => {
  try {
    let orgId = organization.id;
    let orgPayload: any = {};
    if (!orgId) {
      return {
        status: false,
        statusCode: 404,
        message: "Organization not found",
      };
    }
    let type = organization.type;
    // Construct the payload directly from the provided user object
    /**
     * Note: in keycloak organization name can't allowed space. so added below conditions.
     * check if organization name contains spaces, if yes then join with underscore and stored into keycloak.
     */
    organization.name =
      organization.name && organization.name.includes(" ")
        ? organization.name.replace(/\s+/g, "_")
        : organization.name;
    if (type == "body") {
      const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${orgId}`;

      /** Fetch organization data */
      let getOrgData: any = await getData(keycloakRealmUrl, token);
      orgPayload = {
        name: organization.name,
        alias: getOrgData.data.alias,
        description: organization.description ?? '""',
        domains: [
          {
            name: organization.domains,
          },
        ],
        attributes: {
          multiple_location: [organization?.multiple_location],
          website: [organization?.website],
          email: [organization?.email],
          contact_person: [organization?.contact_person],
          address: [organization?.address],
          address1: [organization?.address1],
          geo_country: [organization.geo_country], // "IND"
          geo_state: [organization.geo_state], //"IND-ASS"
          geo_city: [organization.geo_city], // "IND-ASS-BON"
          pin_code: [organization.pin_code], // "IND-ASS-BON"
          vat_number: [organization?.vat_number],
          currency: [organization?.currency],
          timezone: [organization?.timezone],
          facebook_url: [organization?.facebook_url],
          linkdin_url: [organization?.linkdin_url],
          twitter_url: [organization?.twitter_url],
          organization_logo: [organization?.organization_logo],
          createdBy: [organization.createdBy],
          updatedBy: [organization.updatedBy],
          status: [organization.status],
          created_at: [organization.created_at],
          support_pin: [organization.support_pin],
          customer_id: [organization.customer_id],
        },
      };
    } else {
      // Construct the payload directly from the provided user object
      orgPayload = {
        id: organization.id,
        name: organization.name,
        redirectUrl: organization.redirectUrl ?? "",
        description: organization.description ?? "",
        domains: [{ name: organization.domains[0].name }],
      };
      // Loop through the keys of 'attributes' dynamically
      Object.keys(organization?.attributes || {}).forEach((attr) => {
        const value = organization?.attributes[attr];

        // Make sure the attributes object exists before trying to set properties
        if (!orgPayload.attributes) {
          orgPayload.attributes = {}; // Initialize it if undefined
        }
        orgPayload.attributes[attr] = value ? [`${value}`] : [""];
      });
    }
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${orgId}`;
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "PUT",
      orgPayload,
      { Authorization: `Bearer ${token}`, "Content-Type": "application/json" }
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    return { status: true, statusText: response.statusText }; // Return true if the user is Updated successfully in Keycloak
  } catch (e: any) {
    console.log("Update Organization Exception: ", e.response); // Log any exceptions that occur
    if (e?.response?.status !== 200) {
      return {
        status: false,
        statusCode: e.response?.status,
        message: e.response?.data.errorMessage,
        statusText: e.response?.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Get Predefined realm roles */
const getPredefinedRealmRoles = async (userId: any) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/ui-ext/available-roles/users/${userId}?first=0&max=101`;

    const response: any = await makeRequest(
      keycloakRealmUrl,
      "GET",
      null,
      null
    );
    // console.log("response", response)
    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      }; // Return true if the user is created successfully in Keycloak
    }
    return {
      status: true,
      statusText: response.statusText,
      data: response.data,
    }; // Return true if the user is Updated successfully in Keycloak
  } catch (e: any) {
    console.log("Get Predefined Roles Exception: ", e.response); // Log any exceptions that occur
    if (e?.response?.status !== 200) {
      return {
        status: false,
        statusCode: e.response?.status,
        message: e.response?.data.errorMessage,
        statusText: e.response?.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Assigned Predefined realm role to user */
const assignPredefinedRoleToUser = async (
  userId: any,
  roles: any[],
  clientId: any
) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}/role-mappings/clients/${clientId}`;
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "POST",
      roles,
      null
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return { status: true, statusText: response.statusText };
  } catch (e: any) {
    console.log("Assign Predefined Roles Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Get user role */
const getUserRoles = async (userId: any) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/users/${userId}/role-mappings`;
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "GET",
      null,
      null
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return {
      status: true,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (e: any) {
    console.log("Get User Roles Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

/** Get Access Token from Refresh Token */
const getNewTokenFromRefreshToken = async (refreshToken: any) => {
  try {
    const keycloakAdminUrl = `${global.config.KEYCLOAK_TOKEN_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/protocol/openid-connect/token`;
    const keycloakAdminClientId = global.config.KEYCLOAK_CLIENT_ID;
    const keycloakAdminClientSecret = global.config.KEYCLOAK_SECRET_KEY;

    // Define the token exchange URL and data
    const tokenData = new URLSearchParams();
    tokenData.append("grant_type", "refresh_token");
    tokenData.append("client_id", keycloakAdminClientId);
    tokenData.append("client_secret", keycloakAdminClientSecret); // Add secret if required
    tokenData.append("refresh_token", refreshToken);

    // Make a request to exchange the code for a token
    const response = await axios.post(keycloakAdminUrl, tokenData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
    // Return the access token
    return response.data;
  } catch (e: any) {
    console.log("Exception User Token: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 201) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.error_description,
        statusText: e.response.error,
      }; // Return false if an exception is caught
    }
  }
};

/** Delete Organization */
const deleteOrganization = async (orgId: any) => {
  try {
    const keycloakRealmUrl = `${global.config.KEYCLOAK_BASE_URL}${global.config.KEYCLOAK_REALM_NAME}/organizations/${orgId}`;
    const response: any = await makeRequest(
      keycloakRealmUrl,
      "DELETE",
      null,
      null
    );

    if (response.status == false) {
      return {
        status: response.status,
        statusText: response.statusText,
        statusCode: response.statusCode,
        message: response.message,
      };
    }
    return {
      status: true,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (e: any) {
    console.log("Delete Organization Exception: ", e.response); // Log any exceptions that occur
    if (e.response.status !== 200) {
      return {
        status: false,
        statusCode: e.response.status,
        message: e.response.data.errorMessage,
        statusText: e.response.statusText,
      }; // Return false if an exception is caught
    }
  }
};

const generateUniqueDigitNumber = async (length: number) => {
  const digits = "0123456789".split("");

  // Shuffle using Fisher-Yates algorithm
  for (let i = digits.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [digits[i], digits[j]] = [digits[j], digits[i]];
  }

  // Slice first `length` digits and join
  return digits.slice(0, length).join("");
};
export {
  createUsers,
  getKeycloakAdminToken,
  getUserFromToken,
  createOrganization,
  getData,
  makeRequest,
  updateUsers,
  resetPassword,
  getUserToken,
  getRealmsRoles,
  joinUserToOrg,
  assignRolesToUser,
  updateOrg,
  getPredefinedRealmRoles,
  assignPredefinedRoleToUser,
  getUserRoles,
  getNewTokenFromRefreshToken,
  deleteOrganization,
  generateUniqueDigitNumber,
};
