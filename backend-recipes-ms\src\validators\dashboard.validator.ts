import { celebrate, Joi, Segments } from "celebrate";

// Valid date range options for dashboard
const dateRangeOptions = [
  "today",
  "this_week",
  "this_month",
  "last_7_days",
  "last_month",
  "last_30_days",
  "last_90_days",
  "last_year",
  "custom", // For custom date range
];

/**
 * Validator for dashboard overview endpoint
 * Validates date_range query parameter
 */
const getDashboardOverviewValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .default("last_30_days")
        .description("Date range for dashboard analytics"),
      category_type: Joi.string()
        .optional()
        .description("Filter by category type/name"),
    },
  });

/**
 * Validator for dashboard export endpoint
 * Validates format and date_range query parameters
 */
const exportDashboardDataValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      format: Joi.string()
        .valid("json", "csv")
        .default("json")
        .description("Export format"),
      date_range: Joi.string()
        .valid(...dateRangeOptions)
        .default("last_30_days")
        .description("Date range for exported data"),
    },
  });

// CTA Analytics validator moved to analytics.validator.ts for unified access

// Contact Analytics validator moved to analytics.validator.ts for unified access

export default {
  getDashboardOverviewValidator,
  exportDashboardDataValidator,
};
