import { keycloak } from '../keycloak/keycloak'
const Jwt = require("jsonwebtoken");
import { StatusCodes } from "http-status-codes";

const userAuth = async (req: any, res: any, next: any) => {
    try {
        /** check Token is present  */
        if (!req.headers.authorization) {
            return res.status(400).send({ status: false, message: res.__('ERROR_TOKEN_REQUIRED') })
        }
        const token: any = req.headers?.authorization?.split(" ")[1];

        // Decode the token (no verification, just extracting the payload)
        const decoded = Jwt.decode(token);

        if (req.headers.authorization) {
            let access_token = decoded.token;
            keycloak.grantManager
                // .validateAccessToken(token)
                .validateAccessToken(access_token)
                .then((result) => {
                    req.token = result;
                    if (result === false) {
                        return res.status(401).json({
                            status: false,
                            message: res.__("ERROR_INVALID_TOKEN")
                        });
                    } else {
                        const decoded = Jwt.decode(result);
                        req.user = decoded;
                        next();
                    }
                })
        }
    } catch (e: any) {
        console.log(e);
        if (e.message == "jwt malformed") {
            return res
                .status(StatusCodes.UNAUTHORIZED)
                .send({ status: false, message: res.__("ERROR_TOKEN_NOT_FOUND") });
        } else {
            if (e.name == "TokenExpiredError") {
                return res
                    .status(StatusCodes.UNAUTHORIZED)
                    .send({
                        status: false,
                        message: res.__("FAIL_TOKEN_EXPIRED"),
                    });
            } else {
                return res.status(401).send({ status: false, message: e.message });
            }
        }
    }
}

export default userAuth;
