"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add indexes for better query performance
    
    // Unique index for ticket number
    await queryInterface.addIndex("mo_support_tickets", ["ticket_number"], {
      name: "idx_tickets_ticket_number",
      unique: true,
    });

    // Organization-based queries (most common)
    await queryInterface.addIndex("mo_support_tickets", ["organization_id"], {
      name: "idx_tickets_organization_id",
    });

    // User-based queries
    await queryInterface.addIndex("mo_support_tickets", ["ticket_owner_user_id"], {
      name: "idx_tickets_owner_user_id",
    });

    await queryInterface.addIndex("mo_support_tickets", ["assigned_to_user_id"], {
      name: "idx_tickets_assigned_to_user_id",
    });

    // Status and priority filtering
    await queryInterface.addIndex("mo_support_tickets", ["ticket_status"], {
      name: "idx_tickets_status",
    });

    await queryInterface.addIndex("mo_support_tickets", ["ticket_priority"], {
      name: "idx_tickets_priority",
    });

    // Module and type filtering
    await queryInterface.addIndex("mo_support_tickets", ["ticket_module"], {
      name: "idx_tickets_module",
    });

    await queryInterface.addIndex("mo_support_tickets", ["ticket_type"], {
      name: "idx_tickets_type",
    });

    // Date-based queries
    await queryInterface.addIndex("mo_support_tickets", ["created_at"], {
      name: "idx_tickets_created_at",
    });

    await queryInterface.addIndex("mo_support_tickets", ["resolved_at"], {
      name: "idx_tickets_resolved_at",
    });

    await queryInterface.addIndex("mo_support_tickets", ["sla_due_date"], {
      name: "idx_tickets_sla_due_date",
    });

    // Composite indexes for common query patterns
    await queryInterface.addIndex("mo_support_tickets", ["organization_id", "ticket_status"], {
      name: "idx_tickets_org_status",
    });

    await queryInterface.addIndex("mo_support_tickets", ["organization_id", "assigned_to_user_id"], {
      name: "idx_tickets_org_assigned",
    });

    await queryInterface.addIndex("mo_support_tickets", ["organization_id", "created_at"], {
      name: "idx_tickets_org_created",
    });

    // Email-based lookup for notifications
    await queryInterface.addIndex("mo_support_tickets", ["ticket_owner_email"], {
      name: "idx_tickets_owner_email",
    });

    // Rating queries
    await queryInterface.addIndex("mo_support_tickets", ["rating"], {
      name: "idx_tickets_rating",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove all indexes
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_ticket_number");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_organization_id");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_owner_user_id");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_assigned_to_user_id");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_status");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_priority");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_module");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_type");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_created_at");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_resolved_at");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_sla_due_date");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_org_status");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_org_assigned");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_org_created");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_owner_email");
    await queryInterface.removeIndex("mo_support_tickets", "idx_tickets_rating");
  },
};
