import express from "express";
import multer from "multer";
import ingredientsController from "../../controller/ingredients.controller";
import ingredientsValidator from "../../validators/ingredients.validator";

const router = express.Router();

// Configure multer for file upload
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req: any, file: any, cb: any) => {
    // Accept only Excel files
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel files are allowed!"));
    }
  },
});

/**
 * @swagger
 * /private/ingredients/create:
 *   post:
 *     tags:
 *       - Ingredients
 *     summary: Create a new ingredient
 *     description: Create a new ingredient with categories, nutritional attributes, allergens, and dietary information
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ingredient_name
 *               - cost_per_unit
 *               - unit_of_measure
 *             properties:
 *               ingredient_name:
 *                 type: string
 *                 example: "Chicken Breast"
 *                 minLength: 2
 *                 maxLength: 100
 *               ingredient_description:
 *                 type: string
 *                 example: "Fresh organic chicken breast"
 *               ingredient_status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               cost_per_unit:
 *                 type: number
 *                 format: decimal
 *                 example: 12.99
 *               waste_percentage:
 *                 type: number
 *                 format: decimal
 *                 example: 5.5
 *               unit_of_measure:
 *                 type: integer
 *                 example: 1
 *                 description: "ID of the measurement unit"
 *               categories:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *                 description: "Array of category IDs"
 *               nutritions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     unit_of_measure:
 *                       type: integer
 *                       example: 2
 *                     unit:
 *                       type: number
 *                       example: 25.5
 *               allergens:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [4, 5]
 *                 description: "Array of allergen attribute IDs"
 *               dietary:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [6, 7]
 *                 description: "Array of dietary attribute IDs"
 *     responses:
 *       201:
 *         description: Ingredient created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredient created successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Ingredient'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/create",
  ingredientsValidator.createIngredientValidator(),
  ingredientsController.createIngredient,
);

/**
 * @swagger
 * /private/ingredients/get-all:
 *   get:
 *     tags:
 *       - Ingredients
 *     summary: Get all ingredients
 *     description: Retrieve all ingredients with advanced filtering, sorting, pagination, and export functionality
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: organization_id
 *         in: query
 *         description: Organization ID filter
 *         required: false
 *         schema:
 *           type: string
 *       - name: ingredient_status
 *         in: query
 *         description: Filter by ingredient status
 *         required: false
 *         schema:
 *           type: string
 *           enum: [active, inactive]
 *       - name: category
 *         in: query
 *         description: Filter by category IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "1,2,3"
 *       - name: allergy
 *         in: query
 *         description: Filter by allergy attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "4,5"
 *       - name: cuisine
 *         in: query
 *         description: Filter by cuisine attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "6,7"
 *       - name: dietary
 *         in: query
 *         description: Filter by dietary attribute IDs (comma-separated)
 *         required: false
 *         schema:
 *           type: string
 *           example: "8,9"
 *       - name: search
 *         in: query
 *         description: Search in ingredient name and description
 *         required: false
 *         schema:
 *           type: string
 *       - name: sort_by
 *         in: query
 *         description: Sort by field (comma-separated for multiple fields)
 *         required: false
 *         schema:
 *           type: string
 *           enum: [name, cost, category, status, created_at, updated_at]
 *           example: "name,cost"
 *       - name: sort_order
 *         in: query
 *         description: Sort order (comma-separated for multiple fields)
 *         required: false
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           example: "ASC,DESC"
 *       - name: page
 *         in: query
 *         description: Page number for pagination
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - name: limit
 *         in: query
 *         description: Number of items per page
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *       - name: download
 *         in: query
 *         description: Export format (excel or csv)
 *         required: false
 *         schema:
 *           type: string
 *           enum: [excel, csv]
 *     responses:
 *       200:
 *         description: Ingredients retrieved successfully (JSON response when download not specified)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredients retrieved successfully"
 *                 count:
 *                   type: integer
 *                   example: 25
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Ingredient'
 *                 page:
 *                   type: integer
 *                   example: 1
 *                 size:
 *                   type: integer
 *                   example: 10
 *                 total_pages:
 *                   type: integer
 *                   example: 3
 *         headers:
 *           Content-Type:
 *             description: File download when download parameter is specified
 *             schema:
 *               type: string
 *               enum: [application/json, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, text/csv]
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/get-all", ingredientsValidator.getIngredientsListValidator(), ingredientsController.getIngredients);

/**
 * @swagger
 * /private/ingredients/get-by-id/{id}:
 *   get:
 *     tags:
 *       - Ingredients
 *     summary: Get ingredient by ID or slug
 *     description: Retrieve a single ingredient by its ID or slug with all relations (categories, attributes, etc.)
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Ingredient ID (numeric) or slug (string)
 *         required: true
 *         schema:
 *           oneOf:
 *             - type: integer
 *               example: 1
 *             - type: string
 *               example: "chicken-breast"
 *     responses:
 *       200:
 *         description: Ingredient retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredient retrieved successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Ingredient'
 *       404:
 *         description: Ingredient not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/get-by-id/:id", ingredientsController.getIngredientById);

/**
 * @swagger
 * /private/ingredients/update/{id}:
 *   put:
 *     tags:
 *       - Ingredients
 *     summary: Update ingredient
 *     description: Update an existing ingredient with its categories, nutritional attributes, allergens, and dietary information
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Ingredient ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ingredient_name:
 *                 type: string
 *                 example: "Updated Chicken Breast"
 *                 minLength: 2
 *                 maxLength: 100
 *               ingredient_description:
 *                 type: string
 *                 example: "Updated fresh organic chicken breast"
 *               ingredient_status:
 *                 type: string
 *                 enum: [active, inactive]
 *                 example: "active"
 *               cost_per_unit:
 *                 type: number
 *                 format: decimal
 *                 example: 13.99
 *               waste_percentage:
 *                 type: number
 *                 format: decimal
 *                 example: 6.0
 *               unit_of_measure:
 *                 type: integer
 *                 example: 1
 *                 description: "ID of the measurement unit"
 *               categories:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 4]
 *                 description: "Array of category IDs"
 *               nutritions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     unit_of_measure:
 *                       type: integer
 *                       example: 2
 *                     unit:
 *                       type: number
 *                       example: 26.0
 *               allergens:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [4, 5, 6]
 *                 description: "Array of allergen attribute IDs"
 *               dietary:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [7, 8]
 *                 description: "Array of dietary attribute IDs"
 *     responses:
 *       200:
 *         description: Ingredient updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredient updated successfully"
 *                 data:
 *                   $ref: '#/components/schemas/Ingredient'
 *       404:
 *         description: Ingredient not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       400:
 *         description: Bad request - validation error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/update/:id",
  ingredientsValidator.updateIngredientValidator(),
  ingredientsController.updateIngredient,
);

/**
 * @swagger
 * /private/ingredients/delete/{id}:
 *   delete:
 *     tags:
 *       - Ingredients
 *     summary: Delete ingredient
 *     description: Soft delete an ingredient and all its relations (categories, attributes, etc.)
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Ingredient ID
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *     responses:
 *       200:
 *         description: Ingredient deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredient deleted successfully"
 *       404:
 *         description: Ingredient not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete("/delete/:id", ingredientsController.deleteIngredient);

/**
 * @swagger
 * /private/ingredients/import-template:
 *   get:
 *     tags:
 *       - Ingredients
 *     summary: Download import template
 *     description: Download Excel template file for ingredient bulk import
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Template file downloaded successfully
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename
 *             schema:
 *               type: string
 *               example: "attachment; filename=ingredients_import_template.xlsx"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/import-template", ingredientsController.downloadImportTemplate);

/**
 * @swagger
 * /private/ingredients/import:
 *   post:
 *     tags:
 *       - Ingredients
 *     summary: Import ingredients from Excel
 *     description: Bulk import ingredients from Excel file with categories, nutritional attributes, allergens, and dietary information
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: "Excel file (.xlsx) containing ingredient data"
 *     responses:
 *       200:
 *         description: Ingredients imported successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Ingredients imported successfully. 8 successful, 2 failed."
 *                 results:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 10
 *                     success:
 *                       type: integer
 *                       example: 8
 *                     failed:
 *                       type: integer
 *                       example: 2
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           row:
 *                             type: integer
 *                             example: 3
 *                           error:
 *                             type: string
 *                             example: "Row 3: Unit of measure 'Liters' not found"
 *                 summary:
 *                   type: object
 *                   properties:
 *                     total_processed:
 *                       type: integer
 *                       example: 10
 *                     successful_imports:
 *                       type: integer
 *                       example: 8
 *                     failed_imports:
 *                       type: integer
 *                       example: 2
 *                     success_rate:
 *                       type: integer
 *                       example: 80
 *       400:
 *         description: Bad request - invalid file or missing required headers
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/import",
  upload.single("file"),
  ingredientsController.importIngredients,
);

/**
 * @route GET /api/v1/ingredients/export/excel
 * @description Export ingredients to Excel using getIngredients with download=excel
 * @access Private
 */
router.get("/export/excel", (req, res) => {
  req.query.download = "excel";
  return ingredientsController.getIngredients(req, res);
});

/**
 * @route GET /api/v1/ingredients/export/csv
 * @description Export ingredients to CSV using getIngredients with download=csv
 * @access Private
 */
router.get("/export/csv", (req, res) => {
  req.query.download = "csv";
  return ingredientsController.getIngredients(req, res);
});

export default router;
