import { Segments, Joi, celebrate } from "celebrate";
export default {
    login: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                username: Joi.string().required(),
                password: Joi.string().required()
            }),
        }),
    orgUserRegister: () =>
        celebrate({
            [Segments.BODY]: Joi.object({
                user: Joi.object({
                    firstName: Joi.string().required(),
                    lastName: Joi.string().required(),
                    userPhoneNumber: Joi.string()
                        .pattern(/^\d+$/)
                        .min(10)
                        .max(15)
                        .required(),
                    userCountryCode: Joi.number().positive().required(),
                    username: Joi.string().required(),
                    password: Joi.string().required(),
                    email: Joi.string().email().required()
                }).required(),

                organization: Joi.object({
                    name: Joi.string().min(1).required(),
                    website: Joi.string().uri().required(),
                    email: Joi.string().email(),
                    multiple_location: Joi.number(),
                    redirectUrl: Joi.string().uri().allow(null),
                    description: Joi.string()
                }).required()
            })
        }),
    verifyOTP: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                userId: Joi.string().required(),
                otp: Joi.string().required()
            }),
        }),
    verifyAuthToken: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                userId: Joi.string().required()
            }),
        }),
    resendOTP: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                userId: Joi.string().required(),
            }),
        }),
    forgotPassword: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                email: Joi.string().email().required()
            }),
        }),

    resetUserPassword: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                userId: Joi.string().required(),
                password: Joi.string().required(),
            }),
        }),
    changePassword: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                password: Joi.string().required(),
            }),
        }),
    updateUser: Joi.object().keys({
        userId: Joi.string().required(),
        firstName: Joi.string().required(),
        lastName: Joi.string().required(),
        email: Joi.string().email().required(),
        userStatus: Joi.string().required().valid('active', 'pending', 'cancelled', 'deleted'),
        userPhoneNumber: Joi.string().required(),
        userCountryCode: Joi.string(),
        organizationId: Joi.string().required(),
        userToken: Joi.string().required().allow(null, ''),
        isLoginpin: Joi.string().required(),
        updatedBy: Joi.string(),
        userSignature: Joi.string(),
        username: Joi.string().required(),
    }),
    createStaffMember: () =>
        celebrate({
            [Segments.BODY]: Joi.object({
                firstName: Joi.string().required(),
                lastName: Joi.string().required(),
                username: Joi.string().required(),
                email: Joi.string().email().required(),
                organization_id: Joi.string().required(),
                role: Joi.array().items(
                    Joi.object({
                        id: Joi.string().required(),
                        name: Joi.string().required(),
                        description: Joi.string().optional(),
                        composite: Joi.boolean().required(),
                        clientRole: Joi.boolean().required(),
                        containerId: Joi.string().required()
                    })
                ).required()
            })
        }),
    refreshToken: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                refreshToken: Joi.string().required(),
            }),
        }),
    resendEmail: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                userId: Joi.string().required(),
                email: Joi.string().email().required(),
                isUpdateEmail: Joi.boolean(),
            }),
        }),
};
