worker_processes auto;

events {
    worker_connections  1024;
}

http {
    # HTTP server block
    server {
        listen 81;
        server_name localhost;  # Replace with your domain if needed

        # Route for the client (frontend) - need to confirm frontend
        # location /api {
        #     proxy_pass http://client:3000;  # Proxy traffic to the client service
        #     proxy_http_version 1.1;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        # }

        # Route for the API endpoint (server)
        location /auth-api {
            proxy_pass http://auth-ms:9023;  # Proxy traffic to the server service
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        # location /subscription-api {
        #     proxy_pass http://subscription-ms:9024;  # Proxy traffic to the server service
        #     proxy_http_version 1.1;
        #     proxy_set_header Host $host;
        #     proxy_set_header X-Real-IP $remote_addr;
        #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        #     proxy_set_header X-Forwarded-Proto $scheme;
        # }
    }

    # HTTPS server block
    server {
            listen 443 ssl;                # Ensure Nginx listens for HTTPS on port 443
            server_name localhost;         # Set the server_name to localhost or your domain

            ssl_certificate      /etc/nginx/nginx-selfsigned.crt;   # Path inside the container
            ssl_certificate_key  /etc/nginx/nginx-selfsigned.key;

            # Additional SSL options (you may already have these configured)
            # location / {
            #     proxy_pass http://client:3000;                      # Adjust proxy to client service
            #     proxy_set_header Host $host;
            #     proxy_set_header X-Real-IP $remote_addr;
            #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            #     proxy_set_header X-Forwarded-Proto $scheme;
            # }

            # Route for the API endpoint (server)
            location /auth-api {
                proxy_pass http://auth-ms:9023;  # Proxy traffic to the server service
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # location /subscription-api {
            #     proxy_pass http://subscription-ms:9024;  # Proxy traffic to the server service
            #     proxy_http_version 1.1;
            #     proxy_set_header Host $host;
            #     proxy_set_header X-Real-IP $remote_addr;
            #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            #     proxy_set_header X-Forwarded-Proto $scheme;
            # }
     }
}
