﻿Recipe Management & Operations Module
This is a significantly more complex and feature-rich recipe module than the MVP we discussed earlier. This aligns more with a comprehensive system like Kafoodle.
This document will be detailed and suitable for a project kickoff.
________________


Module Specification Document: Recipe Management & Operations
Document Version: 1.1
Date: May 28, 2025
Project: MicrOffice
Module Lead: Vipul Sir, Deepal


Introduction & Purpose
Overview
This document outlines the specifications for the "Recipe Management & Operations" module. This module will serve as the central system for creating, managing, analyzing, and publishing recipes, primarily targeted at administrative users, chefs, and operational staff. It includes robust ingredient management, detailed recipe creation, costing, nutritional analysis, allergen tracking, version control, and public publishing capabilities.
Goals:
1. Provide a comprehensive platform for end-to-end recipe lifecycle management.
2. Ensure accurate costing, nutritional information, and allergen declaration for all recipes.
3. Streamline recipe development, scaling, and versioning.
4. Offer insights into recipe performance and ingredient usage.
5. Facilitate optional public sharing of selected recipes with integrated calls to action.
6. Support operational efficiency for food businesses.
Target Users (Internal): Administrators, Chefs, Recipe Developers, Menu Planners, Cost Controllers, Nutritionists.
Target Users (External - for Public Recipes): General public visiting the organization's public-facing website/platform.
Design Reference: https://www.figma.com/design/IlIhykQCdhDPjFkCRpDyTF/Recipies?node-id=0-1&p=f&t=Rk8U9Zg39mzlfhoK-0
Module List
1. Admin Dashboard (Recipe Module Hub)
2. Allergen Management
3. Ingredient Management
4. Recipe Creation & Editing
5. Public Recipe Publishing & Management
6. Reporting & Exporting
7. Role-Based Access Control (RBAC) within Recipe
8. Search & Filtering (Robust)


Module Features & Functionality
Admin Dashboard (Recipe Module Hub)
1.   Overview Statistics:
   1. Display key metrics at a glance:
   2. Total Recipes (Internal)
   3. Total Ingredients (Master Database)
   4. Total Allergens (Default + Org-Specific Custom)
   5. Total Vendors/Suppliers
   6. Total Recipe Categories
   7. Total Units of Measure
   8. Each stat is clickable, leading to the respective detailed listing/management page.
2. Public Recipe Insights:
   1. Total Published Public Recipes count (clickable to a filtered list of public recipes).
   2.  Graphical display (e.g., bar chart) of "Top 10 Most Liked Public Recipes" (clickable to recipe detail).
   3. Graphical display of "Top 10 Most Viewed/Clicked Public Recipes" (clickable to recipe detail).
3. Quick Actions:
- Direct links to "Create New Recipe," "Manage Ingredients," etc.


Allergen Management
   1.  Allergen Database:
   1. Attributes per Allergen: Name (Standardized), Alternative Names, Type (Default/Custom - Org Specific), Allergen Code (Internal ID), Description/Notes.
   2. Default Allergens: System-provided list (e.g., 14 major), non-deletable by organizations, globally consistent.
   1. Gluten
   2. Crustaceans
   3. Eggs
   4. Fish
   5. Peanuts
   6. Soy
   7. Milk
   8. Tree Nuts
   9. Celery
   10. Mustard
   11. Sesame
   12. Sulphites
   13. Lupin
   14. Molluscs
   3. Custom Allergens: Created by an organization, scoped to that organization. Cannot conflict with default names.
   2. CRUD Operations:
   1. Admins can view all default allergens.
Org-admins can CRUD their own custom allergens. Default allergens are not editable/deletable by org-admins.
      3. Linking:
      1. Allergens are linked to Ingredients.


Ingredient Management
      1.  Ingredient Database: Central repository.
       Attributes per Ingredient:
      1. Name (Required, Unique within organization)
      2. Description, 
      3. Units of Measure (Primary storage unit, e.g., grams, ml)
      1. g 
      2. kg 
      3. ml 
      4. liter 
      5. unit 
      6. OZ 
      7. Ib 
      8. tbsp 
      9. tsp 
      10. cup 
      11. pint 
      12. quart 
      13. gallon
      4. Unit Conversion Capabilities (Define relationships, e.g., 1 cup = 240ml, 1 egg ≈ 50g - system should allow defining these for calculation assistance)
      5. Supplier Information (Link to Vendor module; can store multiple suppliers per ingredient with their specific code, price, pack size)
      6. Cost Per Unit (Calculated based on selected supplier's pack size and price; historical costing considered)
      7. Nutritional Information (Per 100g/ml: Calories, Protein, Carbs, Fat, Sugars, Fiber, Sodium, and other configurable micronutrients)
      8. Allergen Information (Tagging from Allergen Module: Default + Org-Specific Custom)
      9. Dietary Suitability (Tags: Vegan, Vegetarian, Gluten-Free, Halal, Kosher, etc.)
      10. Waste Percentage (e.g., 10% for potato peeling – used in costing)
      11. Category/Tags (For organization and filtering, e.g., "Produce," "Dairy," "Dry Goods")
      1. Dairy 
      2. Meat 
      3. Poultry 
      4. Seafood 
      5. Vegetables 
      6. Fruits 
      7. Grains 
      8. Nuts 
      9. Herbs & Spices 
      10. Oils 
      11. Condiments 
      12. Baking 
      13. Dry Goods 
      14. Beverages
      12. Status (Active, Inactive)

         2. CRUD Operations:
         1. Full Create, Read, Update, Delete (with safeguards for ingredients used in recipes) functionality.
         3. Import/Export:
         1. Bulk import of ingredients from CSV/Excel (mapping supplier data). Export ingredient list.
         4.  Search & Filtering:
         1. Robust search by name, category, supplier, allergen, dietary tag.


Recipe Creation & Editing
         1. Recipe Details:
         1.  Name, Description, Category (e.g., Main Course, Dessert), Cuisine Type (e.g., Italian, Mexican), Custom Tags,Public Names(Optional)
         2. Internal Reference Code/ID.

            2. Yield & Portioning:
            1. Define Total Recipe Yield (e.g., "2 kg" or "5 Liters" or "1 large tray").
            2. Define Number of Portions (e.g., "10 portions").
            3. Define Standard Portion Size (e.g., "200g," "250ml," "1 slice" - system assists with consistency).
            4. Automatic calculation of portion size if total yield and number of portions are given, or vice-versa.

               3. Ingredient List:
               1. Add ingredients from the Ingredient Database (search/browse).
               2. Specify Quantity and Unit for each ingredient as used in the recipe (system will use conversion data for costing/nutrition if recipe unit differs from ingredient's storage unit).
               3. Ability to add "Sub-Recipes": Select an existing internal recipe to be included as an ingredient line item. Its cost, nutrition, and allergens will roll up.
               4. Notes per ingredient line item (e.g., "finely chopped").

                  4. Method/Instructions: 
                  1. Step-by-step instructions.
                  2. Rich Text Formatting (Bold, Italics, Underline, Lists, Links).
                  3. (Advanced V2/Optional for initial build): Ability to add images or short video clips per step.

                     5. Timings:
                     1. Preparation Time (e.g., 30 minutes).
                     2. Cook Time (e.g., 45 minutes).
                     3. Total Time (Auto-calculated).
                     6. Recipe Scaling:
                     1. Function: Allow users to scale the entire recipe up or down based on a new desired total yield OR a new number of portions.
                     2. Logic:
1.Determine the scaling factor: Scaling Factor = New Target / Original Target. (If scaling by yield: New Target Yield / Original Recipe Yield. If scaling by portions: New Number of Portions / Original Number of Portions).
        2. Apply scaling factor: Multiply the quantity of each ingredient by the Scaling Factor.
        3. Adjust units if necessary (e.g., if scaling significantly increases grams, offer to convert to kg).
        4. Method/instructions may need manual review by the user after scaling, as cooking times/techniques might not scale linearly. Display a note to this effect.
                     3. UI: Input field for "New Yield" or "New Portions." Display scaled ingredient list. Option to save as a new version or a new recipe.

                        7. Version Control:
                        1. Automatic Versioning: Each significant save of a recipe creates a new version (timestamped, with user who made change).
                        2. View Version History: List previous versions with key changes (if change highlighting is enabled).
                        3. Revert to Previous Version: Option to make an older version the current active version.
                        4. Highlight Changes (Configurable):
                        5. Admin configuration option: "Enable Change Highlighting for Internal Recipe Versions."
                        6. If YES: When viewing a recipe's version history or comparing to a previous version, the system will attempt to visually highlight what changed (e.g., ingredient quantity, added/removed ingredient, modified step). This is for internal team review and not for public pages.
                        8. Duplication ("Create New from Version" / "Duplicate"):
                        1. Easily duplicate an existing recipe (any version) to create a new, independent recipe as a starting point for variations.
                        9. Assign Recipe Ownership/Access (Internal):
                        1. Assign recipe to a specific user (creator/owner).
                        2. Ability to restrict edit/view access based on user roles (e.g., Chef, Manager) or organizational branches/departments.

                           10.  Role Based Insturctions :
                           1. Suppose some specific instruction for Serving team
                           2. Some specific instruction for Head Shef OR Bar Tender
                           11.  Status: 
                           1. Draft, Save, Publish, Active/InActive, 
                           2. Published /UnPublished.
                           12. Costing & Pricing :
                           1. Automatic Recipe Costing:
                           1. Calculates total recipe cost based on: Quantities of ingredients used, Cost per unit of those ingredients (considering waste percentage), Cost of sub-recipes.
                           2. Displays Total Recipe Cost and Cost Per Portion.
                           2. Gross Profit (GP) Calculation:
                           1. Allow user to input a Selling Price per portion/recipe.
                           2. Calculate and display Gross Profit Amount, GP Percentage, and Food Cost Percentage.

                              3. "What If" Scenarios (Cost Sensitivity):
                              1. Interface to temporarily adjust individual ingredient costs or the overall selling price to see the immediate impact on total cost and GP. (Does not save these changes to the master ingredient/recipe).

                                 13. Nutritional Analysis:
                                 1. Automatic Calculation:
                                 1. Aggregates nutritional data (calories, macros, specified micros) from all ingredients (and sub-recipes) based on their quantities in the recipe.
                                 2. Displays per-recipe totals and per-portion averages.
                                 2. Traffic Light Labelling (Optional, region-dependent):
                                 1. If configured, display color-coded labels (Green, Amber, Red) for Fat, Saturated Fat, Sugar, Salt per portion, based on defined thresholds (e.g., UK FSA guidelines).
                                 3. Reference Intakes (RI % / %DV):
                                 1.  Display the percentage contribution of key nutrients per portion against daily recommended intakes.
                                 14.  Allergen Management (in Recipe Context)
                                 1. Automatic Allergen Highlighting:
                                 1. Clearly lists all allergens present in a recipe, aggregated from its ingredients (and sub-recipes) using the tags from the Allergen Module.
                                 2. "May Contain" Handling:
                                 1. Field to manually add "May Contain" statements for potential cross-contamination risks not inherent to the ingredients themselves (e.g., "Prepared in a kitchen that handles nuts").

                                    3. Allergen Matrix Generation (for Menus):
                                    1. If recipes are grouped into menus (see Menu Engineering & Planning), generate a matrix view showing recipes vs. allergens present.

Public Recipe Publishing & Management
                                       1. Mark Recipe as "Public":
                                       1. Admin/authorized user can toggle a recipe (specific active version) to be "Public."
                                       2. Public Recipe Detail Page:
                                       1. When a recipe is public, it's accessible via a unique, shareable URL on the organization's public-facing site.
                                       2. Public page displays: Recipe Name, Description, Image (if available), Ingredients, Method, Publicly relevant nutritional summary (optional), Declared Allergens (no "May Contain" unless explicitly chosen for public display).
                                       3. No internal costing, version history details, or internal notes are shown on the public page.

                                          3. Call to Action (CTA) Configuration General Common for All Public Recipe:
                                          1. Admin can choose a CTA to display on the public recipe detail page:
                                          1. Option 1: Display a pre-defined "Contact Us" information block (phone, email).
                                          2. Option 2: Display an embedded "Contact Us" form.
                                          3. Option 3: CTA.

                                             4. Remove from Public:
                                             1. Admin can unpublish a recipe.
                                             2. Once unpublished, the previous public URL should no longer be accessible (e.g., return a 404 or a "This recipe is no longer available" page).
                                             5. Analytics for Public Recipes (Basic): Track views/clicks on public recipe pages

Reporting & Exporting
                                                1. Print-Friendly Recipe Cards:
                                                1. Standardized format including Name, Image, Yield, Portions, Ingredients (with quantities & units), Method, Key Allergens, Basic Nutrition Summary.
                                                2. Export Recipes: Option to export selected recipes or all recipes to PDF formats.[a]
                                                3. Costing Reports: Summary and detailed costing reports.
                                                4. Nutritional Reports: Detailed nutritional breakdowns.
                                                5. Allergen Reports: Lists of recipes containing specific allergens.



Role-Based Access Control (RBAC) within Recipe Module
                                                   1. Role-Based Access Control
                                                   1. Define roles (e.g., Admin, Chef, RecipeDeveloper, ReadOnlyViewer).
                                                   2. Permissions associated with roles (e.g., only Admin/Chef can publish recipes, RecipeDeveloper can create/edit but not publish).

                                                      2. Audit Trails: Log significant changes to recipes and ingredients (who, what, when).
                                                      3. Collaboration (Basic): Comments/notes section within a recipe for internal team communication (not for public view).
Search & Filtering (Robust)
                                                      1.  Search recipes and ingredients by: Name, Tags, Categories, Cuisine Type.
                                                      2. Filter recipes/ingredients by: Allergen (contains/does not contain), Dietary Suitability, Nutrient Content (e.g., low carb, high protein), Cost Range.
Non-Functional Requirements
                                                      1. Performance: System should be responsive, especially during recipe loading, searching, and calculations.
                                                      2. Scalability: Architecture should support a growing number of recipes, ingredients, and users
                                                      3. Usability: Intuitive and efficient interface for all user roles. Minimal training required for core tasks
                                                      4. Accuracy: Calculations (cost, nutrition, allergens) must be accurate based on the input data.
                                                      5. Security: Protect sensitive business data (costs, recipes) and ensure proper access controls.
3.6. 
                                                      6. Reliability: High availability of the module.
Assumptions
                                                         1.  Existing robust user authentication and authorization system is in place and can be integrated with for RBAC
                                                         2.  Master data for units of measure and initial default allergens will be pre-populated.
                                                         3. Users are responsible for the accuracy of the data they input (ingredient costs, nutritional info, allergen tagging). Exclusions (for this specific module, assuming other modules might cover them)


Exclusions (for this specific module, assuming other modules might cover them)
                                                         1. Detailed inventory stock level tracking (though ingredient costs are used).
                                                         2. Full procurement and purchase order management.
                                                         3. Customer-facing ordering systems (unless the "Public Recipe Page" is considered a part of it).






Next Phase (Not part of current Scope)
Menu Engineering & Planning  (Integrated Module - Future Phase)
                                                         1.  Create & Manage Menus: Group recipes into named menus (e.g., "Summer Lunch Menu," "Weekly Cycle Menu 1").
                                                         2. Menu Costing & Nutrition: Aggregate costs and nutritional information for entire menus or average per day for cycle menus.
                                                         3. Cycle Menus: Support for creating rotating weekly/monthly menus.


[a]Need to finalaize the export format and look @<EMAIL>
_<NAME_EMAIL>_