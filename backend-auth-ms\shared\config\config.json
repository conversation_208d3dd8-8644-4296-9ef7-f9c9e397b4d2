{"development": {"use_env_variable": false, "PORT": 8010, "OTP_EXPIRE_TIME": "300", "API_BASE_URL": "https://immune-needlessly-porpoise.ngrok-free.app/uploads", "WEB_BASE_URL": "http://localhost:3000", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://localhost:8080/auth/", "KEYCLOAK_REALM_NAME": "orga", "KEYCLOAK_SECRET_KEY": "SelMClSaZrpQ87jyvIF3JqZ1nyZYqliy", "KEYCLOAK_MY_SECRET_KEY": "keycklock-secret-key", "KEYCLOAK_BASE_URL": "http://localhost:8080/auth/admin/realms/", "KEYCLOAK_TOKEN_BASE_URL": "http://localhost:8080/auth/realms/", "KEYCLOAK_MASTER_ROLE": "org_master", "KEYCLOAK_MASTER_ROLE_DESCRIPTION": "role_org_master", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "KEYCLOAK_REALM_ROLE": "realm-admin", "RABBITMQ_URL": "amqp://admin:jnext@123@localhost:5673", "FORGOT_PASSWORD_ATTEMPT": 3, "ORGANIZATION_ID": "70e63024-d8b4-4b03-b5a6-7894834c17fc", "KEYCLOAK_STAFF_ROLE": "staff", "KEYCLOAK_STAFF_ROLE_DESCRIPTION": "role_staff", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "1d", "MINIO_ENDPOINT": "http://mail.theeasyaccess.com:9000", "MINIO_ACCESS_KEY": "ftJDcIQg9bt6pHMu5nCe", "MINIO_SECRET_KEY": "YvhKjLz8MhFrFjy4fXfYdQsyvKQ6CqSHiL8jBBAo"}, "staging": {"use_env_variable": false, "PORT": 8023, "OTP_EXPIRE_TIME": "300", "API_BASE_URL": "https://staging.namastevillage.theeasyaccess.com/backend-api/v1/public/user/get-file?location=", "WEB_BASE_URL": "https://staging.namastevillage.theeasyaccess.com", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://keycloak:8080/auth/", "KEYCLOAK_REALM_NAME": "staging_orga", "KEYCLOAK_SECRET_KEY": "IVmrr3Jq3dMGFGsi4YBImbA25KGkr263", "KEYCLOAK_MY_SECRET_KEY": "keycklock-secret-key", "KEYCLOAK_BASE_URL": "http://keycloak:8080/auth/admin/realms/", "KEYCLOAK_TOKEN_BASE_URL": "http://keycloak:8080/auth/realms/", "KEYCLOAK_MASTER_ROLE": "org_master", "KEYCLOAK_MASTER_ROLE_DESCRIPTION": "role_org_master", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "KEYCLOAK_REALM_ROLE": "realm-admin", "RABBITMQ_URL": "amqp://admin:jnext@123@rabbitmq:5672", "FORGOT_PASSWORD_ATTEMPT": 3, "ORGANIZATION_ID": "c2cdf954-4234-4de6-af73-ff619e744cf3", "KEYCLOAK_STAFF_ROLE": "staff", "KEYCLOAK_STAFF_ROLE_DESCRIPTION": "role_staff", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "1d", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}, "production": {"use_env_variable": false, "PORT": 8023, "OTP_EXPIRE_TIME": "300", "API_BASE_URL": "https://portal.microffice.co.uk/backend-api/v1/public/user/get-file?location=", "WEB_BASE_URL": "https://portal.microffice.co.uk", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://keycloak-service:8080/auth/", "KEYCLOAK_REALM_NAME": "micrOffice-prod", "KEYCLOAK_SECRET_KEY": "Ue6HAWXAg5jHP8MmMBHzpZz2muuvKD2B", "KEYCLOAK_MY_SECRET_KEY": "keycklock-prod-secret-key", "KEYCLOAK_BASE_URL": "http://keycloak-service:8080/auth/admin/realms/", "KEYCLOAK_TOKEN_BASE_URL": "http://keycloak-service:8080/auth/realms/", "KEYCLOAK_MASTER_ROLE": "org_master", "KEYCLOAK_MASTER_ROLE_DESCRIPTION": "role_org_master", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "KEYCLOAK_REALM_ROLE": "realm-admin", "RABBITMQ_URL": "amqp://JNext:JnextMO2025@rabbitmq-service:5673", "FORGOT_PASSWORD_ATTEMPT": 3, "ORGANIZATION_ID": "c2cdf954-4234-4de6-af73-ff619e744cf3", "KEYCLOAK_STAFF_ROLE": "staff", "KEYCLOAK_STAFF_ROLE_DESCRIPTION": "role_staff", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "1d", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}}