import express, { Router } from "express";
import supportConfigController from "../../controller/supportConfig.controller";
import { celebrate, Joi, Segments } from "celebrate";

const routes: Router = express.Router();

// Health check endpoint for public routes
routes.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    service: "Customer Service Microservice - Public Routes",
    message:
      "Only PIN validation is available publicly. All other endpoints require authentication.",
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    features: {
      public_ticket_creation: false,
      authentication_required: true,
      organization_scoped: true,
      support_pin_validation: true,
    },
  });
});

// PIN validation route (public access)
// POST /v1/public/validate-pin - Validate support PIN for organization
const validatePinValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        organization_id: Joi.string().required(),
        support_pin: Joi.string().min(4).max(20).required(),
      })
      .unknown(false),
  });

routes.post("/validate-pin", validatePinValidation(), async (req, res) => {
  try {
    // Extract organization_id from body and add to params for controller compatibility
    const { organization_id } = req.body;
    (req as any).params = { organization_id };

    // Call the existing controller function
    await supportConfigController.validatePin(req, res);
  } catch (error) {
    console.error("Error in public validate-pin route:", error);
    return res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

export default routes;
