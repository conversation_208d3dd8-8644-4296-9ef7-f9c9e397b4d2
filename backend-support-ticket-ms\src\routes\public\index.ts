import express, { Router } from "express";
import supportConfigController from "../../controller/supportConfig.controller";

const routes: Router = express.Router();

// Health check endpoint for public routes
routes.get("/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    service: "Customer Service Microservice - Public Routes",
    message:
      "Only PIN validation is available publicly. All other endpoints require authentication.",
    version: "1.0.0",
    timestamp: new Date().toISOString(),
    features: {
      public_ticket_creation: false,
      authentication_required: true,
      organization_scoped: true,
      support_pin_validation: true,
    },
  });
});

// For now, no public routes are needed - all support operations require authentication
// Following recipe-ms pattern where public routes are minimal

export default routes;
