-- Migration: Create Recipe Settings Table
-- This migration creates the mo_recipe_settings table for storing recipe configuration settings

CREATE TABLE IF NOT EXISTS `mo_recipe_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','number','boolean','json','array') NOT NULL DEFAULT 'string',
  `setting_category` enum('recipe','dashboard','public','analytics','system') NOT NULL,
  `setting_description` text DEFAULT NULL,
  `is_system_setting` tinyint(1) NOT NULL DEFAULT 0,
  `organization_id` varchar(100) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) NOT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON> KEY (`id`),
  UNIQUE KEY `unique_org_setting` (`setting_key`, `organization_id`),
  KEY `idx_setting_category` (`setting_category`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default recipe settings
INSERT IGNORE INTO `mo_recipe_settings` 
(`setting_key`, `setting_value`, `setting_type`, `setting_category`, `setting_description`, `is_system_setting`, `organization_id`, `created_by`, `updated_by`) 
VALUES
-- Private Recipe Visibility Settings
('recipe.highlight_changes', 'false', 'boolean', 'recipe', 'Enable this to show highlighted changes to the assigned team member when viewing the recipe', 0, NULL, 1, 1),

-- Public Recipe Settings
('recipe.public_store_enabled', 'true', 'boolean', 'recipe', 'Enable to make public recipe features available. Turning this off hides all public recipe options', 0, NULL, 1, 1),

-- Public Recipe Call-To-Action Settings
('recipe.cta_contact_form', 'true', 'boolean', 'public', 'Show a basic contact form (Name, Email, Phone, Message)', 0, NULL, 1, 1),
('recipe.cta_contact_info', 'false', 'boolean', 'public', 'Display a predefined contact block (Phone, Email, Link)', 0, NULL, 1, 1),
('recipe.cta_custom_link', 'false', 'boolean', 'public', 'Show a custom CTA with text and an external link', 0, NULL, 1, 1),
('recipe.cta_none', 'false', 'boolean', 'public', 'Show nothing', 0, NULL, 1, 1),

-- Contact Info Fields
('recipe.contact_info_name', '', 'string', 'public', 'Contact name', 0, NULL, 1, 1),
('recipe.contact_info_phone', '', 'string', 'public', 'Phone number', 0, NULL, 1, 1),
('recipe.contact_info_email', '', 'string', 'public', 'Email address', 0, NULL, 1, 1),
('recipe.contact_info_link', '', 'string', 'public', 'Link URL', 0, NULL, 1, 1),

-- Custom CTA Fields
('recipe.custom_cta_text', '', 'string', 'public', 'Custom CTA button text', 0, NULL, 1, 1),
('recipe.custom_cta_link', '', 'string', 'public', 'Custom CTA link URL', 0, NULL, 1, 1),

-- Recipe Details to Display Publicly (exactly as shown in UI)
('recipe.display_category', 'true', 'boolean', 'public', 'Category', 0, NULL, 1, 1),
('recipe.display_ingredients', 'true', 'boolean', 'public', 'Ingredients', 0, NULL, 1, 1),
('recipe.display_nutritional_information', 'true', 'boolean', 'public', 'Nutritional Information', 0, NULL, 1, 1),
('recipe.display_allergen_information', 'true', 'boolean', 'public', 'Allergen Information', 0, NULL, 1, 1),
('recipe.display_preparation_steps', 'true', 'boolean', 'public', 'Preparation Steps', 0, NULL, 1, 1),
('recipe.display_total_time', 'false', 'boolean', 'public', 'Total Time', 0, NULL, 1, 1),
('recipe.display_yield_portioning', 'false', 'boolean', 'public', 'Yield & Portioning', 0, NULL, 1, 1),
('recipe.display_cost', 'false', 'boolean', 'public', 'Cost', 0, NULL, 1, 1),
('recipe.display_dietary_suitability', 'false', 'boolean', 'public', 'Dietary Suitability', 0, NULL, 1, 1),
('recipe.display_cuisine_type', 'false', 'boolean', 'public', 'Cuisine Type', 0, NULL, 1, 1),
('recipe.display_media', 'false', 'boolean', 'public', 'Media', 0, NULL, 1, 1),
('recipe.display_links', 'false', 'boolean', 'public', 'Links', 0, NULL, 1, 1),
('recipe.display_scale', 'false', 'boolean', 'public', 'Scale', 0, NULL, 1, 1),
('recipe.display_serve_in', 'false', 'boolean', 'public', 'Serve In', 0, NULL, 1, 1),
('recipe.display_garnish', 'false', 'boolean', 'public', 'Garnish', 0, NULL, 1, 1);
