import { StatusCodes } from "http-status-codes";
import { RequestDemo } from "../models/RequestDemo";
const ExcelJS = require('exceljs');

/** request demo API */
const requestDemoRequest = async (req: any, res: any) => {
    try {
        let { first_name, last_name, email, mobile_number, organization_name, number_of_employee, industry, description, country_code } = req.body;

        /** check email already exist */
        let emailExist: any = await RequestDemo.findOne({ where: { email }, attributes: ['id'], raw: true });
        if (emailExist) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("ERROR_EMAIL_ALREADY_EXIST"),
                data: null,
            });
        }

        /** store request demo  */
        let createObject: any = {
            first_name,
            last_name,
            email,
            mobile_number,
            organization_name,
            number_of_employee,
            industry,
            description,
            country_code
        }

        let createData: any = await RequestDemo.create(createObject)
        return res.status(StatusCodes.CREATED).json({
            status: true,
            message: res.__("SUCCESS_DEMO_REQUEST_SUBMITTED"),
            data: createData,
        });
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Get All Submitted request */
const getAllDemoRequest = async (req: any, res: any) => {
    try {
        let allData: any = await RequestDemo.findAll({ raw: true });
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_DEMO_REQUESTS_FETCHED"),
            data: allData,
        });
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}

/** Download excel file */
const downloadExcel = async (req: any, res: any) => {
    try {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Sheet 1');

        let requestData: any = await RequestDemo.findAll({ raw: true });
        // Get dynamic headers from first response object
        if (requestData.length > 0) {
            // Convert Sequelize instances to plain objects
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('Request Demos');

            // Get dynamic headers from the first record
            const headers = Object.keys(requestData[0]);

            // Set Headers Dynamically
            worksheet.columns = headers.map(key => ({
                header: key.replace(/_/g, ' ').toUpperCase(), // Convert snake_case to readable format
                key: key,
                width: 20
            }));

            // Add Data Rows
            requestData.forEach((row: any) => {
                worksheet.addRow(row);
            });


            // Send the workbook as a buffer
            const buffer = await workbook.xlsx.writeBuffer();
            await workbook.xlsx.writeBuffer();
            res.setHeader('Content-Disposition', 'attachment; filename=report.xlsx');
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.send(buffer);
        }
    } catch (e) {
        console.log("Exception: ", e);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: e,
        });
    }
}
export default {
    requestDemoRequest,
    getAllDemoRequest,
    downloadExcel
}