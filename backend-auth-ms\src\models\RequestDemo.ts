"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface requestDemoAttributes {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    mobile_number: string;
    organization_name: string;
    number_of_employee: number;
    industry: string;
    description: string;
    country_code: string;
    created_by: number;
    updated_by: number;
}

export class RequestDemo
    extends Model<requestDemoAttributes, never>
    implements requestDemoAttributes {
    id!: number;
    first_name!: string;
    last_name!: string;
    email!: string;
    mobile_number!: string;
    organization_name!: string;
    number_of_employee!: number;
    industry!: string;
    description!: string;
    country_code!: string;
    created_by!: number;
    updated_by!: number;
}

RequestDemo.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        first_name: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        last_name: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        email: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        mobile_number: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        organization_name: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        number_of_employee: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        industry: {
            type: DataTypes.STRING,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT('long'),
            allowNull: true,
        },
        country_code: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "request_demo",
        modelName: "RequestDemo",
        timestamps: true,
    },
);
