import { celebrate, Joi, Segments } from "celebrate";

// Following recipe-ms pattern with function wrapper
const getConfigValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      configKey: Joi.string().min(1).max(100).required(),
    }),
  });

const upsertConfigValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        config_key: Joi.string().min(1).max(100).required(),
        config_value: Joi.alternatives().try(
          Joi.string().max(1000),
          Joi.number(),
          Joi.boolean(),
          Joi.object()
        ).required(),
        config_description: Joi.string().max(500).optional(),
        is_active: Joi.boolean().default(true),
        config_type: Joi.string()
          .valid("string", "number", "boolean", "json", "array")
          .default("string"),
      })
      .unknown(true),
  });

const getAllConfigsValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        page: Joi.number().integer().min(1).optional(),
        limit: Joi.number().integer().min(1).max(100).optional(),
        is_active: Joi.boolean().optional(),
        config_type: Joi.string()
          .valid("string", "number", "boolean", "json", "array")
          .optional(),
        search: Joi.string().max(100).optional(),
      })
      .unknown(true),
  });

const deleteConfigValidation = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      configKey: Joi.string().min(1).max(100).required(),
    }),
  });

const toggleSupportValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        is_support_enabled: Joi.boolean().required(),
        maintenance_message: Joi.string().max(500).optional(),
      })
      .unknown(true),
  });

const defaultConfigValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        reset_to_defaults: Joi.boolean().default(true),
        config_keys: Joi.array().items(Joi.string()).optional(),
      })
      .unknown(true),
  });

export {
  getConfigValidation,
  upsertConfigValidation,
  getAllConfigsValidation,
  deleteConfigValidation,
  toggleSupportValidation,
  defaultConfigValidation,
};

export default {
  getConfigValidation,
  upsertConfigValidation,
  getAllConfigsValidation,
  deleteConfigValidation,
  toggleSupportValidation,
  defaultConfigValidation,
};
