"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("mo_support_configs", {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      organization_id: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: "Organization identifier for multi-tenant support",
      },
      support_pin: {
        type: Sequelize.STRING(6),
        allowNull: true,
        comment: "PIN for ticket creation verification (optional)",
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether support is active for this organization",
      },
      allow_attachments: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: "Whether file attachments are allowed",
      },
      max_attachment_size: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 10485760, // 10MB in bytes
        comment: "Maximum attachment size in bytes",
      },
      allowed_file_types: {
        type: Sequelize.JSON,
        allowNull: false,
        defaultValue: '["pdf", "png", "jpg", "jpeg", "doc", "docx"]',
        comment: "Accepted file types as JSON array",
      },
      auto_assignment_enabled: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: "Toggle for automatic ticket assignment",
      },
      sla_response_time_hours: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 24,
        comment: "SLA response time in hours (optional)",
      },
      sla_resolution_time_hours: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 72,
        comment: "SLA resolution time in hours (optional)",
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user who created the configuration",
      },
      updated_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: "ID of user who last updated the configuration",
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: "Soft delete timestamp",
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex("mo_support_configs", ["organization_id"], {
      name: "idx_support_configs_organization_id",
      unique: true, // One config per organization
    });

    await queryInterface.addIndex("mo_support_configs", ["is_active"], {
      name: "idx_support_configs_is_active",
    });

    await queryInterface.addIndex("mo_support_configs", ["created_by"], {
      name: "idx_support_configs_created_by",
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_organization_id");
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_is_active");
    await queryInterface.removeIndex("mo_support_configs", "idx_support_configs_created_by");

    // Remove table
    await queryInterface.dropTable("mo_support_configs");
  },
};
