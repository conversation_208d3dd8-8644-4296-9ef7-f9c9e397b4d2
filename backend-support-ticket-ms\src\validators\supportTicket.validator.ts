import { celebrate, Joi, Segments } from "celebrate";
import {
  TICKET_MODULE,
  TICKET_TYPE,
  TICKET_PRIORITY,
  TICKET_STATUS,
} from "../helper/constant";

// Following recipe-ms pattern with function wrapper
const createTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string().min(5).max(200).required(),
        ticket_description: Joi.string().min(10).max(2000).required(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .default(TICKET_MODULE.OTHER),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .default(TICKET_TYPE.GENERAL_QUERY),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .default(TICKET_PRIORITY.MEDIUM),
      })
      .unknown(true),
  });

const updateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_title: Joi.string().min(5).max(200).optional(),
        ticket_description: Joi.string().min(10).max(2000).optional(),
        ticket_module: Joi.string()
          .valid(...Object.values(TICKET_MODULE))
          .optional(),
        ticket_type: Joi.string()
          .valid(...Object.values(TICKET_TYPE))
          .optional(),
        ticket_priority: Joi.string()
          .valid(...Object.values(TICKET_PRIORITY))
          .optional(),
        ticket_status: Joi.string()
          .valid(...Object.values(TICKET_STATUS))
          .optional(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        resolution_note: Joi.string().max(1000).optional(),
        rating: Joi.number().integer().min(1).max(5).optional(),
        review_comment: Joi.string().max(500).optional(),
      })
      .unknown(true),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const getTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteTicketValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getTicketsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      organization_id: Joi.string().optional(),
      ticket_status: Joi.string()
        .valid(...Object.values(TICKET_STATUS))
        .optional(),
      ticket_priority: Joi.string()
        .valid(...Object.values(TICKET_PRIORITY))
        .optional(),
      ticket_module: Joi.string()
        .valid(...Object.values(TICKET_MODULE))
        .optional(),
      ticket_type: Joi.string()
        .valid(...Object.values(TICKET_TYPE))
        .optional(),
      assigned_to_user_id: Joi.number().optional(),
      search: Joi.string().optional(),
      sort_by: Joi.string()
        .valid("created_at", "updated_at", "ticket_priority", "ticket_status")
        .optional(),
      sort_order: Joi.string().valid("ASC", "DESC").optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
    }),
  });

const assignTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      assigned_to_user_id: Joi.number().required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const resolveTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      resolution_note: Joi.string().min(10).max(1000).required(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

const rateTicketValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      rating: Joi.number().integer().min(1).max(5).required(),
      review_comment: Joi.string().max(500).optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().integer().positive().required(),
    }),
  });

export default {
  createTicketValidator,
  updateTicketValidator,
  getTicketValidator,
  deleteTicketValidator,
  getTicketsListValidator,
  assignTicketValidator,
  resolveTicketValidator,
  rateTicketValidator,
};
