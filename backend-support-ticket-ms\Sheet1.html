<meta http-equiv="Content-Type" content="text/html; charset=utf-8"><link type="text/css" rel="stylesheet" href="resources/sheet.css" >
<style type="text/css">.ritz .waffle a { color: inherit; }.ritz .waffle .s17{border-bottom:1px SOLID #ffffff;background-color:#f6f8f9;}.ritz .waffle .s11{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#434343;font-family:<PERSON>erdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s13{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #284e3f;background-color:#f6f8f9;text-align:left;color:#434343;font-family:docs-<PERSON><PERSON>,<PERSON><PERSON>;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s8{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #284e3f;background-color:#ffffff;text-align:left;color:#434343;font-family:docs-Roboto,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s9{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:right;color:#434343;font-family:docs-Roboto,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s14{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #ffffff;background-color:#ffffff;text-align:left;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s0{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #356854;background-color:#356854;text-align:left;color:#ffffff;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s4{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #ffffff;background-color:#ffffff;text-align:left;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s21{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;color:#434343;font-family:docs-Roboto,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s12{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;color:#434343;font-family:docs-Roboto,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s16{border-bottom:1px SOLID #f6f8f9;background-color:#ffffff;}.ritz .waffle .s20{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s10{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s1{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #356854;background-color:#356854;text-align:left;color:#ffffff;font-family:docs-Roboto,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s7{text-overflow:ellipsis;overflow:hidden;vertical-align:top;display:inline-block;height:fit-content;border-radius:8px;}.ritz .waffle .s15{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:normal;overflow:hidden;word-wrap:break-word;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s19{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:left;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s18{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #f6f8f9;background-color:#f6f8f9;text-align:right;color:#434343;font-family:docs-Roboto,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s6{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #ffffff;background-color:#ffffff;text-align:left;color:#434343;font-family:docs-Roboto,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 3px 2px 3px;}.ritz .waffle .s5{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #ffffff;background-color:#ffffff;text-align:left;text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#434343;font-family:Verdana;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s2{border-bottom:1px SOLID #284e3f;border-right:1px SOLID #284e3f;background-color:#356854;text-align:left;color:#ffffff;font-family:docs-Roboto,Arial;font-size:10pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}.ritz .waffle .s22{background-color:#f6f8f9;}.ritz .waffle .s3{border-bottom:1px SOLID #f6f8f9;border-right:1px SOLID #ffffff;background-color:#ffffff;text-align:right;color:#434343;font-family:docs-Roboto,Arial;font-size:11pt;vertical-align:middle;white-space:nowrap;overflow:hidden;direction:ltr;padding:2px 8px 2px 8px;}</style><div class="ritz grid-container" dir="ltr"><table class="waffle" cellspacing="0" cellpadding="0"><thead><tr><th class="row-header freezebar-vertical-handle"></th><th id="0C0" style="width:33px;" class="column-headers-background">A</th><th id="0C1" style="width:954px;" class="column-headers-background">B</th><th id="0C2" style="width:367px;" class="column-headers-background">C</th><th id="0C3" style="width:110px;" class="column-headers-background">D</th><th id="0C4" style="width:959px;" class="column-headers-background">E</th></tr></thead><tbody><tr style="height: 30px"><th id="0R0" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">1</div></th><td class="s0" dir="ltr"><div style="display:flex; vertical-align:middle"><svg width="1.25em" viewBox="0 0 20 20" style="display: inline; padding-right: max(6px, 0.25em)"><g id="number-table-header-icon"><path d="M5 16.6667L5.83333 13.3333H2.5L2.91667 11.6667H6.25L7.08333 8.33333H3.75L4.16667 6.66667H7.5L8.33333 3.33333H10L9.16667 6.66667H12.5L13.3333 3.33333H15L14.1667 6.66667H17.5L17.0833 8.33333H13.75L12.9167 11.6667H16.25L15.8333 13.3333H12.5L11.6667 16.6667H10L10.8333 13.3333H7.5L6.66667 16.6667H5ZM7.91667 11.6667H11.25L12.0833 8.33333H8.75L7.91667 11.6667Z" fill="#ffffff"></path></g></svg><div>No</div></div></td><td class="s0" dir="ltr">Change</td><td class="s0" dir="ltr">Refrence</td><td class="s1" dir="ltr"><div style="display:flex; vertical-align:middle"><svg width="1.25em" viewBox="0 0 20 20" style="display: inline; padding-right: max(6px, 0.25em)"><g id="dropdown-table-header-icon"><path d="M15 9L12 12L9 9H15Z" fill="#ffffff"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M7 4H13C16.31 4 19 6.69 19 10C19 13.31 16.31 16 13 16H7C3.69 16 1 13.31 1 10C1 6.69 3.69 4 7 4ZM7 14.5H13C15.48 14.5 17.5 12.48 17.5 10C17.5 7.52 15.48 5.5 13 5.5H7C4.52 5.5 2.5 7.52 2.5 10C2.5 12.48 4.52 14.5 7 14.5Z" fill="#ffffff"></path></g></svg><div>Status </div></div></td><td class="s2" dir="ltr">Dev note</td></tr><tr><th style="height:3px;" class="freezebar-cell freezebar-horizontal-handle"></th><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td><td class="freezebar-cell"></td></tr><tr style="height: 30px"><th id="0R1" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">2</div></th><td class="s3" dir="ltr">1</td><td class="s4" dir="ltr">Update readme file we not integrate socket then why mentioned in read me file</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/kLEtyfyO7dny">https://prnt.sc/kLEtyfyO7dny</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #d4edbc; color: #11734b; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Completed</span></td><td class="s8" dir="ltr">by mistake added it , then not pushed new readme file that&#39;s why ..</td></tr><tr style="height: 30px"><th id="0R2" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">3</div></th><td class="s9" dir="ltr">2</td><td class="s10" dir="ltr">Can you share reason why you added AI filed in config ? </td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/6pPgVtdDPeep">https://prnt.sc/6pPgVtdDPeep</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #d4edbc; color: #11734b; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Completed</span></td><td class="s13" dir="ltr">because i added a ai chat-bot for testing purpose and knowledge powers , i added this keys in config because of easy toggle on/off , by the way i removed that ..</td></tr><tr style="height: 30px"><th id="0R3" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">4</div></th><td class="s3" dir="ltr">3</td><td class="s14" dir="ltr">How you categories this 4 module type ?</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/xF4SMubY-WeM">https://prnt.sc/xF4SMubY-WeM</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #d4edbc; color: #11734b; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Completed</span></td><td class="s8" dir="ltr">because of i not have proper idea which which modules we have so i added some for testing purpose ..</td></tr><tr style="height: 30px"><th id="0R4" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">5</div></th><td class="s9" dir="ltr">4</td><td class="s15" dir="ltr">Already asked you in meeting remvoe emergency type from enum. no need for that also can you give reason or meaing of none value </td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/YwcsOJMHbWmZ">https://prnt.sc/YwcsOJMHbWmZ</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #d4edbc; color: #11734b; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Completed</span></td><td class="s13" dir="ltr">first i added this type then we disscused in meating , by the way no worry i updated it <br>export enum Priority {<br>  URGENT = &quot;urgent&quot;,<br>  HIGH = &quot;high&quot;,<br>  MEDIUM = &quot;medium&quot;,<br>  LOW = &quot;low&quot;,<br>}</td></tr><tr style="height: 30px"><th id="0R5" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">6</div></th><td class="s3" dir="ltr">5</td><td class="s14" dir="ltr">Why key and value pair are same in all enum? please check backend-ms model.how we follows</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/qnqcs7gjp-Gk">https://prnt.sc/qnqcs7gjp-Gk</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #d4edbc; color: #11734b; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Completed</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R6" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">7</div></th><td class="s9" dir="ltr">6</td><td class="s10" dir="ltr">Replace this keys with accutal user id ? what if we need to show user avatar</td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/JVlSf8CEK6yE">https://prnt.sc/JVlSf8CEK6yE</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R7" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">8</div></th><td class="s3" dir="ltr">7</td><td class="s14" dir="ltr">Also you not follow key stucture. why you don&#39;t adde ticket slug in key name</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/2d0higolyLMe">https://prnt.sc/2d0higolyLMe</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R8" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">9</div></th><td class="s9" dir="ltr">8</td><td class="s15" dir="ltr">If multiple item id needed then why you take string ? and what is meaning of talking attchment count and has_attchment.you already worked in recipe module we added new table recipe resource</td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/IKdd0Cr4kBCT">https://prnt.sc/IKdd0Cr4kBCT</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R9" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">10</div></th><td class="s3" dir="ltr">9</td><td class="s14" dir="ltr">Please give proper reason why added this code in model and what is usage</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/_hkDf9dmcCqX">https://prnt.sc/_hkDf9dmcCqX</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R10" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">11</div></th><td class="s9" dir="ltr">10</td><td class="s10" dir="ltr">what if i don&#39;t pass page and limit ? please check proper if i pass pagination only that time it&#39;s applied</td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/jaSBZx3dbAH4">https://prnt.sc/jaSBZx3dbAH4</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R11" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">12</div></th><td class="s3" dir="ltr">11</td><td class="s14" dir="ltr">Remove unecssary date from response</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/FPBJlBs94-_1">https://prnt.sc/FPBJlBs94-_1</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R12" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">13</div></th><td class="s9" dir="ltr">12</td><td class="s10" dir="ltr">Give me explain why getAnalytics api you created and what is purpose for that</td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/ct_Wkr3RT3GY">https://prnt.sc/ct_Wkr3RT3GY</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R13" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">14</div></th><td class="s3" dir="ltr">13</td><td class="s14" dir="ltr">Remove AI chat bot realated  code from main branches. add into seperate branch once PM confirm than add in to current code</td><td class="s4" dir="ltr"><span style="text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;"><a target="_blank" href="https://prnt.sc/L4p2KHuk7v49">https://prnt.sc/L4p2KHuk7v49</a></span><span style="color:#000000;"> , </span><span style="text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;"><a target="_blank" href="https://prnt.sc/Z4oYO59Zj197">https://prnt.sc/Z4oYO59Zj197</a></span></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R14" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">15</div></th><td class="s9" dir="ltr">14</td><td class="s10" dir="ltr">Why you added unnessary filters</td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/kgMqmSNBQcz6">https://prnt.sc/kgMqmSNBQcz6</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R15" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">16</div></th><td class="s3" dir="ltr">15</td><td class="s14" dir="ltr">Please add proper common export funcation</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/hcH3DqoWfoTv">https://prnt.sc/hcH3DqoWfoTv</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R16" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">17</div></th><td class="s9" dir="ltr">16</td><td class="s10" dir="ltr">Update this API. you have to check org_id condition direct with find ticket condtion not after fetching record</td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/NTqgy-Qx-SMH">https://prnt.sc/NTqgy-Qx-SMH</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R17" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">18</div></th><td class="s3" dir="ltr">17</td><td class="s14" dir="ltr">Remove this unnecessary consoles. please do same type of change in whole app.</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/K7jRrVaiq59T">https://prnt.sc/K7jRrVaiq59T</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R18" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">19</div></th><td class="s9" dir="ltr">18</td><td class="s10" dir="ltr">Please explain me this save function once you reach this section. </td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/ABeiocdcBywU">https://prnt.sc/ABeiocdcBywU</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R19" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">20</div></th><td class="s3" dir="ltr">19</td><td class="s14" dir="ltr">Is this role exist in our project ?</td><td class="s5" dir="ltr"><a target="_blank" href="https://prnt.sc/l8Is3jDbDAfI">https://prnt.sc/l8Is3jDbDAfI</a></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R20" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">21</div></th><td class="s9" dir="ltr">20</td><td class="s10" dir="ltr">Remove this type of unnecessary data from whole module </td><td class="s11" dir="ltr"><a target="_blank" href="https://prnt.sc/1pf8G1JTfbO7">https://prnt.sc/1pf8G1JTfbO7</a></td><td class="s12" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s17"></td></tr><tr style="height: 30px"><th id="0R21" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">22</div></th><td class="s3" dir="ltr">21</td><td class="s14" dir="ltr">if we added Auth middleware in route. then why you check in api. please do same change in whole project.</td><td class="s14" dir="ltr"> <span style="text-decoration:underline;text-decoration-skip-ink:none;-webkit-text-decoration-skip:none;color:#1155cc;"><a target="_blank" href="https://prnt.sc/IbwP7lPkioa3">https://prnt.sc/IbwP7lPkioa3</a></span></td><td class="s6" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s16"></td></tr><tr style="height: 30px"><th id="0R22" style="height: 30px;" class="row-headers-background"><div class="row-header-wrapper" style="line-height: 30px">23</div></th><td class="s18" dir="ltr">22</td><td class="s19" dir="ltr">same type of code in three api why you don&#39;t use ani type and managre in one api </td><td class="s20" dir="ltr"><a target="_blank" href="https://prnt.sc/lYSDvqay5XFt">https://prnt.sc/lYSDvqay5XFt</a></td><td class="s21" dir="ltr"><span class="s7" style="background-color: #ffe5a0; color: #473821; width: 82.0px; max-width: 82.0px; margin-left: 6.0px;  padding: 1.0px 5.0px 1.0px 5.0px ; ">Pending</span></td><td class="s22"></td></tr></tbody></table></div>