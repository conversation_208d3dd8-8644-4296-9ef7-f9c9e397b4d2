import { Response } from "express";
import { StatusCodes } from "http-status-codes";

/**
 * Standardized response helper for Customer Service module
 * Following the same patterns as the recipe module
 */
export class ResponseHelper {
  /**
   * Send success response
   */
  static success(
    res: Response,
    message: string,
    data?: any,
    statusCode: number = StatusCodes.OK
  ): Response {
    return res.status(statusCode).json({
      status: true,
      message: res.__(message) || message,
      data: data || null,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send error response
   */
  static error(
    res: Response,
    message: string,
    error?: any,
    statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR
  ): Response {
    const response: any = {
      status: false,
      message: res.__(message) || message,
      timestamp: new Date().toISOString(),
    };

    // Include error details in development mode
    if (process.env.NODE_ENV === "development" && error) {
      response.error =
        typeof error === "string" ? error : error.message || error;
      if (error.stack) {
        response.stack = error.stack;
      }
    }

    return res.status(statusCode).json(response);
  }

  /**
   * Send validation error response
   */
  static validationError(
    res: Response,
    errors: string[] | any,
    message: string = "VALIDATION_ERROR"
  ): Response {
    return res.status(StatusCodes.BAD_REQUEST).json({
      status: false,
      message: res.__(message) || "Validation failed",
      errors: Array.isArray(errors) ? errors : [errors],
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send unauthorized response
   */
  static unauthorized(
    res: Response,
    message: string = "UNAUTHORIZED_ACCESS"
  ): Response {
    return res.status(StatusCodes.UNAUTHORIZED).json({
      status: false,
      message: res.__(message) || "Unauthorized access",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send forbidden response
   */
  static forbidden(
    res: Response,
    message: string = "INSUFFICIENT_PERMISSIONS"
  ): Response {
    return res.status(StatusCodes.FORBIDDEN).json({
      status: false,
      message: res.__(message) || "Insufficient permissions",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send not found response
   */
  static notFound(
    res: Response,
    message: string = "RESOURCE_NOT_FOUND"
  ): Response {
    return res.status(StatusCodes.NOT_FOUND).json({
      status: false,
      message: res.__(message) || "Resource not found",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send conflict response
   */
  static conflict(
    res: Response,
    message: string = "RESOURCE_CONFLICT"
  ): Response {
    return res.status(StatusCodes.CONFLICT).json({
      status: false,
      message: res.__(message) || "Resource conflict",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send rate limit exceeded response
   */
  static rateLimitExceeded(
    res: Response,
    message: string = "RATE_LIMIT_EXCEEDED",
    retryAfter?: number
  ): Response {
    if (retryAfter) {
      res.set("Retry-After", retryAfter.toString());
    }

    return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
      status: false,
      message: res.__(message) || "Rate limit exceeded",
      retry_after: retryAfter,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send service unavailable response
   */
  static serviceUnavailable(
    res: Response,
    message: string = "SERVICE_UNAVAILABLE"
  ): Response {
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__(message) || "Service temporarily unavailable",
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send paginated response
   */
  static paginated(
    res: Response,
    message: string,
    data: {
      items: any[];
      totalItems: number;
      totalPages: number;
      currentPage: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    },
    statusCode: number = StatusCodes.OK
  ): Response {
    return res.status(statusCode).json({
      status: true,
      message: res.__(message) || message,
      data: {
        items: data.items,
        pagination: {
          total_items: data.totalItems,
          total_pages: data.totalPages,
          current_page: data.currentPage,
          has_next_page: data.hasNextPage,
          has_prev_page: data.hasPrevPage,
          items_per_page: data.items.length,
        },
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send created response
   */
  static created(res: Response, message: string, data?: any): Response {
    return this.success(res, message, data, StatusCodes.CREATED);
  }

  /**
   * Send accepted response (for async operations)
   */
  static accepted(res: Response, message: string, data?: any): Response {
    return this.success(res, message, data, StatusCodes.ACCEPTED);
  }

  /**
   * Send no content response
   */
  static noContent(res: Response): Response {
    return res.status(StatusCodes.NO_CONTENT).send();
  }
}

/**
 * Customer Service specific response helpers
 */
export class CustomerServiceResponseHelper extends ResponseHelper {
  /**
   * Send ticket created response
   */
  static ticketCreated(res: Response, ticket: any): Response {
    return this.created(res, "TICKET_CREATED_SUCCESSFULLY", {
      ticket_id: ticket.id,
      ticket_number: ticket.ticket_number,
      status: ticket.ticket_status,
      priority: ticket.ticket_priority,
      created_at: ticket.created_at,
    });
  }

  /**
   * Send ticket updated response
   */
  static ticketUpdated(res: Response, ticket: any): Response {
    return this.success(res, "TICKET_UPDATED_SUCCESSFULLY", {
      ticket_id: ticket.id,
      ticket_number: ticket.ticket_number,
      status: ticket.ticket_status,
      updated_at: ticket.updated_at,
    });
  }

  /**
   * Send ticket assigned response
   */
  static ticketAssigned(
    res: Response,
    ticket: any,
    assignedUser?: any
  ): Response {
    return this.success(res, "TICKET_ASSIGNED_SUCCESSFULLY", {
      ticket_id: ticket.id,
      ticket_number: ticket.ticket_number,
      assigned_to: assignedUser
        ? {
            id: assignedUser.id,
            email: assignedUser.user_email,
          }
        : null,
      assigned_at: ticket.assigned_at,
    });
  }

  /**
   * Send ticket escalated response
   */
  static ticketEscalated(res: Response, ticket: any): Response {
    return this.success(res, "TICKET_ESCALATED_SUCCESSFULLY", {
      ticket_id: ticket.id,
      ticket_number: ticket.ticket_number,
      status: ticket.ticket_status,
      escalation_reason: ticket.escalation_reason,
    });
  }

  /**
   * Send message sent response
   */
  static messageSent(res: Response, message: any): Response {
    return this.created(res, "MESSAGE_SENT_SUCCESSFULLY", {
      message_id: message.id,
      ticket_id: message.ticket_id,
      sender_type: message.sender_type,
      created_at: message.created_at,
    });
  }

  /**
   * Send analytics response
   */
  static analytics(
    res: Response,
    analyticsData: any,
    period?: { start_date: string; end_date: string }
  ): Response {
    return this.success(res, "ANALYTICS_FETCHED", {
      ...analyticsData,
      period,
      generated_at: new Date().toISOString(),
    });
  }

  /**
   * Send bulk operation response
   */
  static bulkOperation(
    res: Response,
    operation: string,
    results: {
      successful: number;
      failed: number;
      total: number;
      errors?: any[];
    }
  ): Response {
    const message =
      results.failed > 0
        ? "BULK_OPERATION_PARTIAL_SUCCESS"
        : "BULK_OPERATION_SUCCESS";

    return this.success(res, message, {
      operation,
      results: {
        successful: results.successful,
        failed: results.failed,
        total: results.total,
        success_rate:
          ((results.successful / results.total) * 100).toFixed(2) + "%",
      },
      errors: results.errors || [],
    });
  }

  /**
   * Send search results response
   */
  static searchResults(
    res: Response,
    results: any[],
    query: string,
    totalFound: number
  ): Response {
    return this.success(res, "SEARCH_COMPLETED", {
      query,
      results,
      total_found: totalFound,
      result_count: results.length,
    });
  }

  /**
   * Send file upload response
   */
  static fileUploaded(
    res: Response,
    fileInfo: {
      filename: string;
      size: number;
      type: string;
      url?: string;
    }
  ): Response {
    return this.created(res, "FILE_UPLOAD_SUCCESSFUL", {
      file: {
        name: fileInfo.filename,
        size: fileInfo.size,
        type: fileInfo.type,
        url: fileInfo.url,
        uploaded_at: new Date().toISOString(),
      },
    });
  }

  /**
   * Send configuration response
   */
  static configuration(
    res: Response,
    config: any,
    isUpdate: boolean = false
  ): Response {
    const message = isUpdate ? "CONFIG_UPDATED" : "CONFIG_FETCHED";
    return this.success(res, message, {
      configuration: config,
      last_updated: new Date().toISOString(),
    });
  }

  /**
   * Send health check response
   */
  static healthCheck(res: Response, healthData: any): Response {
    const isHealthy = Object.values(healthData).every(
      (status: any) =>
        status === "healthy" || status === "connected" || status === true
    );

    return res
      .status(isHealthy ? StatusCodes.OK : StatusCodes.SERVICE_UNAVAILABLE)
      .json({
        status: isHealthy,
        message: isHealthy ? "SYSTEM_HEALTHY" : "SYSTEM_UNHEALTHY",
        health: healthData,
        timestamp: new Date().toISOString(),
      });
  }
}

// Export both classes
export default ResponseHelper;
