// import fs from "fs";
// import path from "path";
// import { getHash, RECIPE_FILE_UPLOAD_CONSTANT, getMimeTypeFromExtension } from "../helper/common";
// import { db } from "../models/index";
// import { item_type, item_status, item_IEC, item_external_location } from "../models/Item";
// import {
//   S3Client,
//   PutObjectCommand,
//   GetObjectCommand,
// } from "@aws-sdk/client-s3";

// // Setup MinIO client
// const s3 = new S3Client({
//   endpoint: global.config.MINIO_ENDPOINT,
//   region: "us-east-1",
//   forcePathStyle: true,
//   credentials: {
//     accessKeyId: global.config.MINIO_ACCESS_KEY,
//     secretAccessKey: global.config.MINIO_SECRET_KEY,
//   },
// });

// /**
//  * Ingredient Category Icon Seeder Service
//  * Maps and uploads icons for ingredient categories
//  */
// class IngredientCategoryIconSeederService {
//   private readonly bucketName: string;

//   constructor() {
//     this.bucketName = global.config.MINIO_BUCKET_NAME || process.env.NODE_ENV || "development";
//   }

//   /**
//    * Main method to seed ingredient category icons
//    */
//   async seedIngredientCategoryIcons(
//     iconsDirectory: string,
//     iconMapping?: Record<string, string>
//   ): Promise<void> {
//     try {

//       // Get all ingredient categories
//       const categories = await db.Category.findAll({
//         where: {
//           category_type: "ingredient",
//           is_system_category: true,
//           organization_id: null,
//         },
//         order: [["category_name", "ASC"]],
//       });

//       const mapping = iconMapping || this.getDefaultIconMapping();

//       for (const category of categories) {
//         const categoryName = category.category_name;
//         const iconFileName = mapping[categoryName];

//         if (iconFileName) {
//           const iconPath = path.join(iconsDirectory, iconFileName);
          
//           if (fs.existsSync(iconPath)) {
//             const itemId = await this.uploadCategoryIcon(categoryName, iconPath, iconFileName);
            
//             if (itemId) {
//               // Update category with icon
//               await category.update({ category_icon: itemId });

//             }
//           } else {

//           }
//         } else {

//         }
//       }

//     } catch (error) {
//       console.error("❌ Error in Ingredient Category Icon Seeder:", error);
//       throw error;
//     }
//   }

//   /**
//    * Default icon mapping for ingredient categories
//    */
//   private getDefaultIconMapping(): Record<string, string> {
//     return {
//       // Original categories
//       "Dairy": "milk Stroke Black.png",
//       "Meat": "meat Stroke Black.png",
//       "Poultry": "Poultry Stroke Black.png",
//       "Seafood": "Sea Food Stroke Black.png",
//       "Vegetables": "vegetables Stroke Black.png",
//       "Fruits": "fruits Stroke Black.png",
//       "Grains": "Grains Stroke Black.png",
//       "Nuts": "Nuts & Seeds Stroke Black.png",
//       "Herbs & Spices": "Herbs & Spices Stroke Black.png",
//       "Oils": "oil Stroke Black.png",
//       "Condiments": "sauce Stroke Black.png",
//       "Baking": "baking & sweeteners  Stroke Black.png", // Note: extra space in filename
//       "Dry Goods": "Dry Goods & Pulses Stroke Black.png",
//       "Beverages": "Beverages & Drinks Stroke Black.png",

//       // Additional categories that might exist in database
//       "Baking & Sweeteners": "baking & sweeteners  Stroke Black.png", // Note: extra space in filename
//       "Condiments & Sauces": "sauce Stroke Black.png",
//       "Dry Goods & Pulses": "Dry Goods & Pulses Stroke Black.png",
//       "Nuts & Seeds": "Nuts & Seeds Stroke Black.png",
//     };
//   }

//   /**
//    * Upload category icon to S3 and local storage
//    */
//   private async uploadCategoryIcon(
//     categoryName: string,
//     iconPath: string,
//     fileName: string
//   ): Promise<number | null> {
//     try {
//       // Read file
//       const fileBuffer = fs.readFileSync(iconPath);
//       const fileExtension = path.extname(fileName);
//       const fileStats = fs.statSync(iconPath);
      
//       // Create file object that matches getHash expectations
//       const fileObject = {
//         path: iconPath,
//         size: fileStats.size,
//         originalname: fileName,
//         filename: fileName
//       };
      
//       // Generate hash to check for duplicates
//       const fileHashResult = getHash(fileObject);
      
//       if (!fileHashResult.status) {
//         console.error(`Failed to generate hash for ${categoryName} icon:`, fileHashResult.message);
//         return null;
//       }

//       // Check if item already exists with same hash
//       const existingItem = await db.Item.findOne({
//         where: {
//           item_hash: fileHashResult.hash,
//           item_organization_id: null,
//         },
//       });

//       if (existingItem) {

//         return existingItem.id;
//       }

//       // Generate file name for storage
//       const storageFileName = `${categoryName.toLowerCase().replace(/\s+/g, '-')}-icon${fileExtension}`;

//       // Use existing INGREDIENT_CATEGORY_ICON constant for path generation
//       const s3FilePath = RECIPE_FILE_UPLOAD_CONSTANT.INGREDIENT_CATEGORY_ICON.destinationPath(
//         null, // organization name (null for system defaults)
//         null, // category ID (not needed for file path)
//         storageFileName
//       );

//       // Upload to S3
//       await s3.send(
//         new PutObjectCommand({
//           Bucket: this.bucketName,
//           Key: s3FilePath,
//           Body: fileBuffer,
//           ContentType: fileHashResult.actualMimeType || getMimeTypeFromExtension(fileExtension),
//         })
//       );

//       // Also save file locally to uploads directory for local serving
//       const localFilePath = path.join(process.cwd(), "uploads", s3FilePath);
//       const localDir = path.dirname(localFilePath);
      
//       // Create directory if it doesn't exist
//       if (!fs.existsSync(localDir)) {
//         fs.mkdirSync(localDir, { recursive: true });
//       }
      
//       // Write file to local uploads directory
//       fs.writeFileSync(localFilePath, fileBuffer);

//       // Create Item record
//       const itemData = {
//         item_type: item_type.IMAGE,
//         item_name: storageFileName,
//         item_hash: fileHashResult.hash,
//         item_mime_type: fileHashResult.actualMimeType || getMimeTypeFromExtension(fileExtension),
//         item_extension: fileExtension,
//         item_size: fileBuffer.length,
//         item_IEC: item_IEC.B,
//         item_status: item_status.ACTIVE,
//         item_external_location: item_external_location.NO,
//         item_location: s3FilePath,
//         item_organization_id: null,
//         item_category: "category_icon",
//         created_by: 1,
//         updated_by: 1,
//       };

//       const item = await db.Item.create(itemData);

//       return item.id;
//     } catch (error) {
//       console.error(`Error uploading icon for ${categoryName}:`, error);
//       return null;
//     }
//   }

//   /**
//    * List all ingredient categories and their current icon status
//    */
//   async listCategoriesWithIconStatus(): Promise<void> {
//     try {
//       const categories = await db.Category.findAll({
//         where: {
//           category_type: "ingredient",
//           is_system_category: true,
//           organization_id: null,
//         },
//         order: [["category_name", "ASC"]],
//       });

//       for (let i = 0; i < categories.length; i++) {
//         const category = categories[i];
//         let iconInfo = "(No icon)";
//         let hasIcon = "❌";

//         if (category.category_icon) {
//           try {
//             const iconItem = await db.Item.findByPk(category.category_icon);
//             if (iconItem) {
//               hasIcon = "✅";
//               iconInfo = `(${iconItem.item_name})`;
//             }
//           } catch {
//             // Ignore icon lookup errors
//           }
//         }

//       }

//     } catch (error) {
//       console.error("Error listing ingredient categories:", error);
//     }
//   }
// }

// export default new IngredientCategoryIconSeederService();
