# **Recipe Management & Operations Module**

This is a significantly more complex and feature-rich recipe module than the MVP we discussed earlier. This aligns more with a comprehensive system like Kafoodle.

This document will be detailed and suitable for a project kickoff.

---

**Module Specification Document: Recipe Management & Operations**

**Document Version:** 1.1  
**Date:** May 28, 2025  
**Project:** MicrOffice  
**Module Lead:** Vipul Sir, Deepal

## Introduction & Purpose

### **Overview**

This document outlines the specifications for the "Recipe Management & Operations" module. This module will serve as the central system for creating, managing, analyzing, and publishing recipes, primarily targeted at administrative users, chefs, and operational staff. It includes robust ingredient management, detailed recipe creation, costing, nutritional analysis, allergen tracking, version control, and public publishing capabilities.

### **Goals:**

1. Provide a comprehensive platform for end-to-end recipe lifecycle management.  
2. Ensure accurate costing, nutritional information, and allergen declaration for all recipes.  
3. Streamline recipe development, scaling, and versioning.  
4. Offer insights into recipe performance and ingredient usage.  
5. Facilitate optional public sharing of selected recipes with integrated calls to action.  
6. Support operational efficiency for food businesses.

**Target Users (Internal):** Administrators, Chefs, Recipe Developers, Menu Planners, Cost Controllers, Nutritionists.

**Target Users (External \- for Public Recipes):** General public visiting the organization's public-facing website/platform.

**Design Reference: [https://www.figma.com/design/IlIhykQCdhDPjFkCRpDyTF/Recipies?node-id=0-1\&p=f\&t=Rk8U9Zg39mzlfhoK-0](https://www.figma.com/design/IlIhykQCdhDPjFkCRpDyTF/Recipies?node-id=0-1&p=f&t=Rk8U9Zg39mzlfhoK-0)**

## Module List

1. [**Admin Dashboard (Recipe Module Hub)**](#admin-dashboard-\(recipe-module-hub\))

2. [**Ingredient Categories**](#ingrediants-categories)

3. [**Recipie Categories**](#recipie-categories)

4. [**Allergen Management**](#allergen-management)

5. [**Ingredient Management**](#ingredient-management)

6. [**Recipe Creation & Editing**](#recipe-creation-&-editing)

7. [**Recipie Listing**](#recipie-listing) 

8. [**Public Recipe Publishing & Management**](#public-recipe-publishing-&-management)

9. [**Reporting & Exporting**](#reporting-&-exporting)

10. [**Role-Based Access Control (RBAC) within Recipe**](#role-based-access-control-\(rbac\)-within-recipe-module)

11. [**Search & Filtering (Robust)**](#search-&-filtering-\(robust\))

12. [**Public Page analytics**](#public-page-analytics) 

## Module Features & Functionality

### **Admin Dashboard (Recipe Module Hub)** {#admin-dashboard-(recipe-module-hub)}

1.   **Overview Statistics:**  
   1. Display key metrics at a glance:  
   2. Total Recipes (Internal)  
   3. Total Ingredients (Master Database)  
   4. Total Allergens (Default \+ Org-Specific Custom)  
   5. Total Vendors/Suppliers  
   6. Total Recipe Categories  
   7. Total Units of Measure  
   8. Each stat is clickable, leading to the respective detailed listing/management page.

2. **Public Recipe Insights:**

   1. Total Published Public Recipes count (clickable to a filtered list of public recipes).

   2.  Graphical display (e.g., bar chart) of "Top 10 Most Liked Public Recipes" (clickable to recipe detail).

   3. Graphical display of "Top 10 Most Viewed/Clicked Public Recipes" (clickable to recipe detail).

3. **Quick Actions:**  
   \- Direct links to "Create New Recipe," "Manage Ingredients," etc.

   

### **Ingrediants Categories**  {#ingrediants-categories}

1.  **Ingredient Category Database & Attributes:**

            A central repository will store information for each ingredient category, including:

1.   ID: (Primary Key, Auto-increment) Unique system identifier.

   2.  Name: (Text, Required, Unique within its scope \- Default global, Custom per Org) The display name of the category (e.g., "Dairy," "Vegetables").

   3.   Slug: (Text, Required, Unique within its scope) Auto-generated from the name, URL-friendly (e.g., "dairy," "fresh-vegetables"). Used for system references.

   4.   Icon: (Optional) Icon or a symbol representing the category for UI purposes.

   5.   Status: (Enum: Active, Inactive \- Default: Active) Determines if the category is available for use. Custom categories can be deactivated. Default categories cannot be deactivated by organizations.

   6.   IsDefault: (Boolean, Required) True if it's a system-provided default category, False if it's a custom category created by an organization.

   7.   OrganizationID: (Foreign Key, Nullable) If \`IsDefault\` is False, this links to the organization that created/owns this custom category. Null for default categories.

   8.   CreatedBy: (User ID/Reference, Nullable) User who created the category (especially for custom categories).

   9.   CreatedDateTime: (Timestamp) Date and time of category creation.

   10.   UpdatedDateTime: (Timestamp) Date and time of last update.

2. **Default Ingredient Categories:**

 The system will be pre-populated with the following non-deletable, globally available default ingredient categories (\`IsDefault: True\`):

1.    Dairy

   2.    Meat

   3.    Poultry

   4.    Seafood

   5.    Vegetables

   6.    Fruits

   7.    Grains

   8.    Nuts & Seeds 

   9.    Herbs & Spices

   10.    Oils & Fats 

   11.    Condiments & Sauces 

   12.    Baking & Sweeteners 

   13.    Dry Goods & Pulses 

   14.    Beverages & Drinks 

3. **Ingredient Category Listing & Viewing:**

   1.    Interface: A dedicated screen accessible to authorized administrators.

   2.    Display: Lists all available ingredient categories, showing Name, Status, Type (Default/Custom). Super-admins will see all; organization admins will see default \+ their own custom categories.

   3.    Search: Ability to search categories by name.

   4.    Filtering:

      1.  Filter by 	(Active, Inactive \- for custom categories).

      2. Filter by Type (Default, Custom).

      3.  (Supereme-admin only) Filter by OrganizationID for custom categories.

   5.    Sorting: Sort by Name, Type.

4. **Create Ingredient Category (Custom Categories Only):**

   1.    Access: Restricted to authorized administrators of an organization.

   2.    Process: Users can add new ingredient categories relevant to their specific needs.

   3.    Form Fields: Name (slug auto-generated), Icon (optional). Status defaults to Active. \`IsDefault\` is False, \`OrganizationID\` is set to the creator's organization.

   4.    Scope: Custom categories are specific to the creating organization.

   5.    Validation: Ensure category name (and thus slug) is unique within the organization's custom categories and does not conflict with default category names/slugs.

5. **Update Ingredient Category:**

   1.  Default Categories:

      1. Only updatable by system super-administrators (e.g., to refine names, slugs, or icons globally Vipul Sir’s Superme admin account).

      2. Organization-level admins cannot modify default categories.

   2.    Custom Categories:

      1. Organization administrators can update the Name (slug will regenerate if name changes and slug is not manually locked), Icon, and Status of categories they created, not of other orgs.

6. **Delete Ingredient Category (Custom Categories Only):**

   1.    Access: Restricted to authorized administrators of an organization.

   2.    Conditions:

      1.  A custom ingredient category can only be deleted if it is not currently assigned to any ingredients within that organization.

      2.  If in use, the system will prevent deletion and display a message indicating which ingredients are using it.

   3.    Default Categories: Cannot be deleted by any organization-level administrator.

   4.   Process: Confirmation required before deletion.

7. **Linking:**  Ingredient Categories are linked to Ingredients.(a Ingredient can typically belong to one primary category, but multi-categorization could be supported if needed).

### **Recipie Categories** {#recipie-categories}

1. **Recipe Category Database & Attributes:**

   A central repository will store information for each recipe category, including:

   1.    ID: (Primary Key, Auto-increment) Unique system identifier.

   2.     Name: (Text, Required, Unique within its scope \- Default global, Custom per Org) The display name of the category (e.g., "Appetizers," "Main Courses \- Poultry," "Desserts").

   3.     Slug: (Text, Required, Unique within its scope) Auto-generated from the name, URL-friendly (e.g., "appetizers," "main-courses-poultry").

   4.     ParentCategoryID: (Foreign Key, Nullable) Allows for hierarchical categories (e.g., "Poultry" under "Main Courses"). Self-referencing.

   5.     Description: (Text, Optional) A brief description of the category.

   6.     Icon: (Text/URL, Optional) Path to an icon or a symbol representing the category.

   7.     Status: (Enum: Active, Inactive \- Default: Active) Determines if the category is available for use.

   8.     IsDefault: (Boolean, Required) True if it's a system-provided default category, False if custom.

   9.     OrganizationID: (Foreign Key, Nullable) If \`IsDefault\` is False, links to the creating organization.

   10.     CreatedBy: (User ID/Reference, Nullable)

   11.     CreatedDateTime: (Timestamp)

   12.     UpdatedDateTime: (Timestamp)

2. **Default Recipe Categories (Examples \- to be defined by system):**

The system will be pre-populated with common, non-deletable, globally available default recipe categories (\`IsDefault: True\`). Examples:

1.     Appetizers & Starters

   2.     Soups

   3.     Salads

   4.     Main Courses

   5.     Side Dishes

   6.     Desserts

   7.     Baked Goods

   8.     Beverages

   9.     Breakfast & Brunch

   10.     Sauces & Dressings

   11.     Snacks

3. **Recipe Category Listing & Viewing:**

   1.    Interface: A dedicated screen accessible to authorized administrators.

   2.     Display: Lists recipe categories, showing Name, Parent Category (if any), Status, Type (Default/Custom). Can be displayed hierarchically.

   3.     Search: Ability to search categories by name.

   4.     Filtering: By Status, Type, (Super-admin) OrganizationID.

   5.     Sorting: By Name, Type.

4. **Create Recipe Category (Custom Categories Only):**

   1.   Access: Restricted to authorized administrators of an organization.

   2.   Form Fields: Name (slug auto-generated), Parent Category (optional, select from existing default or org's custom categories), Description (optional), Icon (optional). Status defaults to Active.

   3.   Scope: Custom categories are specific to the creating organization.

   4.   Validation: Ensure category name (and slug) is unique within its level and parent for that organization.

5. **Update Recipe Category:**

   1.   Default Categories: Only updatable by system super-administrators.

   2.     Custom Categories: Organization administrators can update Name, Parent Category, Description, Icon, and Status of their own custom categories.

6.  **Delete Recipe Category (Custom Categories Only):**

   1.  Access: Restricted to authorized administrators of an organization.

   2.   Conditions:

      1. A custom recipe category can only be deleted if it is not assigned to any recipes within that organization AND if it has no child categories.

      2.  If in use or has children, deletion is prevented with an informative message.

   3.     Default Categories: Cannot be deleted by organization-level administrators.

   4.     Process: Confirmation required.

7.  **Linking:** Recipe Categories are linked to Recipes (an Recipe can potentially belong to one or more categories, depending on design \- typically one category).

8. 

   

### **Allergen Management** {#allergen-management}

1.  **Allergen Database:**

   1. Attributes per Allergen: Name (Standardized), Type (Default/Custom \- Org Specific), Allergen Code (Internal ID), Description/Notes.

   2. Default Allergens: System-provided list (e.g., **14 major**), non-deletable by organizations, globally consistent.  
      1. Gluten  
      2. Crustaceans  
      3. Eggs  
      4. Fish  
      5. Peanuts  
      6. Soy  
      7. Milk  
      8. Tree Nuts  
      9. Celery  
      10. Mustard  
      11. Sesame  
      12. Sulphites  
      13. Lupin  
      14. Molluscs

   3. Custom Allergens: Created by an organization, scoped to that organization. Cannot conflict with default names.

2. **CRUD Operations:**

   1. Admins can view all default allergens.  
      Org-admins can CRUD their own custom allergens. Default allergens are not editable/deletable by org-admins.

3. **Linking:**

   1. Allergens are linked to Ingredients.

### **Ingredient Management** {#ingredient-management}

1.  **Ingredient Database: Central repository.**

       Attributes per Ingredient:

1. Name (Required)

   2. Description, 

   3. Units of Measure (Primary storage unit, e.g., grams, ml)

      1. g 

      2. kg 

      3. ml 

      4. liter 

      5. unit 

      6. oz (ounce)

      7. Ib (pound)

      8. tbsp (tablespoon)

      9. tsp (teaspoon)

      10. cup 

      11. pint 

      12. quart 

      13. gallon

      14. each

      15. clove

      16. ball

      17. slice 

      18. serving 

      19. leaf

      20. pinch 

      21. wedge 

      22. sprig

      23. pack 

      24. case

      25. dozen

   4. Unit Conversion Capabilities (Define relationships, e.g., 1 cup \= 240ml, 1 egg ≈ 50g \- system should allow defining these for calculation assistance)

   5. Supplier Information (Link to Vendor module; can store multiple suppliers per ingredient with their specific code, price, pack size)

   6. Cost Per Unit (Calculated based on selected supplier's pack size and price; historical costing considered)

   7. Nutritional Information (Per 100g/ml: Calories, Protein, Carbs, Fat, Sugars, Fiber, Sodium, and other configurable micronutrients)

   8. Allergen Information (Tagging from Allergen Module: Default \+ Org-Specific Custom)

   9. Dietary Suitability (Tags: Vegan, Vegetarian, Gluten-Free, Halal, Kosher, etc.)

   10. Waste Percentage (e.g., 10% for potato peeling – used in costing)

   11. Category/Tags (For organization and filtering, e.g., "Produce," "Dairy," "Dry Goods") As mentioned above [here](#ingrediants-categories) 

   12. Status (Active, Inactive)

2. **CRUD Operations:**

   1. Full Create, Read, Update, Delete (with safeguards for ingredients used in recipes) functionality.

3. **Import/Export:**

   1. Bulk import of ingredients from CSV/Excel (mapping supplier data). Export ingredient list.

4.  **Search & Filtering:**

   1. Robust search by name, category, supplier, allergen, dietary tag.

### **Recipe Creation & Editing** {#recipe-creation-&-editing}

1. **Recipe Details:**

   1.  Name, Description, Category (e.g., Main Course, Dessert), Cuisine Type (e.g., Italian, Mexican), Custom Tags,Public Names(Optional)

   2. Internal Reference Code/ID.

2. **Yield & Portioning:**

   1. Define Total Recipe Yield (e.g., "2 kg" or "5 Liters" or "1 large tray").

   2. Define Number of Portions (e.g., "10 portions").

   3. Define Standard Portion Size (e.g., "200g," "250ml," "1 slice" \- system assists with consistency).

   4. Automatic calculation of portion size if total yield and number of portions are given, or vice-versa.

3. **Ingredient List:**

   1. Add ingredients from the Ingredient Database (search/browse).

   2. Specify Quantity and Unit for each ingredient as used in the recipe (system will use conversion data for costing/nutrition if recipe unit differs from ingredient's storage unit).

   3. Ability to add "Sub-Recipes": Select an existing internal recipe to be included as an ingredient line item. Its cost, nutrition, and allergens will roll up.

   4. Notes per ingredient line item (e.g., "finely chopped").

4. **Method/Instructions:** 

   1. Step-by-step instructions.

   2. Rich Text Formatting (Bold, Italics, Underline, Lists, Links).

   3. (Advanced V2/Optional for initial build): Ability to add images or short video clips per step. Each step wise placeholder image (optional)

5. **Timings:**

   1. Preparation Time (e.g., 30 minutes).

   2. Cook Time (e.g., 45 minutes).

   3. Total Time (Auto-calculated).

6. **Recipe Scaling:**

   1. **Function:** Allow users to scale the entire recipe up or down based on a new desired total yield OR a new number of portions.

   2. **Logic:**

   1.Determine the scaling factor: Scaling Factor \= New Target / Original Target. (If scaling by yield: New Target Yield / Original Recipe Yield. If scaling by portions: New Number of Portions / Original Number of Portions).  
      	2\. Apply scaling factor: Multiply the quantity of each ingredient by the Scaling Factor.  
      	3\. Adjust units if necessary (e.g., if scaling significantly increases grams, offer to convert to kg).  
      	4\. Method/instructions may need manual review by the user after scaling, as cooking times/techniques might not scale linearly. Display a note to this effect.

   3. **UI:** Input field for "New Yield" or "New Portions." Display scaled ingredient list. Option to save as a new version or a new recipe.

7. **Version Control:**

   1. Automatic Versioning: Each significant save of a recipe creates a new version (timestamped, with user who made change).

   2. View Version History: List previous versions with key changes (if change highlighting is enabled).

   3. Revert to Previous Version: Option to make an older version the current active version.

   4. Highlight Changes (Configurable):

   5. Admin configuration option: "Enable Change Highlighting for Internal Recipe Versions."

   6. If YES: When viewing a recipe's version history or comparing to a previous version, the system will attempt to visually highlight what changed (e.g., ingredient quantity, added/removed ingredient, modified step). This is for internal team review and not for public pages.

8. **Duplication ("Create New from Version" / "Duplicate"):**

   1. Easily duplicate an existing recipe (any version) to create a new, independent recipe as a starting point for variations.

9. **Assign Recipe Ownership/Access (Internal):**

   1. Assign recipe to a specific user (creator/owner).

   2. Ability to restrict edit/view access based on user roles (e.g., Chef, Manager) or organizational branches/departments.

10.  **Role Based Insturctions :**

    1. Suppose some specific instruction for Serving team  
    2. Some specific instruction for Head Shef OR Bar Tender

11.  **Status:** 

    1. Draft, Save, Publish, Active/InActive, 

    2. Published /UnPublished.

12. **Costing & Pricing :**

    1. **Automatic Recipe Costing:**

       1. Calculates total recipe cost based on: Quantities of ingredients used, Cost per unit of those ingredients (considering waste percentage), Cost of sub-recipes.

       2. Displays Total Recipe Cost and Cost Per Portion.

    2. **Gross Profit (GP) Calculation:**

       1. Allow user to input a Selling Price per portion/recipe.

       2. Calculate and display Gross Profit Amount, GP Percentage, and Food Cost Percentage.

    3. **"What If" Scenarios (Cost Sensitivity):**

       1. Interface to temporarily adjust individual ingredient costs or the overall selling price to see the immediate impact on total cost and GP. (Does not save these changes to the master ingredient/recipe).

13. **Nutritional Analysis:**

    1. **Automatic Calculation:**

       1. Aggregates nutritional data (calories, macros, specified micros) from all ingredients (and sub-recipes) based on their quantities in the recipe.

       2. Displays per-recipe totals and per-portion averages.

    2. **Traffic Light Labelling (Optional, region-dependent):**

       1. If configured, display color-coded labels (Green, Amber, Red) for Fat, Saturated Fat, Sugar, Salt per portion, based on defined thresholds (e.g., UK FSA guidelines).

    3. **Reference Intakes (RI % / %DV):**

       1.  Display the percentage contribution of key nutrients per portion against daily recommended intakes.

14.  **HACCP** : Add A Section where they can add some details of HACCP standards like shown in Design [https://prnt.sc/HmMZggGyh1t9](https://prnt.sc/HmMZggGyh1t9) 

    1. Basically USer can add category from below and add Description beside it , OR Can use  default and default text will come as below 

       1. **General** : Ensure all control measures are checked and recorded. Ensure food is date labelled and protected correctly. Ensure regular hand washing with an anti-bacterial un-perfumed soap. Always use PPE when preparing food.

       2. **Purchase, Receipt / Delivery,Collect** : Make sure that all food delivered or collected is within its 'use by' date and of acceptable quality at a temperature that will discourage the growth of harmful bacteria

       3. **Storage :** Store food at a temperature that will discourage the growth of harmful bacteria. Below 5°c in the fridge Below \-18°c in the freezer Keep raw and cooked / ready-to-eat foods separate. Use safe handling practices Make sure that food is protected and /or covered

       4. **Preparation :** Minimise the time that food is out of the refrigerator / chiller. Keep raw food to be eaten apart from other raw food which will later be cooked.

       5. **Cooking :** Cook the food to a temperature that will destroy harmful bacteria, until the ​CORE TEMPERATURE is 75°c (82°c in Scotland)

       6. **Hot Holding / Reheating :** Hot display 63°c Reheat to no less than 82°C

       7. **Cooling :** Cool hot food which has just been cooked as quickly as possible before chilling. Ensure food is date labelled correctly

       8. **Allergens :** In the UK the 14 allergens are: **celery, cereals containing gluten** (such as wheat, rye, barley, and oats), **crustaceans** (such as prawns, crabs and lobsters), **eggs, fish, lupin, milk, molluscs** (such as mussels and oysters), **mustard, peanuts, sesame, soybeans, sulphur dioxide and sulphites** (if the sulphur dioxide and sulphites are at a concentration of more than ten parts per million) and **tree nuts** (such as almonds, hazelnuts, walnuts, brazil nuts, cashews, pecans, pistachios and macadamia nuts).

          This also applies to additives, processing aids and any other substances which are present in the final product.

          **Please be aware of handling any products containing these allergens in the kitchen and the possibility of cross contamination.**

15. **Allergen Management (in Recipe Context)**

    1. **Automatic Allergen Highlighting:**

       1. Clearly lists all allergens present in a recipe, aggregated from its ingredients (and sub-recipes) using the tags from the Allergen Module.

    2. **"May Contain" Handling:**

       1. Field to manually add "May Contain" statements for potential cross-contamination risks not inherent to the ingredients themselves (e.g., "Prepared in a kitchen that handles nuts").

    3. **Allergen Matrix Generation (for Menus):**

       1. If recipes are grouped into menus (see **Menu Engineering & Planning**), generate a matrix view showing recipes vs. allergens present.

### **Recipie Listing** {#recipie-listing}

Here’s a refined and user-friendly version of our **Recipe Listing** section, with polished and intuitive titles, organized for clarity and simplicity:  
Ref image : [image 1](https://prnt.sc/Nofy6Jo9R0Dc) , [image 2](https://prnt.sc/UuwGgw0jZODj) 

---

### **🍽 Recipes**

**Explore, manage, and organize your recipes effortlessly.**

---

### **🔎 Smart Filters**

Make it easy for users to narrow down their recipe collection:

* **Bookmark Filter**: *All | Bookmarked | Unbookmarked*

* **Recipe Status**: *Draft | Published | Archived*

* **Privacy Setting**: *Public | Private*

* **Categories**: *Breakfast, Main Course, Dessert, etc.* *(Multi-select)*

* **Dietary Preferences**: *Vegan, Gluten-Free, Keto, etc.*

* **Allergens to Exclude**: *Gluten, Dairy, Eggs, Nuts, etc.*

* **Ingredient Filters**:

  * **Include Ingredients** (e.g., “chicken, garlic”)

  * **Exclude Ingredients** (e.g., “onion, dairy”)

* **Search Bar**: *Search by name or description*

---

### **📋 Recipe Cards Display**

Modern, clean layout combining elements from both screenshots:

* Rounded card design (as in image 1\)

* Card content:

  * Recipe Name

  * Short Description

  * Tags (e.g., "Dessert", "Main Course")

  * Last Updated Date

  * Cost per Portion (if applicable)

  * Status Badge (Draft, Published, etc.)

  * Bookmark Icon (toggle)

* Quick preview modal when clicked

---

### **⚡ Quick Actions on Each Recipe**

Accessible via 3-dot menu or buttons on the card:

* 🔍 **Preview**

* ✏️ **Edit**

* 🗑 **Delete**

* 📄 **Duplicate**

* 🔖 **Bookmark / Unbookmark**

* 📤 **Assign to a Collection or Plan**

* 🌐 **View on Public Page**

---

### **🎛 Layout Header Options**

Top navigation (as seen in image 1 with icons) for quick access:

* **All Recipes**

* **By Meal Type** (Breakfast, Lunch, Dinner...)

* **Desserts | Sides | Snacks | Vegan**, etc.

* **Sort & Filter Buttons**

* **\+ Create Recipe** Button (Top-right, green/primary action)

---

Let me know if you'd like me to design the actual layout in code or Figma-style mockups.

### **Public Recipe Publishing & Management** {#public-recipe-publishing-&-management}

1. **Mark Recipe as "Public":**

   1. Admin/authorized user can toggle a recipe (specific active version) to be "Public."

2. **Public Recipe Detail Page:**

   1. When a recipe is public, it's accessible via a unique, shareable URL on the organization's public-facing site.

   2. Public page displays: Recipe Name, Description, Image (if available), Ingredients, Method, Publicly relevant nutritional summary (optional), Declared Allergens (no "May Contain" unless explicitly chosen for public display).

   3. No internal costing, version history details, or internal notes are shown on the public page.

3. **Call to Action (CTA) Configuration General Common for All Public Recipe:**

   1. Admin can choose a CTA to display on the public recipe detail page:

      1. Contact Info: Display a pre-defined "Contact Us" information block (Name, phone, email, link ).

      2. Contact Us Form: Display an embedded "Contact Us" form. (Name, Email, Mobile,Message,button) (In Public Page add Keptcha-for human identification when user feed contact us form. )

      3. Call To Action : CTA. (CTA Text , Link to attach)

      4. None \- Nothing to show 

4. **Remove from Public:**

   1. Admin can unpublish a recipe.

   2. Once unpublished, the previous public URL should no longer be accessible (e.g., return a 404 or a "This recipe is no longer available" page).

5. **Analytics for Public Recipes (Basic):** Track views/clicks on public recipe pages

#### **UI Representation of Settings screen :** 

Here’s a polished and **user-friendly** version of our **Recipe Settings screen**, keeping clarity and simplicity in mind while still reflecting the feature purpose clearly:

---

### **🍽️ Recipe Settings**

#### **🔒 Private Recipe Visibility Settings**

**Highlight Changes for Assignee**  
 *Toggle On/Off*

Enable this to show highlighted changes (e.g., updated ingredients, removed steps) to the assigned team member when viewing the recipe.

---

#### **🌐 Public Recipe Settings**

**Public Recipe Store Access**  
 *Toggle On/Off*

Enable to make public recipe features available. Turning this off hides all public recipe options.

---

**Public Recipe Call-To-Action (CTA)**

Choose what appears at the bottom of each public recipe page:

* 📞 **Contact Info** – Display a predefined contact block (phone, email, link)

* 📝 **Contact Form** – Show a basic contact form (Name, Email, Phone, Message)

* 🔗 **Custom CTA Link** – Show a custom CTA with text and an external link

* 🚫 **None** – Show nothing

---

**Recipe Details to Display Publicly**

Select which sections of the recipe to show publicly:

* Category  
* Total time  
* Media  
* Links  
* Ingredients  
* Yield & portioning  
* Cost  
* Scale  
* Nutritional information  
* Allergen information  
* Dietary suitability  
* Cuisine type  
* Serve in  
* Garnish  
* Preparation steps

  Default selected Options : 

---

### **Reporting & Exporting** {#reporting-&-exporting}

1. **Print-Friendly Recipe Cards:**

   1. Standardized format including Name, Image, Yield, Portions, Ingredients (with quantities & units), Method, Key Allergens, Basic Nutrition Summary.

2. **Export Recipes:** Option to export selected recipes or all recipes to PDF formats.

3. **Costing Reports:** Summary and detailed costing reports.

4. **Nutritional Reports:** Detailed nutritional breakdowns.

5. **Allergen Reports:** Lists of recipes containing specific allergens.

### **Role-Based Access Control (RBAC) within Recipe Module** {#role-based-access-control-(rbac)-within-recipe-module}

1. **Role-Based Access Control**

   1. Define roles (e.g., Admin, Chef, RecipeDeveloper, ReadOnlyViewer).

   2. Permissions associated with roles (e.g., only Admin/Chef can publish recipes, RecipeDeveloper can create/edit but not publish).

2. **Audit Trails:** Log significant changes to recipes and ingredients (who, what, when).

3. **Collaboration (Basic):** Comments/notes section within a recipe for internal team communication (not for public view).

### **Search & Filtering (Robust)** {#search-&-filtering-(robust)}

1.  Search recipes and ingredients by: Name, Tags, Categories, Cuisine Type.

2. Filter recipes/ingredients by: Allergen (contains/does not contain), Dietary Suitability, Nutrient Content (e.g., low carb, high protein), Cost Range.

### **Public Page Analytics**  {#public-page-analytics}

To visually support the **Public Recipe Dashboard** with a **50%-50% side-by-side layout**, here's a breakdown and suggestion:

---

### **🎯 Left Side (50%) – Recipe CTA Click Analytics**

* **Header**: 📊 *Public Recipe CTA Analytics*

* **Filter Options**:

  * Sort by: `Ascending ⬆️ / Descending ⬇️`

  * Date Range: `Last 7 days`, `This Month`, `Custom Range`

  * Recipe Name (search input)

* **Table**:

| Recipe Name | CTA Type | Clicks | Last Clicked At |
| ----- | ----- | :---: | ----- |
| Chocolate Cake | Contact Info | 25 | 2025-06-03 |
| Pizza Base | Custom CTA | 12 | 2025-06-02 |

    
  **Pagination**: Bottom-right navigation (page-wise)

---

### **🎯 Right Side (50%) – Contact Us Submissions**

* **Header**: 📥 *Contact Us Form Submissions*

* **Filters**:

  * By Recipe

  * By Period: Weekly / Monthly / Yearly / Custom

* **Table**:

| Recipe Name | Name | Email | Mobile | Message | Submitted On | Actions |
| ----- | ----- | ----- | ----- | ----- | ----- | ----- |
| Chocolate Cake | John | <EMAIL> | 123456 | Interested | 2025-06-02 | 🗑️ |

**Export Button**: `Export as CSV/Excel`

* **Delete Support**: Admin can delete individual submissions

* **Mockup Refrence for visualization** , ***Use your own skills to create beautiful UI from this*** [https://prnt.sc/9lXeO1Sjw2fS](https://prnt.sc/9lXeO1Sjw2fS)

---

## Non-Functional Requirements

1. **Performance:** System should be responsive, especially during recipe loading, searching, and calculations.

2. **Scalability:** Architecture should support a growing number of recipes, ingredients, and users

3. **Usability:** Intuitive and efficient interface for all user roles. Minimal training required for core tasks

4. **Accuracy:** Calculations (cost, nutrition, allergens) must be accurate based on the input data.

5. **Security:** Protect sensitive business data (costs, recipes) and ensure proper access controls.  
   3.6. 

6. **Reliability:** High availability of the module.

## Assumptions

1.  Existing robust user authentication and authorization system is in place and can be integrated with for RBAC

2.  Master data for units of measure and initial default allergens will be pre-populated.

3. Users are responsible for the accuracy of the data they input (ingredient costs, nutritional info, allergen tagging). Exclusions (for this specific module, assuming other modules might cover them)

## Exclusions (for this specific module, assuming other modules might cover them)

1. Detailed inventory stock level tracking (though ingredient costs are used).

2. Full procurement and purchase order management.

3. Customer-facing ordering systems (unless the "Public Recipe Page" is considered a part of it).

---

# 🔄 UK Standard Basic Cooking Conversion Table

Here's a standard set of commonly used conversions, especially useful in the UK where both **metric** and **imperial** units are often referenced.

#### **🥄 Spoons to Millilitres (ml)**

| Unit | Metric Equivalent |
| ----- | ----- |
| 1 teaspoon (tsp) | 5 ml |
| 1 tablespoon (tbsp) | 15 ml |
| 1 dessert spoon | 10 ml |

Note: UK tablespoon \= 15 ml (not 20 ml like in Australia)

---

#### **⚖️ Spoons to Grams (Approximate, varies by ingredient)**

| Ingredient | 1 tsp (g) | 1 tbsp (g) |
| ----- | ----- | ----- |
| Water/Milk | 5 g | 15 g |
| Butter | 5 g | 15 g |
| Sugar (granulated) | 4 g | 12 g |
| Salt | 6 g | 18 g |
| Flour (plain) | 3 g | 9 g |
| Honey | 7 g | 21 g |
| Oil (vegetable) | 4.5 g | 13.5 g |

---

#### **📦 Volume to Weight (approximate)**

| Unit | Grams (varies by ingredient) |
| ----- | ----- |
| 1 cup flour | \~120 g |
| 1 cup sugar | \~200 g |
| 1 cup butter | \~227 g |
| 1 cup milk | \~240 g |

---

#### **📏 Length and Temperature**

| Unit | UK Metric Equivalent |
| ----- | ----- |
| 1 inch | 2.54 cm |
| 1 lb (pound) | 454 g |
| 1 oz (ounce) | 28.35 g |
| 1 pint (UK) | 568 ml |
| Oven Gas Mark 4 | \~180°C |

---

Let me know if you need a downloadable table or specific conversions for a particular ingredient or domain (e.g., baby food, baking, etc.).

# Next Phase (Not part of current Scope)

### **Menu Engineering & Planning**  (Integrated Module \- Future Phase)

1.  **Create & Manage Menus: Group recipes into named menus (e.g., "Summer Lunch Menu," "Weekly Cycle Menu 1").**

2. **Menu Costing & Nutrition: Aggregate costs and nutritional information for entire menus or average per day for cycle menus.**

3. **Cycle Menus: Support for creating rotating weekly/monthly menus.**

