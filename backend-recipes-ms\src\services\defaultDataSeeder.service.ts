// import { Category } from "../models/Category";
// import { FoodAttributes } from "../models/FoodAttributes";
// import { RecipeMeasure } from "../models/RecipeMeasure";
// import { Item } from "../models/Item";
// import { generateUniqueSlug } from "../helper/slugGenerator";
// import settingsService from "./settings.service";

// /**
//  * Smart Default Data Seeder Service
//  * - Runs on server startup
//  * - Checks for missing default values
//  * - Only inserts what's missing
//  * - Safe to run multiple times
//  */
// class DefaultDataSeederService {
//   /**
//    * Main function to seed all default data
//    */
//   async seedAllDefaults(): Promise<void> {
//     try {

//       await this.seedRecipeCategories();
//       await this.seedIngredientCategories();
//       await this.seedAllergens();
//       await this.seedDietaryAttributes();
//       await this.seedCuisineTypes();
//       await this.seedMeasurementUnitsPrivate();
//       await this.seedDefaultSettings();
//       await this.seedDefaultIcons();

//     } catch (error) {
//       console.error("❌ Error in Default Data Seeder:", error);
//       throw error;
//     }
//   }

//   /**
//    * Public method to seed only measurement units (for foreign key dependencies)
//    */
//   async seedMeasurementUnits(): Promise<void> {
//     await this.seedMeasurementUnitsPrivate();
//   }

//   /**
//    * Seed Recipe Categories
//    */
//   private async seedRecipeCategories(): Promise<void> {
//     const defaultRecipeCategories = [
//       { name: "Main Course", slug: "main-course" },
//       { name: "Appetizer", slug: "appetizer" },
//       { name: "Dessert", slug: "dessert" },
//       { name: "Beverage", slug: "beverage" },
//       { name: "Soup", slug: "soup" },
//       { name: "Salad", slug: "salad" },
//       { name: "Snack", slug: "snack" },
//       { name: "Breakfast", slug: "breakfast" },
//       // Additional recipe categories
//       { name: "Baked Goods", slug: "baked-goods" },
//       { name: "Sauces & Dressings", slug: "sauces-dressings" },
//       { name: "Side Dishes", slug: "side-dishes" },
//     ];

//     for (const category of defaultRecipeCategories) {
//       // Generate unique slug
//       const checkSlugExists = async (slug: string): Promise<boolean> => {
//         const existing = await Category.findOne({
//           where: {
//             category_slug: slug,
//             category_type: "recipe",
//             organization_id: null,
//           },
//         });
//         return !!existing;
//       };

//       const categorySlug = await generateUniqueSlug(
//         category.name,
//         checkSlugExists,
//         {
//           maxLength: 25,
//           separator: "-",
//           lowercase: true,
//         }
//       );

//       const exists = await Category.findOne({
//         where: {
//           category_name: category.name,
//           category_type: "recipe",
//           organization_id: null,
//           is_system_category: true,
//         },
//       });

//       if (!exists) {
//         await Category.create({
//           category_name: category.name,
//           category_slug: categorySlug,
//           category_type: "recipe",
//           category_icon: null,
//           category_status: "active",
//           organization_id: null,
//           is_system_category: true,
//           created_by: 1,
//           updated_by: 1,
//         });

//       } else {

//       }
//     }
//   }

//   /**
//    * Seed Ingredient Categories
//    */
//   private async seedIngredientCategories(): Promise<void> {
//     const defaultIngredientCategories = [
//       { name: "Dairy", slug: "dairy" },
//       { name: "Meat", slug: "meat" },
//       { name: "Poultry", slug: "poultry" },
//       { name: "Seafood", slug: "seafood" },
//       { name: "Vegetables", slug: "vegetables" },
//       { name: "Fruits", slug: "fruits" },
//       { name: "Grains", slug: "grains" },
//       { name: "Nuts", slug: "nuts" },
//       { name: "Herbs & Spices", slug: "herbs-spices" },
//       { name: "Oils", slug: "oils" },
//       { name: "Condiments", slug: "condiments" },
//       { name: "Baking", slug: "baking" },
//       { name: "Dry Goods", slug: "dry-goods" },
//       { name: "Beverages", slug: "beverages" },
//     ];

//     for (const category of defaultIngredientCategories) {
//       // Generate unique slug
//       const checkSlugExists = async (slug: string): Promise<boolean> => {
//         const existing = await Category.findOne({
//           where: {
//             category_slug: slug,
//             category_type: "ingredient",
//             organization_id: null,
//           },
//         });
//         return !!existing;
//       };

//       const categorySlug = await generateUniqueSlug(
//         category.name,
//         checkSlugExists,
//         {
//           maxLength: 25,
//           separator: "-",
//           lowercase: true,
//         }
//       );

//       const exists = await Category.findOne({
//         where: {
//           category_name: category.name,
//           category_type: "ingredient",
//           organization_id: null,
//           is_system_category: true,
//         },
//       });

//       if (!exists) {
//         await Category.create({
//           category_name: category.name,
//           category_slug: categorySlug,
//           category_type: "ingredient",
//           category_icon: null,
//           category_status: "active",
//           organization_id: null,
//           is_system_category: true,
//           created_by: 1,
//           updated_by: 1,
//         });

//       } else {

//       }
//     }
//   }

//   /**
//    * Seed Allergens (14 Major Allergens)
//    */
//   private async seedAllergens(): Promise<void> {
//     const defaultAllergens = [
//       {
//         name: "Gluten",
//         description:
//           "A protein found in wheat, barley, rye, and other grains that can cause allergic reactions in sensitive individuals.",
//       },
//       {
//         name: "Crustaceans",
//         description:
//           "Shellfish including crab, lobster, shrimp, and prawns that are common allergens.",
//       },
//       {
//         name: "Eggs",
//         description:
//           "Chicken eggs and egg products that can cause allergic reactions, especially in children.",
//       },
//       {
//         name: "Fish",
//         description:
//           "All types of fish including salmon, tuna, cod, and other seafood that can trigger allergies.",
//       },
//       {
//         name: "Peanuts",
//         description:
//           "Ground nuts that are one of the most common and severe food allergens.",
//       },
//       {
//         name: "Soy",
//         description:
//           "Soybeans and soy products including tofu, soy sauce, and soy milk.",
//       },
//       {
//         name: "Milk",
//         description:
//           "Dairy products including cow's milk, cheese, butter, and other dairy-based ingredients.",
//       },
//       {
//         name: "Tree Nuts",
//         description:
//           "Nuts that grow on trees including almonds, walnuts, cashews, pistachios, and hazelnuts.",
//       },
//       {
//         name: "Celery",
//         description:
//           "Celery stalks, leaves, seeds, and celery-based seasonings that can cause allergic reactions.",
//       },
//       {
//         name: "Mustard",
//         description:
//           "Mustard seeds, mustard powder, and prepared mustard products.",
//       },
//       {
//         name: "Sesame",
//         description:
//           "Sesame seeds and sesame oil commonly used in cooking and baking.",
//       },
//       {
//         name: "Sulphites",
//         description:
//           "Chemical preservatives used in wine, dried fruits, and processed foods that can trigger reactions.",
//       },
//       {
//         name: "Lupin",
//         description:
//           "Lupin beans and flour used in some baked goods and pasta products.",
//       },
//       {
//         name: "Molluscs",
//         description:
//           "Soft-bodied shellfish including mussels, clams, oysters, and squid.",
//       },
//     ];

//     for (const allergen of defaultAllergens) {
//       // Generate unique slug
//       const checkSlugExists = async (slug: string): Promise<boolean> => {
//         const existing = await FoodAttributes.findOne({
//           where: {
//             attribute_slug: slug,
//             attribute_type: "allergen",
//             organization_id: null,
//           },
//         });
//         return !!existing;
//       };

//       const allergenSlug = await generateUniqueSlug(
//         allergen.name,
//         checkSlugExists,
//         {
//           maxLength: 25,
//           separator: "-",
//           lowercase: true,
//         }
//       );

//       const exists = await FoodAttributes.findOne({
//         where: {
//           attribute_title: allergen.name,
//           attribute_type: "allergen",
//           organization_id: null,
//           is_system_attribute: true,
//         },
//       });

//       if (!exists) {
//         await FoodAttributes.create({
//           attribute_title: allergen.name,
//           attribute_slug: allergenSlug,
//           attribute_description: allergen.description,
//           attribute_type: "allergen",
//           attribute_icon: null,
//           attribute_status: "active",
//           organization_id: null,
//           is_system_attribute: true,
//           created_by: 1,
//           updated_by: 1,
//         });

//       } else {

//       }
//     }
//   }

//   /**
//    * Seed Dietary Attributes
//    */
//   private async seedDietaryAttributes(): Promise<void> {
//     const defaultDietaryAttributes = [
//       {
//         name: "Vegan",
//         description:
//           "Plant-based diet that excludes all animal products including meat, dairy, eggs, and honey.",
//       },
//       {
//         name: "Vegetarian",
//         description:
//           "Diet that excludes meat and fish but may include dairy products and eggs.",
//       },
//       {
//         name: "Gluten-Free",
//         description:
//           "Diet that excludes gluten, a protein found in wheat, barley, rye, and other grains.",
//       },
//       {
//         name: "Halal",
//         description:
//           "Food prepared according to Islamic dietary laws and regulations.",
//       },
//       {
//         name: "Kosher",
//         description:
//           "Food prepared according to Jewish dietary laws and regulations.",
//       },
//       {
//         name: "Dairy-Free",
//         description:
//           "Diet that excludes all dairy products including milk, cheese, butter, and yogurt.",
//       },
//       {
//         name: "Nut-Free",
//         description:
//           "Diet that excludes all tree nuts and peanuts to prevent allergic reactions.",
//       },
//       {
//         name: "Low-Carb",
//         description:
//           "Diet that restricts carbohydrate intake, typically focusing on proteins and fats.",
//       },
//       {
//         name: "Keto",
//         description:
//           "Very low-carb, high-fat diet that puts the body into a metabolic state called ketosis.",
//       },
//       {
//         name: "Paleo",
//         description:
//           "Diet based on foods presumed to be available to Paleolithic humans, excluding processed foods.",
//       },
//     ];

//     for (const dietary of defaultDietaryAttributes) {
//       // Generate unique slug
//       const checkSlugExists = async (slug: string): Promise<boolean> => {
//         const existing = await FoodAttributes.findOne({
//           where: {
//             attribute_slug: slug,
//             attribute_type: "dietary",
//             organization_id: null,
//           },
//         });
//         return !!existing;
//       };

//       const dietarySlug = await generateUniqueSlug(
//         dietary.name,
//         checkSlugExists,
//         {
//           maxLength: 25,
//           separator: "-",
//           lowercase: true,
//         }
//       );

//       const exists = await FoodAttributes.findOne({
//         where: {
//           attribute_title: dietary.name,
//           attribute_type: "dietary",
//           organization_id: null,
//           is_system_attribute: true,
//         },
//       });

//       if (!exists) {
//         await FoodAttributes.create({
//           attribute_title: dietary.name,
//           attribute_slug: dietarySlug,
//           attribute_description: dietary.description,
//           attribute_type: "dietary",
//           attribute_icon: null,
//           attribute_status: "active",
//           organization_id: null,
//           is_system_attribute: true,
//           created_by: 1,
//           updated_by: 1,
//         });

//       } else {

//       }
//     }
//   }

//   /**
//    * Seed Cuisine Types
//    */
//   private async seedCuisineTypes(): Promise<void> {
//     const defaultCuisineTypes = [
//       {
//         name: "Italian",
//         description:
//           "Traditional cuisine from Italy featuring pasta, pizza, risotto, and Mediterranean ingredients.",
//       },
//       {
//         name: "Mexican",
//         description:
//           "Vibrant cuisine from Mexico with bold flavors, spices, and ingredients like chili, lime, and cilantro.",
//       },
//       {
//         name: "Chinese",
//         description:
//           "Diverse cuisine from China with regional variations, featuring stir-fries, dumplings, and balanced flavors.",
//       },
//       {
//         name: "Indian",
//         description:
//           "Rich and aromatic cuisine from India with complex spice blends, curries, and diverse regional dishes.",
//       },
//       {
//         name: "French",
//         description:
//           "Classic European cuisine known for refined techniques, sauces, and elegant presentation.",
//       },
//       {
//         name: "Japanese",
//         description:
//           "Traditional cuisine from Japan emphasizing fresh ingredients, simplicity, and seasonal flavors.",
//       },
//       {
//         name: "Thai",
//         description:
//           "Southeast Asian cuisine balancing sweet, sour, salty, and spicy flavors with fresh herbs.",
//       },
//       {
//         name: "Mediterranean",
//         description:
//           "Healthy cuisine from the Mediterranean region featuring olive oil, fresh vegetables, and seafood.",
//       },
//       {
//         name: "American",
//         description:
//           "Diverse cuisine from the United States with regional specialties and fusion influences.",
//       },
//       {
//         name: "British",
//         description:
//           "Traditional cuisine from Britain featuring hearty dishes, roasts, and classic comfort foods.",
//       },
//     ];

//     for (const cuisine of defaultCuisineTypes) {
//       // Generate unique slug
//       const checkSlugExists = async (slug: string): Promise<boolean> => {
//         const existing = await FoodAttributes.findOne({
//           where: {
//             attribute_slug: slug,
//             attribute_type: "cuisine",
//             organization_id: null,
//           },
//         });
//         return !!existing;
//       };

//       const cuisineSlug = await generateUniqueSlug(
//         cuisine.name,
//         checkSlugExists,
//         {
//           maxLength: 25,
//           separator: "-",
//           lowercase: true,
//         }
//       );

//       const exists = await FoodAttributes.findOne({
//         where: {
//           attribute_title: cuisine.name,
//           attribute_type: "cuisine",
//           organization_id: null,
//           is_system_attribute: true,
//         },
//       });

//       if (!exists) {
//         await FoodAttributes.create({
//           attribute_title: cuisine.name,
//           attribute_slug: cuisineSlug,
//           attribute_description: cuisine.description,
//           attribute_type: "cuisine",
//           attribute_icon: null,
//           attribute_status: "active",
//           organization_id: null,
//           is_system_attribute: true,
//           created_by: 1,
//           updated_by: 1,
//         });

//       } else {

//       }
//     }
//   }

//   /**
//    * Seed Measurement Units (Private Implementation)
//    */
//   private async seedMeasurementUnitsPrivate(): Promise<void> {
//     const defaultMeasurementUnits = [
//       { name: "Gram", slug: "gram" },
//       { name: "Kilogram", slug: "kilogram" },
//       { name: "Milliliter", slug: "milliliter" },
//       { name: "Liter", slug: "liter" },
//       { name: "Unit", slug: "unit" },
//       { name: "Ounce", slug: "ounce" },
//       { name: "Pound", slug: "pound" },
//       { name: "Tablespoon", slug: "tablespoon" },
//       { name: "Teaspoon", slug: "teaspoon" },
//       { name: "Cup", slug: "cup" },
//       { name: "Pint", slug: "pint" },
//       { name: "Quart", slug: "quart" },
//       { name: "Gallon", slug: "gallon" },
//     ];

//     for (const unit of defaultMeasurementUnits) {
//       // Generate unique slug
//       const checkSlugExists = async (slug: string): Promise<boolean> => {
//         const existing = await RecipeMeasure.findOne({
//           where: {
//             unit_slug: slug,
//             organization_id: null,
//           },
//         });
//         return !!existing;
//       };

//       const unitSlug = await generateUniqueSlug(unit.name, checkSlugExists, {
//         maxLength: 25,
//         separator: "-",
//         lowercase: true,
//       });

//       const exists = await RecipeMeasure.findOne({
//         where: {
//           unit_title: unit.name,
//           organization_id: null,
//           is_system_unit: true,
//         },
//       });

//       if (!exists) {
//         await RecipeMeasure.create({
//           unit_title: unit.name,
//           unit_slug: unitSlug,
//           unit_icon: null,
//           status: "active",
//           organization_id: null,
//           is_system_unit: true,
//           created_by: 1,
//           updated_by: 1,
//         });

//       } else {

//       }
//     }
//   }

//   /**
//    * Seed Default Settings for System
//    */
//   private async seedDefaultSettings(): Promise<void> {

//     try {
//       // Initialize default settings for system (no organization)
//       // This will create system-wide default settings
//       const systemUserId = 1; // System user ID

//       // Check if recipe settings already exist
//       const existingSettings = await settingsService.getSetting(
//         "recipe.highlight_changes"
//       );

//       if (existingSettings === null) {

//         // Initialize only recipe settings (minimal implementation)
//         const systemDefaults = {
//           "recipe.highlight_changes": false,
//           "recipe.public_store_enabled": false,
//           "recipe.public_cta_type": "contact_form",
//           "recipe.public_display_category": true,
//           "recipe.public_display_ingredients": true,
//           "recipe.public_display_nutritional_information": true,
//           "recipe.public_display_allergen_information": true,
//           "recipe.public_display_total_time": true,
//           "recipe.public_display_yield_portioning": true,
//           "recipe.public_display_cost": false,
//           "recipe.public_display_dietary_suitability": true,
//           "recipe.public_display_cuisine_type": false,
//           "recipe.public_display_preparation_steps": true,
//           "recipe.public_display_media": false,
//           "recipe.public_display_links": false,
//           "recipe.public_display_scale": false,
//           "recipe.public_display_serve_in": false,
//           "recipe.public_display_garnish": false,
//         };

//         await settingsService.updateSettings(
//           systemDefaults,
//           undefined,
//           systemUserId
//         );

//       } else {

//       }
//     } catch (error) {
//       console.error("   ❌ Error seeding default settings:", error);
//       // Don't throw error to prevent breaking the entire seeder
//     }
//   }

//   /**
//    * Seed Default Icons for Categories, Attributes, and Units
//    * Note: This method skips icon creation as icons should be seeded using actual image files
//    * Use the RecipeCategoryIconSeederService for proper icon seeding with real files
//    */
//   private async seedDefaultIcons(): Promise<void> {

//     try {
//       // Skip recipe category icon creation - will be handled by RecipeCategoryIconSeederService

//       // Skip ingredient category and measurement unit icon creation

//     } catch (error) {
//       console.error("❌ Error seeding default icons:", error);
//       // Don't throw error to prevent breaking the entire seeder
//     }
//   }
// }

// export default new DefaultDataSeederService();
