import express, { Express } from "express";
import dotenv from "dotenv";
import bodyParser from "body-parser";
import i18n from "./helper/i18n";
import cors from "cors";
import http from "http";
import path from "path";

import session from 'express-session';

dotenv.config();
const env = process.env.NEXT_NODE_ENV || "development";

import config from "../shared/config/config.json";
import dbConfig from "../shared/config/db.json";

global.config = JSON.parse(JSON.stringify(config))[env];
global.db = JSON.parse(JSON.stringify(dbConfig))[env];

import HandleErrorMessage from "./middleware/validatorMessage";
import { keycloak, memoryStore } from '../src/keycloak/keycloak'
import userAuth from "./middleware/auth";
import swaggerUi from 'swagger-ui-express';
import { swaggerSpec } from './swagger/swagger-config';
import { setupConsumers } from "./rabbitmq/consumerQueue";
import { db } from "./models/index";
import secureDocs from "./middleware/docSecurity";

db.sequelize
  .sync({ alter: true })
  .then(() => {
    console.log("re-sync db.");
  })
  .catch((error: Error) => {
    console.log("DB Error", error);
    throw error;
  });


const app: Express = express();
const router = express.Router();

app.use(cors());
router.use(bodyParser.urlencoded({ extended: true }));
router.use(bodyParser.json());

router.use(i18n.init);

app.use((req: any, res: any, next: any) => {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.header(
    "Access-Control-Expose-Headers",
    "Download-File,Set-Cookie,set-cookie, secure_session",
  );
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization,Set-Cookie,Platform-Type ,Ip-Address ,Address,Location",
  );
  next();
});

// use route for files 
// router.all("/files/*", secureDocs);
router.use("/uploads", express.static(__dirname + "/uploads"));
router.use("/uploads", express.static(path.join(__dirname, "uploads")));


/** Use routers */
import { publicRoutes, privateRoutes } from "./routes/index";

// Apply middleware
app.use(
  session({
    secret: global.config.KEYCLOAK_MY_SECRET_KEY,
    resave: false,
    saveUninitialized: true,
    store: memoryStore,
  })
);
app.use(keycloak.middleware());
router.all("/v1/private/*", userAuth);
router.use("/v1/public", publicRoutes);
router.use("/v1/private", privateRoutes);

// Add middleware to set response header for all routes
app.use((req, res, next) => {
  res.setHeader('App-Version', 'OK');
  next();
});

app.use(router);
router.use(HandleErrorMessage);
const server = http.createServer(app);

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
console.log('Swagger docs available at https://immune-needlessly-porpoise.ngrok-free.app/api-docs');

// Initialize database and RabbitMQ consumers
const initializeApp = async () => {
  try {
    /** Listen all queue from subscription-ms */
    await setupConsumers();

    server.listen(global.config.PORT, () => {
      console.log(`Server is started on`, global.config.PORT);
    });
  } catch (error) {
    console.error("Error during application initialization:", error);
    process.exit(1); // Exit the process if initialization fails
  }
};

// Start application initialization
initializeApp();

export default router;
