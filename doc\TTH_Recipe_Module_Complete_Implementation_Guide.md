# 🍽️ TTH Recipe Management Module - Complete Implementation Guide

## 📋 **COMPREHENSIVE TABLE OF CONTENTS**

### **PART I: PROJECT OVERVIEW & ARCHITECTURE**
1. [Module Overview & Requirements](#module-overview--requirements)
2. [TTH Integration Architecture](#tth-integration-architecture)
3. [Database Models & Relationships](#database-models--relationships)

### **PART II: IMPLEMENTATION COMPONENTS**
4. [Controller Implementation](#controller-implementation)
5. [Route Definitions](#route-definitions)
6. [Validation Schemas](#validation-schemas)
7. [Helper Functions](#helper-functions)

### **PART III: API SPECIFICATION**
8. [API Endpoints Documentation](#api-endpoints-documentation)
9. [Business Logic Implementation](#business-logic-implementation)
10. [Testing & Deployment](#testing--deployment)

---

## 🎯 **Module Overview & Requirements**

### **Recipe Module Purpose:**
The **TTH Recipe Management Module** is a comprehensive food service management system designed to handle ingredient management, recipe creation, nutritional analysis, cost tracking, and public recipe sharing. It follows TTH's established patterns for multi-tenant architecture, role-based access control, and enterprise-grade functionality.

### **Key Features Required:**
- ✅ **Ingredient Management:** Complete ingredient database with nutritional data
- ✅ **Recipe Creation:** Advanced recipe builder with yield calculations
- ✅ **Cost Analysis:** Real-time ingredient costing and profit margins
- ✅ **Allergen Tracking:** Comprehensive allergen management system
- ✅ **Version Control:** Full recipe versioning with change history
- ✅ **Public Publishing:** Secure recipe sharing with analytics
- ✅ **Nutritional Analysis:** Automated nutritional calculations
- ✅ **Multi-tenant Support:** Organization and branch isolation
- ✅ **Role-based Access:** Granular permissions following TTH patterns

### **TTH Integration Points:**
- 🔗 **User Management:** Uses existing `nv_users`, `nv_roles`, `nv_permissions`
- 🔗 **Organization Structure:** Integrates with `nv_organizations`, `nv_branches`, `nv_departments`
- 🔗 **Activity Logging:** Uses TTH's `nv_activities` for audit trails
- 🔗 **Notification System:** Integrates with TTH's notification infrastructure
- 🔗 **File Management:** Uses TTH's media upload and security system
- 🔗 **Email Templates:** Follows TTH's email template patterns

---

## 🏗️ **TTH Integration Architecture**

### **Following TTH Patterns:**

#### **1. Database Naming Convention:**
- **Table Prefix:** `recipe_` (following TTH pattern like `nv_`)
- **Field Naming:** snake_case following TTH standards
- **Audit Fields:** Standard TTH audit fields in all tables
- **Multi-tenant:** `organization_id` in all relevant tables

#### **2. Controller Structure:**
- **File Naming:** `recipe.controller.ts`, `ingredient.controller.ts`
- **Function Patterns:** Following TTH's CRUD patterns
- **Response Format:** Standard TTH response structure
- **Error Handling:** TTH's internationalized error messages

#### **3. Route Organization:**
- **Private Routes:** `routes/private/recipe.routes.ts`
- **Validation:** Joi validation following TTH patterns
- **Middleware:** TTH's authentication and permission middleware
- **File Structure:** Following TTH's route organization

#### **4. Permission Integration:**
```typescript
// Recipe module permissions following TTH pattern
const RECIPE_PERMISSIONS = {
  recipe: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'APPROVE', 'PUBLISH'],
  ingredient: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
  category: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
  allergen: ['CREATE', 'READ', 'UPDATE', 'DELETE'],
  supplier: ['CREATE', 'READ', 'UPDATE', 'DELETE']
};
```

---

## 🗄️ **DATABASE MODELS & RELATIONSHIPS**

### **1. 🏢 Core System Integration**

#### **Existing TTH Models Used:**
```sql
-- Core TTH tables that Recipe module integrates with:
-- nv_organizations (organization_id references)
-- nv_branches (branch_id references)
-- nv_departments (department_id references)
-- nv_users (created_by, updated_by references)
-- nv_roles (role-based access control)
-- nv_permissions (module permissions)
-- nv_activities (audit logging)
-- nv_media (file uploads)
```

### **2. 🏷️ Category Management Models**

#### **Recipe Categories Table (`recipe_categories`)**
```sql
CREATE TABLE recipe_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category_name VARCHAR(100) NOT NULL,
  category_slug VARCHAR(100) NOT NULL,
  category_icon VARCHAR(100) NULL,
  category_status ENUM('active', 'inactive', 'deleted') NOT NULL DEFAULT 'active',
  organization_id VARCHAR(100) NULL,
  category_type ENUM('recipe', 'ingredient') NOT NULL,
  is_system_category BOOLEAN NOT NULL DEFAULT FALSE,

  -- Standard TTH Audit Fields
  created_by INT NOT NULL,
  updated_by INT NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NOT NULL,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_category_slug_per_org_type (category_slug, organization_id, category_type),
  INDEX idx_category_status (category_status),
  INDEX idx_category_type (category_type),
  INDEX idx_organization (organization_id),
  INDEX idx_is_system_category (is_system_category)
);
```

**Field Explanations:**
- `category_slug`: URL-friendly version of category name for routing
- `category_status`: active/inactive/deleted status management
- `category_type`: recipe or ingredient categories
- `is_system_category`: System categories cannot be deleted
- `organization_id`: NULL for system categories, specific for organization categories

### **3. 🥗 Allergen Management Models**

#### **Allergens Table (`recipe_allergens`)**
```sql
CREATE TABLE recipe_allergens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  allergen_name VARCHAR(100) NOT NULL,
  allergen_code VARCHAR(20) NOT NULL,
  allergen_description TEXT,

  -- Allergen Type
  allergen_type ENUM('SYSTEM', 'ORGANIZATION') NOT NULL DEFAULT 'SYSTEM',
  alternative_names JSON, -- Array of alternative names

  -- Status
  is_active BOOLEAN DEFAULT TRUE,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NULL, -- NULL for system allergens

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_allergen_per_org (allergen_name, organization_id),
  UNIQUE KEY unique_code_per_org (allergen_code, organization_id),
  INDEX idx_allergen_type (allergen_type),
  INDEX idx_organization (organization_id)
);
```

**Field Explanations:**
- `allergen_type`: SYSTEM (14 default allergens, non-deletable) vs ORGANIZATION (custom)
- `alternative_names`: JSON array for regional variations
- `organization_id`: NULL for system allergens, specific for custom ones

### **4. 🥕 Nutrition Data Models**

#### **Nutrition Data Table (`recipe_nutrition_data`)**
```sql
CREATE TABLE recipe_nutrition_data (
  id INT PRIMARY KEY AUTO_INCREMENT,

  -- Macronutrients (per 100g)
  calories DECIMAL(8,2) DEFAULT 0.00,
  protein DECIMAL(8,2) DEFAULT 0.00,
  carbohydrates DECIMAL(8,2) DEFAULT 0.00,
  fat DECIMAL(8,2) DEFAULT 0.00,
  fiber DECIMAL(8,2) DEFAULT 0.00,
  sugar DECIMAL(8,2) DEFAULT 0.00,
  sodium DECIMAL(8,2) DEFAULT 0.00,

  -- Vitamins (per 100g)
  vitamin_a DECIMAL(8,2) DEFAULT 0.00,
  vitamin_c DECIMAL(8,2) DEFAULT 0.00,
  vitamin_d DECIMAL(8,2) DEFAULT 0.00,
  vitamin_e DECIMAL(8,2) DEFAULT 0.00,
  vitamin_k DECIMAL(8,2) DEFAULT 0.00,

  -- Minerals (per 100g)
  calcium DECIMAL(8,2) DEFAULT 0.00,
  iron DECIMAL(8,2) DEFAULT 0.00,
  magnesium DECIMAL(8,2) DEFAULT 0.00,
  phosphorus DECIMAL(8,2) DEFAULT 0.00,
  potassium DECIMAL(8,2) DEFAULT 0.00,
  zinc DECIMAL(8,2) DEFAULT 0.00,

  -- Data Source & Verification
  data_source VARCHAR(100), -- USDA, Manual, Lab Analysis
  verified_by INT, -- FK to user who verified
  verified_at TIMESTAMP NULL,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),
  FOREIGN KEY (verified_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  INDEX idx_organization (organization_id),
  INDEX idx_data_source (data_source)
);
```

### **6. 🥕 Ingredient Management Models**

#### **Ingredients Table (`recipe_ingredients`)**
```sql
CREATE TABLE recipe_ingredients (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ingredient_code VARCHAR(50) NOT NULL, -- Auto-generated: ORG-CAT-SEQ
  ingredient_name VARCHAR(200) NOT NULL,
  ingredient_description TEXT,

  -- Classification
  category_id INT NOT NULL, -- FK to recipe_categories (INGREDIENT type)
  ingredient_type ENUM('RAW', 'PROCESSED', 'PREPARED', 'SPICE', 'LIQUID') DEFAULT 'RAW',

  -- Physical Properties
  default_unit ENUM('g', 'kg', 'ml', 'liter', 'unit', 'oz', 'lb', 'tbsp', 'tsp', 'cup') DEFAULT 'g',
  density DECIMAL(8,4) DEFAULT 1.0000, -- For volume to weight conversions
  shelf_life_days INT DEFAULT 30,
  storage_temperature ENUM('FROZEN', 'REFRIGERATED', 'ROOM_TEMP', 'DRY_STORAGE') DEFAULT 'ROOM_TEMP',

  -- Costing Information
  current_cost_per_unit DECIMAL(10,4) DEFAULT 0.0000,
  cost_currency VARCHAR(3) DEFAULT 'GBP',
  waste_percentage DECIMAL(5,2) DEFAULT 0.00, -- Expected waste %

  -- Nutritional Data Link
  nutrition_data_id INT NULL, -- FK to recipe_nutrition_data

  -- Status & Flags
  ingredient_status ENUM('ACTIVE', 'INACTIVE', 'DISCONTINUED') DEFAULT 'ACTIVE',
  is_organic BOOLEAN DEFAULT FALSE,
  is_halal BOOLEAN DEFAULT FALSE,
  is_kosher BOOLEAN DEFAULT FALSE,
  is_vegan BOOLEAN DEFAULT FALSE,
  is_vegetarian BOOLEAN DEFAULT FALSE,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (category_id) REFERENCES recipe_categories(id),
  FOREIGN KEY (nutrition_data_id) REFERENCES recipe_nutrition_data(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_ingredient_code_per_org (ingredient_code, organization_id),
  UNIQUE KEY unique_ingredient_name_per_org (ingredient_name, organization_id),
  INDEX idx_category (category_id),
  INDEX idx_ingredient_type (ingredient_type),
  INDEX idx_ingredient_status (ingredient_status),
  INDEX idx_organization (organization_id)
);
```

#### **Ingredient Allergens Junction Table (`recipe_ingredient_allergens`)**
```sql
CREATE TABLE recipe_ingredient_allergens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ingredient_id INT NOT NULL,
  allergen_id INT NOT NULL,

  -- Allergen Level
  allergen_level ENUM('CONTAINS', 'MAY_CONTAIN', 'TRACES') DEFAULT 'CONTAINS',
  notes TEXT,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (ingredient_id) REFERENCES recipe_ingredients(id) ON DELETE CASCADE,
  FOREIGN KEY (allergen_id) REFERENCES recipe_allergens(id),
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_ingredient_allergen (ingredient_id, allergen_id),
  INDEX idx_allergen_level (allergen_level),
  INDEX idx_organization (organization_id)
);
```

### **7. 📝 Recipe Management Models**

#### **Recipes Table (`recipe_recipes`)**
```sql
CREATE TABLE recipe_recipes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_code VARCHAR(50) NOT NULL, -- Auto-generated: ORG-RCP-SEQ
  recipe_name VARCHAR(200) NOT NULL,
  recipe_description TEXT,

  -- Classification
  category_id INT NOT NULL, -- FK to recipe_categories (RECIPE type)
  recipe_type ENUM('APPETIZER', 'MAIN_COURSE', 'DESSERT', 'BEVERAGE', 'SAUCE', 'SIDE_DISH') DEFAULT 'MAIN_COURSE',
  cuisine_type VARCHAR(100), -- Italian, Chinese, etc.
  difficulty_level ENUM('EASY', 'MEDIUM', 'HARD', 'EXPERT') DEFAULT 'MEDIUM',

  -- Yield & Portioning
  total_yield_quantity DECIMAL(10,2) NOT NULL,
  total_yield_unit VARCHAR(20) NOT NULL, -- kg, liters, portions
  number_of_portions INT NOT NULL,
  standard_portion_size DECIMAL(10,2) NOT NULL,
  portion_unit VARCHAR(20) NOT NULL, -- g, ml, piece

  -- Timing
  prep_time_minutes INT DEFAULT 0,
  cook_time_minutes INT DEFAULT 0,
  total_time_minutes INT GENERATED ALWAYS AS (prep_time_minutes + cook_time_minutes) STORED,

  -- Instructions
  method_instructions TEXT NOT NULL,
  chef_instructions TEXT, -- Special notes for chefs
  serving_instructions TEXT,

  -- Costing & Pricing
  calculated_cost_per_portion DECIMAL(10,4) DEFAULT 0.0000,
  suggested_selling_price DECIMAL(10,2) DEFAULT 0.00,
  target_food_cost_percentage DECIMAL(5,2) DEFAULT 30.00,

  -- Nutritional Summary (calculated from ingredients)
  calories_per_portion DECIMAL(8,2) DEFAULT 0.00,
  protein_per_portion DECIMAL(8,2) DEFAULT 0.00,
  carbs_per_portion DECIMAL(8,2) DEFAULT 0.00,
  fat_per_portion DECIMAL(8,2) DEFAULT 0.00,

  -- Version Control
  version_number DECIMAL(3,1) DEFAULT 1.0,
  parent_recipe_id INT NULL, -- For recipe variations
  is_latest_version BOOLEAN DEFAULT TRUE,

  -- Status & Publishing
  recipe_status ENUM('DRAFT', 'ACTIVE', 'INACTIVE', 'ARCHIVED') DEFAULT 'DRAFT',
  is_public BOOLEAN DEFAULT FALSE,
  public_url_slug VARCHAR(200) NULL,
  public_view_count INT DEFAULT 0,
  public_like_count INT DEFAULT 0,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,
  branch_id INT NULL, -- Optional branch-specific recipes

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (branch_id) REFERENCES nv_branches(id),
  FOREIGN KEY (category_id) REFERENCES recipe_categories(id),
  FOREIGN KEY (parent_recipe_id) REFERENCES recipe_recipes(id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_recipe_code_per_org (recipe_code, organization_id),
  UNIQUE KEY unique_public_url_slug (public_url_slug),
  INDEX idx_recipe_type (recipe_type),
  INDEX idx_recipe_status (recipe_status),
  INDEX idx_is_public (is_public),
  INDEX idx_organization (organization_id),
  INDEX idx_branch (branch_id),
  INDEX idx_category (category_id)
);
```

#### **Recipe Media Table (`recipe_media`)**
```sql
CREATE TABLE recipe_media (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,

  -- Media Information
  media_type ENUM('IMAGE', 'VIDEO', 'PDF', 'YOUTUBE_LINK') NOT NULL,
  media_category ENUM('MAIN_IMAGE', 'GALLERY', 'INSTRUCTION_STEP', 'DOCUMENT', 'VIDEO_TUTORIAL') NOT NULL,

  -- File Details (for uploaded files)
  file_name VARCHAR(255),
  file_path VARCHAR(500), -- Path in TTH media system
  file_size INT, -- Size in bytes
  mime_type VARCHAR(100),

  -- External Links (for YouTube, etc.)
  external_url TEXT, -- YouTube URL, external video links

  -- Media Metadata
  media_title VARCHAR(200),
  media_description TEXT,
  alt_text VARCHAR(200), -- For accessibility

  -- Instruction Step Association
  instruction_step_number INT NULL, -- Which step this media belongs to

  -- Display Properties
  display_order INT DEFAULT 1,
  is_featured BOOLEAN DEFAULT FALSE, -- Main recipe image
  is_active BOOLEAN DEFAULT TRUE,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (recipe_id) REFERENCES recipe_recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  INDEX idx_recipe_media (recipe_id, media_category),
  INDEX idx_media_type (media_type),
  INDEX idx_instruction_step (instruction_step_number),
  INDEX idx_display_order (display_order),
  INDEX idx_organization (organization_id)
);
```

**Field Explanations:**
- `media_type`: Type of media (IMAGE, VIDEO, PDF, YOUTUBE_LINK)
- `media_category`: Purpose of media (MAIN_IMAGE, GALLERY, INSTRUCTION_STEP, etc.)
- `file_path`: Integration with TTH's existing media upload system
- `instruction_step_number`: Links media to specific instruction steps
- `is_featured`: Marks the main recipe image

#### **Recipe Ingredients List Table (`recipe_recipe_ingredients`)**
```sql
CREATE TABLE recipe_recipe_ingredients (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,
  ingredient_id INT NOT NULL,

  -- Quantity & Measurement
  quantity DECIMAL(10,4) NOT NULL,
  unit VARCHAR(20) NOT NULL, -- g, kg, ml, liter, unit, etc.

  -- Recipe-specific Information
  preparation_notes TEXT, -- "diced", "room temperature", etc.
  ingredient_order INT DEFAULT 1, -- Order in recipe
  is_optional BOOLEAN DEFAULT FALSE,

  -- Cost Snapshot (at time of recipe creation)
  cost_per_unit_snapshot DECIMAL(10,4) DEFAULT 0.0000,
  total_ingredient_cost DECIMAL(10,4) GENERATED ALWAYS AS (quantity * cost_per_unit_snapshot) STORED,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (recipe_id) REFERENCES recipe_recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (ingredient_id) REFERENCES recipe_ingredients(id),
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_recipe_ingredient (recipe_id, ingredient_id),
  INDEX idx_ingredient_order (ingredient_order),
  INDEX idx_organization (organization_id)
);
```

### **8. 🔄 Version Control & History Models**

#### **Recipe Version History Table (`recipe_version_history`)**
```sql
CREATE TABLE recipe_version_history (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,
  version_number DECIMAL(3,1) NOT NULL,

  -- Change Information
  change_type ENUM('CREATED', 'UPDATED', 'INGREDIENTS_CHANGED', 'INSTRUCTIONS_CHANGED', 'COSTING_UPDATED') NOT NULL,
  change_description TEXT,
  change_summary JSON, -- Detailed change log

  -- Snapshot Data
  recipe_data_snapshot JSON, -- Full recipe data at this version

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (recipe_id) REFERENCES recipe_recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_recipe_version (recipe_id, version_number),
  INDEX idx_change_type (change_type),
  INDEX idx_organization (organization_id)
);
```

### **9. 📊 Analytics & Tracking Models**

#### **Recipe Analytics Table (`recipe_analytics`)**
```sql
CREATE TABLE recipe_analytics (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,

  -- Usage Analytics
  times_viewed INT DEFAULT 0,
  times_cooked INT DEFAULT 0,
  times_scaled INT DEFAULT 0,
  times_duplicated INT DEFAULT 0,

  -- Public Analytics (if published)
  public_views INT DEFAULT 0,
  public_likes INT DEFAULT 0,
  public_shares INT DEFAULT 0,

  -- Performance Metrics
  average_rating DECIMAL(3,2) DEFAULT 0.00,
  total_ratings INT DEFAULT 0,

  -- Cost Analytics
  cost_trend_percentage DECIMAL(5,2) DEFAULT 0.00, -- Cost change over time
  last_cost_calculation TIMESTAMP NULL,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (recipe_id) REFERENCES recipe_recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_recipe_analytics (recipe_id),
  INDEX idx_organization (organization_id)
);
```

### **10. 🔐 Permissions & Access Control Models**

#### **Recipe Permissions Table (`recipe_permissions`)**
```sql
CREATE TABLE recipe_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipe_id INT NOT NULL,
  user_id INT NOT NULL,

  -- Permission Types (following TTH pattern)
  can_view BOOLEAN DEFAULT TRUE,
  can_edit BOOLEAN DEFAULT FALSE,
  can_delete BOOLEAN DEFAULT FALSE,
  can_publish BOOLEAN DEFAULT FALSE,
  can_approve BOOLEAN DEFAULT FALSE,

  -- Permission Metadata
  granted_by INT NOT NULL, -- User who granted permission
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  permission_notes TEXT,

  -- Status
  is_active BOOLEAN DEFAULT TRUE,

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (recipe_id) REFERENCES recipe_recipes(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES nv_users(id),
  FOREIGN KEY (granted_by) REFERENCES nv_users(id),
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_recipe_user_permission (recipe_id, user_id),
  INDEX idx_user (user_id),
  INDEX idx_expires_at (expires_at),
  INDEX idx_organization (organization_id)
);
```

### **8. 🔧 Unit Conversion System Models**

#### **Unit Conversions Table (`recipe_unit_conversions`)**
```sql
CREATE TABLE recipe_unit_conversions (
  id INT PRIMARY KEY AUTO_INCREMENT,

  -- Conversion Information
  from_unit VARCHAR(20) NOT NULL,
  to_unit VARCHAR(20) NOT NULL,
  conversion_factor DECIMAL(12,6) NOT NULL, -- Multiply by this to convert

  -- Conversion Details
  unit_category ENUM('WEIGHT', 'VOLUME', 'COUNT') NOT NULL,
  description VARCHAR(200), -- e.g., "Grams to Kilograms"

  -- Status
  is_active BOOLEAN DEFAULT TRUE,
  is_system_default BOOLEAN DEFAULT TRUE, -- System conversions vs custom

  -- Multi-tenant (TTH Pattern)
  organization_id VARCHAR(50) NOT NULL,

  -- Standard TTH Audit Fields
  created_by INT,
  updated_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- Foreign Keys (TTH Pattern)
  FOREIGN KEY (organization_id) REFERENCES nv_organizations(organization_id),
  FOREIGN KEY (created_by) REFERENCES nv_users(id),
  FOREIGN KEY (updated_by) REFERENCES nv_users(id),

  -- Indexes (TTH Pattern)
  UNIQUE KEY unique_conversion_per_org (from_unit, to_unit, organization_id),
  INDEX idx_from_unit (from_unit),
  INDEX idx_to_unit (to_unit),
  INDEX idx_unit_category (unit_category),
  INDEX idx_organization (organization_id)
);
```

**Field Explanations:**
- `unit_category`: Groups conversions by type (WEIGHT, VOLUME, COUNT)
- `conversion_factor`: Direct multiplication factor for conversion
- `is_system_default`: Pre-populated conversions that can't be deleted

---

## 🎯 **DATABASE RELATIONSHIPS SUMMARY**

### **Key Relationships:**

1. **Organizations → All Tables:** Multi-tenant isolation
2. **Categories → Ingredients/Recipes:** Unified category system (simplified)
3. **Ingredients → Nutrition Data:** 1:1 relationship
4. **Ingredients → Allergens:** Many-to-many with levels
5. **Recipes → Ingredients:** Many-to-many with quantities
6. **Recipes → Media:** 1:many for images, videos, PDFs, YouTube links
7. **Recipes → Version History:** 1:many for change tracking
8. **Recipes → Analytics:** 1:1 for performance tracking
9. **Recipes → Permissions:** 1:many for access control
10. **Unit Conversions:** System-wide conversion factors

### **TTH Integration Points:**

- ✅ **nv_organizations:** Multi-tenant foundation
- ✅ **nv_users:** User management and audit trails
- ✅ **nv_roles/nv_permissions:** Role-based access control
- ✅ **nv_branches:** Branch-specific recipes
- ✅ **nv_activities:** Audit logging integration
- ✅ **nv_media:** File upload integration

### **Simplified Design Benefits:**

- ✅ **No Suppliers:** Removed unnecessary supplier management complexity
- ✅ **Flat Categories:** No parent/child hierarchy - simpler to manage
- ✅ **Simple Unit Conversions:** System-wide conversion table without ingredient-specific complexity
- ✅ **Focused Features:** Only essential recipe management functionality
- ✅ **TTH Patterns:** Follows established TTH architecture and naming conventions

---

## 🚀 **CONTROLLER IMPLEMENTATION**

### **Following TTH Controller Patterns:**

All controllers follow TTH's established patterns:
- **Standard Response Format:** `{ success: boolean, data?: any, message?: string }`
- **Error Handling:** TTH's internationalized error messages
- **Authentication:** TTH's JWT middleware
- **Permissions:** TTH's role-based access control
- **Audit Logging:** Integration with TTH's activity logging
- **Multi-tenant:** Organization-based data isolation

### **Controller Files Structure:**

```
src/controllers/
├── recipe/
│   ├── category.controller.ts
│   ├── allergen.controller.ts
│   ├── ingredient.controller.ts
│   ├── recipe.controller.ts
│   ├── analytics.controller.ts
│   └── import-export.controller.ts
```

---

## 🛣️ **API ENDPOINTS DOCUMENTATION**

### **Base URL:** `/api/recipe`

All endpoints follow TTH patterns:
- **Authentication Required:** All endpoints require valid JWT token
- **Organization Isolation:** All data is organization-specific
- **Permission Checks:** Role-based access control
- **Standard Responses:** Consistent response format

---

## 🏷️ **1. CATEGORY MANAGEMENT APIs**

### **GET /api/recipe/categories**
**Purpose:** Get all categories with filtering options

**Query Parameters:**
- `category_status` (optional): `active` | `inactive` | `deleted` (default: active)
- `category_type` (optional): `recipe` | `ingredient` (default: all)
- `category_name` (optional): Search by category name (partial match)
- `page` (optional): number (default: 1)
- `limit` (optional): number (default: 50)

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "category_name": "Vegetables",
        "category_slug": "vegetables",
        "category_icon": "leaf-icon",
        "category_status": "active",
        "organization_id": "org_123",
        "category_type": "ingredient",
        "is_system_category": true,
        "created_by": 1,
        "updated_by": 1,
        "created_at": "2024-12-20T10:30:00",
        "updated_at": "2024-12-20T10:30:00"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 1,
      "total_items": 5,
      "items_per_page": 50
    }
  }
}
```

### **POST /api/recipe/categories**
**Purpose:** Create new category

**Request Body:**
```json
{
  "category_name": "Organic Vegetables",
  "category_slug": "organic-vegetables",
  "category_icon": "eco-leaf",
  "category_type": "ingredient",
  "organization_id": "org_123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "category_name": "Organic Vegetables",
    "category_slug": "organic-vegetables",
    "category_icon": "eco-leaf",
    "category_status": "active",
    "organization_id": "org_123",
    "category_type": "ingredient",
    "is_system_category": false,
    "created_by": 1,
    "updated_by": 1,
    "created_at": "2024-12-20T10:30:00",
    "updated_at": "2024-12-20T10:30:00"
  }
}
```

### **GET /api/recipe/categories/{id}**
**Purpose:** Get single category by ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "category_name": "Vegetables",
    "category_slug": "vegetables",
    "category_icon": "leaf-icon",
    "category_status": "active",
    "organization_id": "org_123",
    "category_type": "ingredient",
    "is_system_category": true,
    "created_by": 1,
    "updated_by": 1,
    "created_at": "2024-12-20T10:30:00",
    "updated_at": "2024-12-20T10:30:00"
  }
}
```

### **PUT /api/recipe/categories/{id}**
**Purpose:** Update category (system categories cannot be updated)

**Request Body:**
```json
{
  "category_name": "Fresh Vegetables",
  "category_slug": "fresh-vegetables",
  "category_icon": "fresh-leaf",
  "category_status": "active"
}
```

### **DELETE /api/recipe/categories/{id}**
**Purpose:** Soft delete category (system categories cannot be deleted)

**Response:**
```json
{
  "success": true,
  "message": "Category deleted successfully"
}
```

### **POST /api/recipe/import/categories**
**Purpose:** Import categories from Excel/CSV file

**Request:** Multipart form data with file upload

**Required Headers:**
```
category_name, category_slug, category_icon, category_type, organization_id
```

**Response:**
```json
{
  "success": true,
  "data": {
    "imported_count": 15,
    "skipped_count": 2,
    "error_count": 1,
    "errors": [
      {
        "row": 8,
        "error": "Category slug already exists"
      }
    ]
  }
}
```

### **GET /api/recipe/export/categories**
**Purpose:** Export categories to Excel/CSV

**Query Parameters:**
- `format`: `EXCEL` | `CSV` (default: EXCEL)
- `category_type` (optional): Filter by type
- `category_status` (optional): Filter by status

**Response:** File download with appropriate headers

---

## 🥗 **2. ALLERGEN MANAGEMENT APIs**

### **GET /api/recipe/allergens**
**Purpose:** Get all allergens (system + organization-specific)

**Query Parameters:**
- `type` (optional): `SYSTEM` | `ORGANIZATION` | `ALL` (default: ALL)
- `active_only` (optional): boolean (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "allergens": [
      {
        "id": 1,
        "allergen_name": "Gluten",
        "allergen_code": "GLU",
        "allergen_description": "Contains gluten proteins",
        "allergen_type": "SYSTEM",
        "alternative_names": ["Wheat protein", "Gluten protein"],
        "is_active": true,
        "organization_id": null,
        "created_at": "2024-12-20T10:30:00Z"
      }
    ]
  }
}
```

### **POST /api/recipe/allergens**
**Purpose:** Create organization-specific allergen

**Request Body:**
```json
{
  "allergen_name": "Sesame Oil",
  "allergen_code": "SES-OIL",
  "allergen_description": "Contains sesame oil derivatives",
  "alternative_names": ["Sesame extract", "Sesame derivatives"]
}
```

### **PUT /api/recipe/allergens/{id}**
**Purpose:** Update allergen (system allergens cannot be updated)

### **DELETE /api/recipe/allergens/{id}**
**Purpose:** Soft delete allergen (system allergens cannot be deleted)

---

## 🥕 **3. INGREDIENT MANAGEMENT APIs**

### **GET /api/recipe/ingredients**
**Purpose:** Get all ingredients for organization

**Query Parameters:**
- `category_id` (optional): Filter by category
- `ingredient_type` (optional): Filter by type
- `status` (optional): Filter by status
- `search` (optional): Search in name/description
- `page` (optional): number (default: 1)
- `limit` (optional): number (default: 50)
- `include_nutrition` (optional): boolean (default: false)
- `include_allergens` (optional): boolean (default: false)

**Response:**
```json
{
  "success": true,
  "data": {
    "ingredients": [
      {
        "id": 1,
        "ingredient_code": "ORG-VEG-001",
        "ingredient_name": "Fresh Tomatoes",
        "ingredient_description": "Organic fresh tomatoes",
        "category": {
          "id": 1,
          "category_name": "Vegetables",
          "category_type": "INGREDIENT"
        },
        "ingredient_type": "RAW",
        "default_unit": "g",
        "current_cost_per_unit": 0.0025,
        "cost_currency": "GBP",
        "ingredient_status": "ACTIVE",
        "is_organic": true,
        "is_vegan": true,
        "nutrition": {
          "calories": 18.0,
          "protein": 0.9,
          "carbohydrates": 3.9,
          "fat": 0.2
        },
        "allergens": [
          {
            "allergen_name": "None",
            "allergen_level": "CONTAINS"
          }
        ],
        "created_at": "2024-12-20T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "total_items": 125,
      "items_per_page": 50
    }
  }
}
```

### **POST /api/recipe/ingredients**
**Purpose:** Create new ingredient

**Request Body:**
```json
{
  "ingredient_name": "Fresh Tomatoes",
  "ingredient_description": "Organic fresh tomatoes",
  "category_id": 1,
  "ingredient_type": "RAW",
  "default_unit": "g",
  "density": 1.0000,
  "shelf_life_days": 7,
  "storage_temperature": "REFRIGERATED",
  "current_cost_per_unit": 0.0025,
  "cost_currency": "GBP",
  "waste_percentage": 5.00,
  "is_organic": true,
  "is_vegan": true,
  "is_vegetarian": true
}
```

### **POST /api/recipe/ingredients/with-nutrition**
**Purpose:** Create ingredient with nutrition data in single transaction

**Request Body:**
```json
{
  "ingredient_data": {
    "ingredient_name": "Fresh Tomatoes",
    "ingredient_description": "Organic fresh tomatoes",
    "category_id": 1,
    "ingredient_type": "RAW",
    "default_unit": "g",
    "current_cost_per_unit": 0.0025,
    "is_organic": true,
    "is_vegan": true,
    "is_vegetarian": true
  },
  "nutrition_data": {
    "calories": 18.0,
    "protein": 0.9,
    "carbohydrates": 3.9,
    "fat": 0.2,
    "fiber": 1.2,
    "vitamin_c": 13.7,
    "calcium": 10.0,
    "iron": 0.3,
    "data_source": "USDA"
  },
  "allergen_ids": [1, 3] // Optional allergen associations
}
```

### **GET /api/recipe/ingredients/{id}**
**Purpose:** Get single ingredient with full details

### **PUT /api/recipe/ingredients/{id}**
**Purpose:** Update ingredient

### **DELETE /api/recipe/ingredients/{id}**
**Purpose:** Soft delete ingredient

### **POST /api/recipe/ingredients/{id}/allergens**
**Purpose:** Add allergens to ingredient

**Request Body:**
```json
{
  "allergen_associations": [
    {
      "allergen_id": 1,
      "allergen_level": "CONTAINS",
      "notes": "Contains gluten proteins"
    },
    {
      "allergen_id": 3,
      "allergen_level": "MAY_CONTAIN",
      "notes": "Processed in facility with nuts"
    }
  ]
}
```

### **PUT /api/recipe/ingredients/{id}/nutrition**
**Purpose:** Update or add nutrition data to ingredient

---

## 📝 **4. RECIPE MANAGEMENT APIs**

### **GET /api/recipe/recipes**
**Purpose:** Get all recipes for organization

**Query Parameters:**
- `category_id` (optional): Filter by category
- `recipe_type` (optional): Filter by type
- `status` (optional): Filter by status
- `search` (optional): Search in name/description
- `page` (optional): number (default: 1)
- `limit` (optional): number (default: 20)
- `include_ingredients` (optional): boolean (default: false)
- `include_nutrition` (optional): boolean (default: false)
- `include_costing` (optional): boolean (default: false)

**Response:**
```json
{
  "success": true,
  "data": {
    "recipes": [
      {
        "id": 1,
        "recipe_code": "ORG-RCP-001",
        "recipe_name": "Classic Margherita Pizza",
        "recipe_description": "Traditional Italian pizza with fresh ingredients",
        "category": {
          "id": 2,
          "category_name": "Main Courses",
          "category_type": "RECIPE"
        },
        "recipe_type": "MAIN_COURSE",
        "cuisine_type": "Italian",
        "difficulty_level": "MEDIUM",
        "total_yield_quantity": 4.0,
        "total_yield_unit": "portions",
        "number_of_portions": 4,
        "standard_portion_size": 1.0,
        "portion_unit": "piece",
        "prep_time_minutes": 30,
        "cook_time_minutes": 15,
        "total_time_minutes": 45,
        "calculated_cost_per_portion": 3.25,
        "suggested_selling_price": 12.00,
        "target_food_cost_percentage": 27.08,
        "calories_per_portion": 285.0,
        "protein_per_portion": 12.5,
        "carbs_per_portion": 35.2,
        "fat_per_portion": 8.7,
        "version_number": 1.0,
        "recipe_status": "ACTIVE",
        "is_public": false,
        "created_at": "2024-12-20T10:30:00Z",
        "updated_at": "2024-12-20T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 89,
      "items_per_page": 20
    }
  }
}
```

### **POST /api/recipe/recipes**
**Purpose:** Create new recipe with ingredients

**Request Body:**
```json
{
  "recipe_name": "Classic Margherita Pizza",
  "recipe_description": "Traditional Italian pizza with fresh ingredients",
  "category_id": 2,
  "recipe_type": "MAIN_COURSE",
  "cuisine_type": "Italian",
  "difficulty_level": "MEDIUM",
  "total_yield_quantity": 4.0,
  "total_yield_unit": "portions",
  "number_of_portions": 4,
  "standard_portion_size": 1.0,
  "portion_unit": "piece",
  "prep_time_minutes": 30,
  "cook_time_minutes": 15,
  "method_instructions": "1. Prepare dough...\n2. Add toppings...\n3. Bake at 450°F for 12-15 minutes",
  "chef_instructions": "Ensure oven is preheated to 450°F",
  "serving_instructions": "Serve immediately while hot",
  "suggested_selling_price": 12.00,
  "target_food_cost_percentage": 30.00,
  "ingredients": [
    {
      "ingredient_id": 1,
      "quantity": 500.0,
      "unit": "g",
      "preparation_notes": "room temperature",
      "ingredient_order": 1,
      "is_optional": false
    },
    {
      "ingredient_id": 2,
      "quantity": 200.0,
      "unit": "g",
      "preparation_notes": "grated",
      "ingredient_order": 2,
      "is_optional": false
    }
  ]
}
```

### **GET /api/recipe/recipes/{id}**
**Purpose:** Get single recipe with full details including ingredients, nutrition, and costing

**Query Parameters:**
- `include_ingredients` (optional): boolean (default: true)
- `include_nutrition` (optional): boolean (default: true)
- `include_costing` (optional): boolean (default: true)
- `include_allergens` (optional): boolean (default: true)

### **PUT /api/recipe/recipes/{id}**
**Purpose:** Update recipe (creates new version if significant changes)

### **DELETE /api/recipe/recipes/{id}**
**Purpose:** Soft delete recipe

### **POST /api/recipe/recipes/{id}/scale**
**Purpose:** Scale recipe up or down

**Request Body:**
```json
{
  "scale_type": "PORTIONS", // or "YIELD"
  "new_value": 8, // new number of portions or yield quantity
  "save_as_new": true, // whether to save as new recipe or version
  "new_recipe_name": "Large Margherita Pizza (8 portions)"
}
```

### **POST /api/recipe/recipes/{id}/duplicate**
**Purpose:** Duplicate recipe as starting point for variations

**Request Body:**
```json
{
  "new_recipe_name": "Margherita Pizza Variation",
  "new_recipe_description": "Modified version with extra herbs",
  "copy_ingredients": true,
  "copy_instructions": true
}
```

### **GET /api/recipe/recipes/{id}/versions**
**Purpose:** Get version history of recipe

### **POST /api/recipe/recipes/{id}/publish**
**Purpose:** Publish recipe publicly

**Request Body:**
```json
{
  "public_name": "Amazing Margherita Pizza",
  "public_description": "Our signature pizza recipe",
  "public_url_slug": "amazing-margherita-pizza",
  "include_nutrition": true,
  "include_allergens": true
}
```

### **POST /api/recipe/recipes/{id}/unpublish**
**Purpose:** Remove recipe from public access

### **POST /api/recipe/recipes/{id}/media**
**Purpose:** Upload media files to recipe (images, videos, PDFs, YouTube links)

**Request:** Multipart form data with file upload

**Supported Media Types:**
- **Images:** JPG, PNG, GIF, WebP (max 10MB each)
- **Videos:** MP4, MOV, AVI (max 100MB each)
- **Documents:** PDF (max 50MB each)
- **External Links:** YouTube URLs, Vimeo URLs

**Request Body (Multipart Form):**
```typescript
// For file uploads
{
  file: File, // The actual file
  media_type: 'IMAGE' | 'VIDEO' | 'PDF',
  media_category: 'MAIN_IMAGE' | 'GALLERY' | 'INSTRUCTION_STEP' | 'DOCUMENT' | 'VIDEO_TUTORIAL',
  media_title: string,
  media_description?: string,
  alt_text?: string, // For accessibility
  instruction_step_number?: number, // For step-specific media
  display_order?: number,
  is_featured?: boolean // Mark as main recipe image
}

// For external links (YouTube, etc.)
{
  media_type: 'YOUTUBE_LINK',
  media_category: 'VIDEO_TUTORIAL',
  external_url: string, // YouTube URL
  media_title: string,
  media_description?: string,
  instruction_step_number?: number,
  display_order?: number
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "recipe_id": 1,
    "media_type": "IMAGE",
    "media_category": "MAIN_IMAGE",
    "file_name": "margherita-pizza-main.jpg",
    "file_path": "/uploads/recipes/org_123/recipe_1/margherita-pizza-main.jpg",
    "file_size": 2048576,
    "mime_type": "image/jpeg",
    "media_title": "Classic Margherita Pizza",
    "alt_text": "Delicious margherita pizza with fresh basil",
    "display_order": 1,
    "is_featured": true,
    "created_at": "2024-12-20T10:30:00Z"
  }
}
```

### **GET /api/recipe/recipes/{id}/media**
**Purpose:** Get all media files for a recipe

**Query Parameters:**
- `media_type` (optional): Filter by type (IMAGE, VIDEO, PDF, YOUTUBE_LINK)
- `media_category` (optional): Filter by category
- `instruction_step` (optional): Filter by instruction step number

**Response:**
```json
{
  "success": true,
  "data": {
    "media": [
      {
        "id": 1,
        "media_type": "IMAGE",
        "media_category": "MAIN_IMAGE",
        "file_name": "margherita-pizza-main.jpg",
        "file_path": "/uploads/recipes/org_123/recipe_1/margherita-pizza-main.jpg",
        "media_title": "Classic Margherita Pizza",
        "is_featured": true,
        "display_order": 1
      },
      {
        "id": 2,
        "media_type": "IMAGE",
        "media_category": "INSTRUCTION_STEP",
        "file_name": "step-3-add-toppings.jpg",
        "instruction_step_number": 3,
        "media_title": "Adding Toppings",
        "display_order": 1
      },
      {
        "id": 3,
        "media_type": "YOUTUBE_LINK",
        "media_category": "VIDEO_TUTORIAL",
        "external_url": "https://youtube.com/watch?v=abc123",
        "media_title": "How to Make Perfect Pizza Dough",
        "display_order": 1
      }
    ],
    "grouped_by_category": {
      "MAIN_IMAGE": [...],
      "GALLERY": [...],
      "INSTRUCTION_STEP": [...],
      "VIDEO_TUTORIAL": [...],
      "DOCUMENT": [...]
    }
  }
}
```

### **PUT /api/recipe/recipes/{id}/media/{media_id}**
**Purpose:** Update media metadata (title, description, order, etc.)

**Request Body:**
```json
{
  "media_title": "Updated Pizza Image",
  "media_description": "Updated description",
  "alt_text": "Updated alt text",
  "display_order": 2,
  "is_featured": false,
  "instruction_step_number": 5
}
```

### **DELETE /api/recipe/recipes/{id}/media/{media_id}**
**Purpose:** Delete media file from recipe

### **POST /api/recipe/recipes/{id}/media/{media_id}/set-featured**
**Purpose:** Set media as featured/main image for recipe

### **POST /api/recipe/recipes/{id}/media/reorder**
**Purpose:** Reorder media files

**Request Body:**
```json
{
  "media_order": [
    { "media_id": 1, "display_order": 1 },
    { "media_id": 3, "display_order": 2 },
    { "media_id": 2, "display_order": 3 }
  ]
}
```

---

## 📊 **5. ANALYTICS & REPORTING APIs**

### **GET /api/recipe/analytics/dashboard**
**Purpose:** Get recipe module dashboard statistics

**Response:**
```json
{
  "success": true,
  "data": {
    "overview_stats": {
      "total_recipes": 150,
      "total_ingredients": 500,
      "total_allergens": 18,
      "total_categories": 12,
      "public_recipes": 25
    },
    "cost_analysis": {
      "average_cost_per_portion": 4.25,
      "highest_cost_recipe": {
        "id": 5,
        "recipe_name": "Premium Steak",
        "cost_per_portion": 25.50
      },
      "lowest_cost_recipe": {
        "id": 8,
        "recipe_name": "Simple Salad",
        "cost_per_portion": 1.25
      }
    },
    "popular_recipes": [
      {
        "id": 1,
        "recipe_name": "Classic Margherita",
        "times_viewed": 125,
        "times_cooked": 45
      }
    ]
  }
}
```

### **GET /api/recipe/analytics/recipes/{id}**
**Purpose:** Get detailed analytics for specific recipe

### **GET /api/recipe/analytics/ingredients/usage**
**Purpose:** Get ingredient usage analytics

### **GET /api/recipe/analytics/costs/trends**
**Purpose:** Get cost trend analysis

---

## 🔍 **6. SEARCH & FILTERING APIs**

### **GET /api/recipe/search**
**Purpose:** Global search across recipes and ingredients

**Query Parameters:**
- `q` (required): Search query
- `type` (optional): `RECIPES` | `INGREDIENTS` | `ALL` (default: ALL)
- `category_id` (optional): Filter by category
- `allergen_free` (optional): Comma-separated allergen IDs to exclude
- `dietary` (optional): `vegan` | `vegetarian` | `organic`
- `page` (optional): number (default: 1)
- `limit` (optional): number (default: 20)

**Response:**
```json
{
  "success": true,
  "data": {
    "recipes": [
      {
        "id": 1,
        "recipe_name": "Classic Margherita Pizza",
        "match_score": 0.95,
        "match_fields": ["name", "description"]
      }
    ],
    "ingredients": [
      {
        "id": 1,
        "ingredient_name": "Fresh Tomatoes",
        "match_score": 0.87,
        "match_fields": ["name"]
      }
    ],
    "total_results": 15
  }
}
```

---

## 📄 **7. IMPORT & EXPORT APIs**

### **POST /api/recipe/import/ingredients**
**Purpose:** Import ingredients from Excel/CSV file

**Request:** Multipart form data with file upload

**Supported Formats:**
- **Excel (.xlsx):** Multiple sheets supported
- **CSV (.csv):** Standard comma-separated format

**Required Headers:**
```
ingredient_name, category_name, ingredient_type, default_unit,
current_cost_per_unit, is_organic, is_vegan, is_vegetarian,
calories, protein, carbohydrates, fat, allergen_names
```

**Response:**
```json
{
  "success": true,
  "data": {
    "imported_count": 45,
    "skipped_count": 3,
    "error_count": 2,
    "errors": [
      {
        "row": 15,
        "error": "Category 'Unknown Category' not found"
      }
    ],
    "summary": {
      "total_processed": 50,
      "success_rate": 90.0
    }
  }
}
```

### **POST /api/recipe/import/recipes**
**Purpose:** Import recipes from Excel/CSV file

**Request:** Multipart form data with file upload

**Supported Formats:**
- **Excel (.xlsx):** Recipes sheet + Ingredients sheet
- **JSON (.json):** Structured recipe format

**Response:**
```json
{
  "success": true,
  "data": {
    "imported_count": 12,
    "skipped_count": 1,
    "error_count": 0,
    "summary": {
      "total_processed": 13,
      "success_rate": 92.3
    }
  }
}
```

### **GET /api/recipe/export/ingredients**
**Purpose:** Export ingredients to Excel/CSV

**Query Parameters:**
- `format`: `EXCEL` | `CSV` (default: EXCEL)
- `category_id` (optional): Filter by category
- `include_nutrition` (optional): boolean (default: true)
- `include_allergens` (optional): boolean (default: true)
- `include_costing` (optional): boolean (default: true)

**Response:** File download with appropriate headers

### **GET /api/recipe/export/recipes**
**Purpose:** Export recipes to Excel/PDF

**Query Parameters:**
- `format`: `EXCEL` | `PDF` | `JSON` (default: EXCEL)
- `recipe_ids` (optional): Comma-separated recipe IDs
- `category_id` (optional): Filter by category
- `include_ingredients` (optional): boolean (default: true)
- `include_nutrition` (optional): boolean (default: true)
- `include_costing` (optional): boolean (default: false)

**Response:** File download with appropriate headers

### **GET /api/recipe/export/templates**
**Purpose:** Download import templates

**Query Parameters:**
- `type`: `INGREDIENTS` | `RECIPES`
- `format`: `EXCEL` | `CSV`

**Response:** Template file download

### **POST /api/recipe/import/validate**
**Purpose:** Validate import file before actual import

**Request:** Multipart form data with file upload

**Response:**
```json
{
  "success": true,
  "data": {
    "is_valid": true,
    "total_rows": 50,
    "valid_rows": 47,
    "invalid_rows": 3,
    "validation_errors": [
      {
        "row": 15,
        "field": "category_name",
        "error": "Category not found"
      }
    ],
    "preview": [
      {
        "row": 1,
        "ingredient_name": "Fresh Tomatoes",
        "category_name": "Vegetables",
        "status": "valid"
      }
    ]
  }
}
```

---

## 🔐 **8. PERMISSIONS & ACCESS CONTROL APIs**

### **GET /api/recipe/recipes/{id}/permissions**
**Purpose:** Get recipe permissions

### **POST /api/recipe/recipes/{id}/permissions**
**Purpose:** Grant recipe permissions

**Request Body:**
```json
{
  "user_id": 123,
  "permissions": {
    "can_view": true,
    "can_edit": false,
    "can_delete": false,
    "can_publish": false
  },
  "expires_at": "2025-12-31T23:59:59Z"
}
```

---

## ⚙️ **BUSINESS LOGIC IMPLEMENTATION**

### **Key Business Rules:**

1. **Recipe Costing:** `Total Cost = Σ(ingredient_quantity × cost_per_unit × (1 + waste_percentage))`
2. **Nutrition Calculation:** Aggregate from ingredients based on quantities
3. **Allergen Detection:** Union of all ingredient allergens
4. **Recipe Scaling:** Proportional scaling with unit conversion support
5. **Version Control:** Automatic versioning on significant changes

### **Import/Export Features:**

1. **Bulk Operations:** Handle large datasets efficiently
2. **Validation:** Pre-import validation with detailed error reporting
3. **Templates:** Downloadable templates for easy data preparation
4. **Multiple Formats:** Support for Excel, CSV, JSON, and PDF
5. **Error Handling:** Detailed error reporting with row-level feedback

### **Performance Considerations:**

1. **Indexing:** Strategic indexes on search and filter fields
2. **Caching:** Recipe calculations and frequently accessed data
3. **Pagination:** All list endpoints support pagination
4. **Lazy Loading:** Optional includes for related data
5. **Search:** Full-text search with relevance scoring

This comprehensive API specification provides a robust, scalable foundation for a professional recipe management system with full import/export capabilities!