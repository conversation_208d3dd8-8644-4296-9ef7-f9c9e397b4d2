@echo off
echo 🚀 Starting Enhanced Customer Support System...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available

REM Install dependencies
echo 📦 Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Check environment variables
if "%DB_HOST%"=="" (
    echo ⚠️  Database environment variables not set. Please configure:
    echo    - DB_HOST
    echo    - DB_NAME
    echo    - DB_USER
    echo    - DB_PASS
    echo    - JWT_SECRET
    echo.
    echo You can set them in a .env file or as environment variables.
)

REM Run database migrations
echo 🗄️  Running database migrations...
npm run db:migrate
if %errorlevel% neq 0 (
    echo ⚠️  Database migrations failed. Please check your database configuration.
    echo    Make sure your database is running and accessible.
) else (
    echo ✅ Database migrations completed
)

REM Build the application
echo 🔨 Building application...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Application built successfully

REM Start the application
echo 🎯 Starting Enhanced Customer Support System...
echo.
echo 📋 System Features:
echo    ✅ Multi-tenant support ticket management
echo    ✅ Role-based access control (Public, Agent, Admin)
echo    ✅ Advanced filtering and search capabilities
echo    ✅ Bulk operations for administrative efficiency
echo    ✅ File attachment support
echo    ✅ SLA tracking and monitoring
echo    ✅ Complete audit trails
echo    ✅ Comprehensive dashboard with metrics
echo.
echo 🌐 API Endpoints will be available at:
echo    Public:  http://localhost:5008/api/public/support/*
echo    Private: http://localhost:5008/api/private/support/*
echo    Admin:   http://localhost:5008/api/private/admin/*
echo.
echo 📖 Documentation: http://localhost:5008/api-docs
echo 🏥 Health Check: http://localhost:5008/health
echo.
echo 🚀 Starting server...

npm start
