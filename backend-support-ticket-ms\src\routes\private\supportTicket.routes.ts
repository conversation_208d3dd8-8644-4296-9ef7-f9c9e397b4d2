import express from "express";
import uploadService from "../../helper/upload.service";
import supportTicketController from "../../controller/supportTicket.controller";
import supportTicketValidator from "../../validators/supportTicket.validator";
import { SUPPORT_FILE_UPLOAD_CONSTANT } from "../../utils/common";

// Configure S3 Upload for Support Ticket Files (Following Recipe Pattern)
const multerS3Upload = uploadService.multerS3(
  process.env.NODE_ENV || "development",
  SUPPORT_FILE_UPLOAD_CONSTANT.TICKET_ATTACHMENT.folder
);

const router = express.Router();

// GET /list - Get all tickets with pagination and filters (following recipe-ms pattern)
router.get(
  "/list",
  supportTicketValidator.getTicketsListValidator(),
  supportTicketController.getAllTickets
);

// GET /:id - Get single ticket by ID or slug (following recipe-ms pattern)
router.get(
  "/:id",
  supportTicketValidator.getTicketValidator(),
  supportTicketController.getTicket
);

// POST /create - Create new ticket with file upload (following recipe-ms pattern)
router.post(
  "/create",
  multerS3Upload.fields([{ name: "ticketFiles", maxCount: 5 }]),
  supportTicketValidator.createTicketValidator(),
  supportTicketController.createTicket
);

// PUT /update/:id - Update ticket with file upload (following recipe-ms pattern)
router.put(
  "/update/:id",
  multerS3Upload.fields([{ name: "ticketFiles", maxCount: 5 }]),
  supportTicketValidator.updateTicketValidator(),
  supportTicketController.updateTicket
);

// DELETE /delete/:id - Delete ticket (following recipe-ms pattern)
router.delete(
  "/delete/:id",
  supportTicketValidator.deleteTicketValidator(),
  supportTicketController.deleteTicket
);

// PUT /assign/:id - Assign ticket to user
router.put(
  "/assign/:id",
  supportTicketValidator.assignTicketValidator(),
  supportTicketController.assignTicket
);

// PUT /resolve/:id - Resolve ticket
router.put(
  "/resolve/:id",
  supportTicketValidator.resolveTicketValidator(),
  supportTicketController.resolveTicket
);

// POST /rate/:id - Rate ticket
router.post(
  "/rate/:id",
  supportTicketValidator.rateTicketValidator(),
  supportTicketController.rateTicket
);

// Export routes following recipe-ms pattern
export default router;
