import { Router } from "express";
import requestController from "../../controller/requestDemo.controller";

const router = Router();

/** Request Demo API 
 * @swagger
 * /v1/public/demo-request/add-demo-request:
 *   post:
 *     summary: Request a demo
 *     description: API to request a demo for a user.
 *     tags:
 *       - Demo Requests
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - email
 *               - mobile_number
 *               - organization_name
 *               - number_of_employee
 *               - industry
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: "Test"
 *               last_name:
 *                 type: string
 *                 example: "User"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               mobile_number:
 *                 type: string
 *                 example: "1234567890"
 *               organization_name:
 *                 type: string
 *                 example: "test_org"
 *               number_of_employee:
 *                 type: integer
 *                 example: 10
 *               industry:
 *                 type: string
 *                 example: "Hospitality"
 *     responses:
 *       200:
 *         description: Request submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Demo request submitted successfully."
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post("/add-demo-request", requestController.requestDemoRequest);

/** Get all Demo API
 * @swagger
 * /v1/public/demo-request/get-all-demo-requests:
 *   get:
 *     summary: Get all demo requests
 *     tags:
 *       - Demo Requests
 *     responses:
 *       200:
 *         description: Demo requests fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Demo requests fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       first_name:
 *                         type: string
 *                         example: Test
 *                       last_name:
 *                         type: string
 *                         example: Test
 *                       email:
 *                         type: string
 *                         example: <EMAIL>
 *                       mobile_number:
 *                         type: string
 *                         example: 123465798
 *                       organization_name:
 *                         type: string
 *                         example: NV
 *                       number_of_employee:
 *                         type: integer
 *                         example: 10
 *                       industry:
 *                         type: string
 *                         example: Hospitality
 *                       help_description:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       created_by:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       updated_by:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-04-09T11:38:49.000Z
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-04-09T11:38:49.000Z
 */
router.get("/get-all-demo-requests", requestController.getAllDemoRequest);
export default router;