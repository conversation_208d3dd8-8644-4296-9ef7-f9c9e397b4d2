# 🍽️ TTH Recipe Controller - Complete Implementation Example

This document provides a complete implementation example of a Recipe Controller following TTH's architectural patterns and standards.

## 📁 **File Structure (TTH Pattern)**

```
src/
├── controllers/
│   ├── recipe.controller.ts          // Main recipe controller
│   ├── recipeCategory.controller.ts  // Category management
│   └── ingredient.controller.ts      // Ingredient management
├── models/
│   ├── Recipe.ts                     // Recipe model
│   ├── RecipeIngredient.ts          // Recipe-ingredient junction
│   ├── RecipeInstruction.ts         // Recipe instructions
│   └── RecipeCategory.ts            // Recipe categories
├── validators/
│   ├── recipe.validator.ts          // Recipe validation rules
│   └── ingredient.validator.ts      // Ingredient validation
├── helpers/
│   ├── recipeHelper.ts              // Recipe utility functions
│   └── nutritionCalculator.ts      // Nutrition calculations
├── services/
│   ├── recipeService.ts             // Business logic
│   └── notificationService.ts      // Notifications
└── types/
    └── recipe.types.ts              // TypeScript interfaces
```

## 🎮 **Recipe Controller Implementation**

### **recipe.controller.ts**

```typescript
import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Op } from 'sequelize';
import { Recipe } from '../models/Recipe';
import { RecipeIngredient } from '../models/RecipeIngredient';
import { RecipeInstruction } from '../models/RecipeInstruction';
import { RecipeCategory } from '../models/RecipeCategory';
import { User } from '../models/User';
import { Activity } from '../models/Activity';
import { 
  generateRecipeCode, 
  calculateNutrition, 
  validateRecipePermission 
} from '../helpers/recipeHelper';
import { 
  createNotification, 
  sendEmailNotification 
} from '../services/notificationService';
import { rabbitmqPublisher } from '../config/rabbitmq';
import { RABBITMQ_QUEUE } from '../constants/queue.constants';
import { 
  recipe_status, 
  approval_status, 
  ROLE_CONSTANT 
} from '../constants/status.constants';

/**
 * Recipe Controller - TTH Pattern Implementation
 * Handles all recipe-related operations with multi-tenant support
 */
export class RecipeController {

  /**
   * Create a new recipe
   * @route POST /api/recipes
   * @access Private (Authenticated users)
   */
  static async createRecipe(req: Request, res: Response): Promise<Response> {
    try {
      const {
        recipe_name,
        recipe_description,
        category_id,
        cuisine_type,
        difficulty_level,
        meal_type,
        prep_time_minutes,
        cook_time_minutes,
        servings,
        ingredients = [],
        instructions = [],
        recipe_tags = [],
        dietary_restrictions = []
      } = req.body;

      // 1. Permission check for recipe creation
      const hasPermission = await validateRecipePermission(
        req.user.id, 
        'CREATE', 
        req.user.organization_id
      );

      if (!hasPermission) {
        return res.status(StatusCodes.FORBIDDEN).json({
          status: false,
          message: res.__('ERROR_INSUFFICIENT_PERMISSION')
        });
      }

      // 2. Generate unique recipe code
      const recipe_code = await generateRecipeCode(req.user.organization_id);

      // 3. Calculate total time
      const total_time_minutes = prep_time_minutes + cook_time_minutes;

      // 4. Create recipe with DRAFT status (TTH Pattern)
      const newRecipe = await Recipe.setHeaders(req).create({
        recipe_code,
        recipe_name,
        recipe_description,
        category_id,
        cuisine_type,
        difficulty_level,
        meal_type,
        prep_time_minutes,
        cook_time_minutes,
        total_time_minutes,
        servings,
        recipe_tags: JSON.stringify(recipe_tags),
        dietary_restrictions: JSON.stringify(dietary_restrictions),
        recipe_status: recipe_status.DRAFT,
        approval_status: approval_status.PENDING,
        organization_id: req.user.organization_id,
        branch_id: req.user.branch_id,
        created_by: req.user.id
      });

      // 5. Add ingredients if provided
      if (ingredients && ingredients.length > 0) {
        const ingredientData = ingredients.map((ingredient: any, index: number) => ({
          recipe_id: newRecipe.id,
          ingredient_id: ingredient.ingredient_id,
          quantity: ingredient.quantity,
          unit_of_measurement: ingredient.unit,
          ingredient_notes: ingredient.notes || null,
          is_optional: ingredient.is_optional || false,
          ingredient_order: index + 1,
          ingredient_status: 'ACTIVE',
          organization_id: req.user.organization_id,
          created_by: req.user.id
        }));

        await RecipeIngredient.bulkCreate(ingredientData);
      }

      // 6. Add instructions if provided
      if (instructions && instructions.length > 0) {
        const instructionData = instructions.map((instruction: any) => ({
          recipe_id: newRecipe.id,
          step_number: instruction.step,
          instruction_title: instruction.title || '',
          instruction_description: instruction.description,
          estimated_time_minutes: instruction.duration || 0,
          temperature_celsius: instruction.temperature || null,
          equipment_needed: instruction.equipment || null,
          chef_tips: instruction.tips || null,
          instruction_status: 'ACTIVE',
          organization_id: req.user.organization_id,
          created_by: req.user.id
        }));

        await RecipeInstruction.bulkCreate(instructionData);
      }

      // 7. Calculate nutritional information
      const nutritionData = await calculateNutrition(newRecipe.id);
      await newRecipe.update(nutritionData);

      // 8. Log activity (TTH Pattern)
      await Activity.create({
        activity_type: 'RECIPE_CREATED',
        activity_description: `Recipe "${recipe_name}" created`,
        reference_type: 'recipe',
        reference_id: newRecipe.id,
        created_by: req.user.id,
        organization_id: req.user.organization_id
      });

      // 9. Send notification to managers (TTH Pattern)
      await createNotification({
        notification_content: `New recipe "${recipe_name}" created by ${req.user.user_first_name}`,
        notification_subject: 'New Recipe Created',
        notification_type: 'RECIPE_CREATED',
        from_user_id: req.user.id,
        role_ids: [ROLE_CONSTANT.BRANCH_MANAGER, ROLE_CONSTANT.HOTEL_MANAGER],
        organization_id: req.user.organization_id
      });

      // 10. Queue background processing (TTH Pattern)
      await rabbitmqPublisher.publishMessage(RABBITMQ_QUEUE.RECIPE_PROCESSING, {
        recipe_id: newRecipe.id,
        action: 'RECIPE_CREATED',
        user_id: req.user.id,
        organization_id: req.user.organization_id
      });

      // 11. Fetch complete recipe data for response
      const completeRecipe = await Recipe.findByPk(newRecipe.id, {
        include: [
          {
            model: RecipeCategory,
            as: 'category',
            attributes: ['id', 'category_name', 'category_description']
          },
          {
            model: RecipeIngredient,
            as: 'recipeIngredients',
            include: [{
              model: Ingredient,
              as: 'ingredient',
              attributes: ['id', 'ingredient_name', 'ingredient_category']
            }]
          },
          {
            model: RecipeInstruction,
            as: 'instructions',
            order: [['step_number', 'ASC']]
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'user_first_name', 'user_last_name']
          }
        ]
      });

      return res.status(StatusCodes.CREATED).json({
        status: true,
        message: res.__('RECIPE_CREATED_SUCCESSFULLY'),
        data: completeRecipe
      });

    } catch (error) {
      console.error('Error creating recipe:', error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        status: false,
        message: res.__('ERROR_INTERNAL_SERVER'),
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get all recipes with advanced filtering and pagination
   * @route GET /api/recipes
   * @access Private (Authenticated users)
   */
  static async getAllRecipes(req: Request, res: Response): Promise<Response> {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        category_id,
        cuisine_type,
        difficulty_level,
        meal_type,
        max_prep_time,
        dietary_restrictions,
        sort_by = 'created_at',
        sort_order = 'DESC',
        status = 'ACTIVE'
      } = req.query;

      // 1. Build where clause with organization isolation (TTH Pattern)
      const whereClause: any = {
        organization_id: req.user.organization_id,
        recipe_status: status
      };

      // 2. Add search functionality
      if (search) {
        whereClause[Op.or] = [
          { recipe_name: { [Op.like]: `%${search}%` } },
          { recipe_description: { [Op.like]: `%${search}%` } }
        ];
      }

      // 3. Add filters
      if (category_id) whereClause.category_id = category_id;
      if (cuisine_type) whereClause.cuisine_type = cuisine_type;
      if (difficulty_level) whereClause.difficulty_level = difficulty_level;
      if (meal_type) whereClause.meal_type = meal_type;
      if (max_prep_time) whereClause.prep_time_minutes = { [Op.lte]: max_prep_time };

      // 4. Handle dietary restrictions filter
      if (dietary_restrictions) {
        whereClause.dietary_restrictions = {
          [Op.like]: `%${dietary_restrictions}%`
        };
      }

      // 5. Calculate pagination
      const offset = (Number(page) - 1) * Number(limit);

      // 6. Fetch recipes with associations
      const { count, rows: recipes } = await Recipe.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: RecipeCategory,
            as: 'category',
            attributes: ['id', 'category_name', 'category_color']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'user_first_name', 'user_last_name']
          }
        ],
        order: [[sort_by as string, sort_order as string]],
        limit: Number(limit),
        offset: offset,
        distinct: true
      });

      // 7. Calculate pagination metadata
      const totalPages = Math.ceil(count / Number(limit));
      const hasNext = Number(page) < totalPages;
      const hasPrev = Number(page) > 1;

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__('RECIPES_FETCHED_SUCCESSFULLY'),
        data: {
          recipes,
          pagination: {
            currentPage: Number(page),
            totalPages,
            totalItems: count,
            itemsPerPage: Number(limit),
            hasNext,
            hasPrev
          }
        }
      });

    } catch (error) {
      console.error('Error fetching recipes:', error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        status: false,
        message: res.__('ERROR_INTERNAL_SERVER'),
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }

  /**
   * Get recipe by ID with complete details
   * @route GET /api/recipes/:id
   * @access Private (Authenticated users)
   */
  static async getRecipeById(req: Request, res: Response): Promise<Response> {
    try {
      const { id } = req.params;

      // 1. Fetch recipe with all associations
      const recipe = await Recipe.findOne({
        where: {
          id,
          organization_id: req.user.organization_id,
          recipe_status: { [Op.ne]: recipe_status.DELETED }
        },
        include: [
          {
            model: RecipeCategory,
            as: 'category',
            attributes: ['id', 'category_name', 'category_description', 'category_color']
          },
          {
            model: RecipeIngredient,
            as: 'recipeIngredients',
            where: { ingredient_status: 'ACTIVE' },
            required: false,
            include: [{
              model: Ingredient,
              as: 'ingredient',
              attributes: [
                'id', 'ingredient_name', 'ingredient_category',
                'calories_per_100g', 'protein_per_100g', 'allergen_info'
              ]
            }],
            order: [['ingredient_order', 'ASC']]
          },
          {
            model: RecipeInstruction,
            as: 'instructions',
            where: { instruction_status: 'ACTIVE' },
            required: false,
            order: [['step_number', 'ASC']]
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'user_first_name', 'user_last_name', 'user_email']
          },
          {
            model: User,
            as: 'approver',
            attributes: ['id', 'user_first_name', 'user_last_name'],
            required: false
          }
        ]
      });

      if (!recipe) {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: res.__('RECIPE_NOT_FOUND')
        });
      }

      // 2. Parse JSON fields
      const recipeData = recipe.toJSON();
      recipeData.recipe_tags = JSON.parse(recipeData.recipe_tags || '[]');
      recipeData.dietary_restrictions = JSON.parse(recipeData.dietary_restrictions || '[]');
      recipeData.allergen_info = JSON.parse(recipeData.allergen_info || '[]');

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__('RECIPE_FETCHED_SUCCESSFULLY'),
        data: recipeData
      });

    } catch (error) {
      console.error('Error fetching recipe:', error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        status: false,
        message: res.__('ERROR_INTERNAL_SERVER'),
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}
```

## 🔧 **Helper Functions Example**

### **recipeHelper.ts**

```typescript
import { Recipe } from '../models/Recipe';
import { RecipeIngredient } from '../models/RecipeIngredient';
import { Ingredient } from '../models/Ingredient';
import { Permission } from '../models/Permission';
import { UserRole } from '../models/UserRole';

/**
 * Generate unique recipe code following TTH pattern
 * Format: ORG-RCP-SEQUENCE (e.g., "TTH-RCP-001")
 */
export const generateRecipeCode = async (organizationId: string): Promise<string> => {
  const orgPrefix = organizationId.substring(0, 3).toUpperCase();
  
  const lastRecipe = await Recipe.findOne({
    where: { organization_id: organizationId },
    order: [['id', 'DESC']]
  });

  const sequence = lastRecipe ? lastRecipe.id + 1 : 1;
  const paddedSequence = sequence.toString().padStart(3, '0');
  
  return `${orgPrefix}-RCP-${paddedSequence}`;
};

/**
 * Calculate nutritional information for a recipe
 */
export const calculateNutrition = async (recipeId: number) => {
  const recipeIngredients = await RecipeIngredient.findAll({
    where: { recipe_id: recipeId, ingredient_status: 'ACTIVE' },
    include: [{
      model: Ingredient,
      as: 'ingredient',
      attributes: [
        'calories_per_100g', 'protein_per_100g', 'carbs_per_100g',
        'fat_per_100g', 'fiber_per_100g', 'sugar_per_100g', 'sodium_per_100g'
      ]
    }]
  });

  let totalCalories = 0;
  let totalProtein = 0;
  let totalCarbs = 0;
  let totalFat = 0;
  let totalFiber = 0;
  let totalSugar = 0;
  let totalSodium = 0;

  recipeIngredients.forEach(recipeIngredient => {
    const ingredient = recipeIngredient.ingredient;
    const quantity = recipeIngredient.quantity;
    
    // Convert quantity to grams (simplified conversion)
    let quantityInGrams = quantity;
    if (recipeIngredient.unit_of_measurement === 'kg') {
      quantityInGrams = quantity * 1000;
    } else if (recipeIngredient.unit_of_measurement === 'cups') {
      quantityInGrams = quantity * 240; // Approximate
    }
    
    const factor = quantityInGrams / 100;
    
    totalCalories += (ingredient.calories_per_100g || 0) * factor;
    totalProtein += (ingredient.protein_per_100g || 0) * factor;
    totalCarbs += (ingredient.carbs_per_100g || 0) * factor;
    totalFat += (ingredient.fat_per_100g || 0) * factor;
    totalFiber += (ingredient.fiber_per_100g || 0) * factor;
    totalSugar += (ingredient.sugar_per_100g || 0) * factor;
    totalSodium += (ingredient.sodium_per_100g || 0) * factor;
  });

  return {
    calories_per_serving: Math.round(totalCalories),
    protein_grams: Math.round(totalProtein * 10) / 10,
    carbs_grams: Math.round(totalCarbs * 10) / 10,
    fat_grams: Math.round(totalFat * 10) / 10,
    fiber_grams: Math.round(totalFiber * 10) / 10,
    sugar_grams: Math.round(totalSugar * 10) / 10,
    sodium_mg: Math.round(totalSodium)
  };
};

/**
 * Validate user permission for recipe operations (TTH RBAC Pattern)
 */
export const validateRecipePermission = async (
  userId: number, 
  action: string, 
  organizationId: string
): Promise<boolean> => {
  const userRoles = await UserRole.findAll({
    where: { 
      user_id: userId, 
      user_role_status: 'ACTIVE' 
    },
    include: [{
      model: Permission,
      where: {
        permission_module: 'recipe',
        permission_action: action,
        permission_status: 'ACTIVE',
        organization_id: organizationId
      }
    }]
  });

  return userRoles.length > 0;
};
```

This implementation follows TTH's architectural patterns including:

1. **Multi-tenant isolation** with `organization_id`
2. **Comprehensive status management** (DRAFT, PENDING, ACTIVE, etc.)
3. **RBAC integration** with permission validation
4. **Activity logging** for audit trails
5. **Background processing** with RabbitMQ
6. **Notification system** integration
7. **Internationalization** support with `res.__()` 
8. **Proper error handling** and response formatting
9. **TypeScript** with proper type definitions
10. **Sequelize ORM** with associations and transactions
