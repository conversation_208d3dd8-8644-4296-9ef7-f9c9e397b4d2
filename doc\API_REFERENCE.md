# 🍽️ TTH Recipe Module - API Reference

## 🌐 **Base URL & Environment**
```
Production:  https://api.teamtrainhub.com/v1
Staging:     https://staging-api.teamtrainhub.com/v1
Development: http://localhost:3000/api/v1
```

## 🔐 **Authentication (TTH Standard)**
All API requests require authentication using JWT tokens in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### **Multi-Tenant Headers (Required)**
```
X-Organization-ID: <organization_id>
X-Branch-ID: <branch_id> (optional)
Accept-Language: en|es|fr|de (for i18n support)
```

## 📋 **Response Format (TTH Standard)**
All API responses follow TTH's standard format:
```json
{
  "status": true|false,
  "message": "Localized message",
  "data": {}, // Response data (on success)
  "error": "", // Error message (on failure)
  "errors": [], // Validation errors (on validation failure)
  "pagination": {}, // Pagination metadata (for list endpoints)
  "meta": {} // Additional metadata
}
```

## Endpoints

### Authentication

#### POST /auth/login
Authenticate user and receive JWT token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "60d5ecb74b24a1234567890",
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### Recipes

#### GET /recipes
Get all recipes with optional filtering and pagination.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10, max: 50)
- `search` (string): Search term for recipe name/description
- `category` (string): Filter by category ID
- `cuisine` (string): Filter by cuisine type
- `difficulty` (string): Filter by difficulty level
- `maxPrepTime` (number): Maximum preparation time in minutes
- `dietary` (string): Filter by dietary restrictions

**Example:**
```
GET /recipes?search=chocolate&category=dessert&difficulty=beginner&page=1&limit=10
```

#### GET /recipes/:id
Get a specific recipe by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "60d5ecb74b24a1234567890",
    "name": "Chocolate Chip Cookies",
    "description": "Delicious homemade cookies",
    "category": {
      "id": "60d5ecb74b24a1234567891",
      "name": "Desserts"
    },
    "cuisine": "american",
    "difficulty": "beginner",
    "prepTime": 15,
    "cookTime": 12,
    "servings": 24,
    "ingredients": [...],
    "instructions": [...],
    "nutrition": {...},
    "rating": 4.5,
    "reviewCount": 23,
    "createdAt": "2023-06-25T10:30:00Z"
  }
}
```

#### POST /recipes
Create a new recipe.

**Request:**
```json
{
  "name": "Chocolate Chip Cookies",
  "description": "Soft and chewy chocolate chip cookies",
  "category": "60d5ecb74b24a1234567891",
  "cuisine": "american",
  "difficulty": "beginner",
  "prepTime": 15,
  "cookTime": 12,
  "servings": 24,
  "ingredients": [
    {
      "ingredient": "60d5ecb74b24a1234567892",
      "amount": 2.25,
      "unit": "cups",
      "notes": "All-purpose flour"
    }
  ],
  "instructions": [
    {
      "step": 1,
      "description": "Preheat oven to 375°F",
      "duration": 5
    }
  ],
  "tags": ["cookies", "dessert", "baking"]
}
```

#### PUT /recipes/:id
Update an existing recipe.

#### DELETE /recipes/:id
Delete a recipe (soft delete).

### Ingredients

#### GET /ingredients
Get all ingredients.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `search` (string): Search term
- `category` (string): Filter by ingredient category

#### GET /ingredients/:id
Get a specific ingredient by ID.

#### POST /ingredients
Create a new ingredient.

**Request:**
```json
{
  "name": "All-Purpose Flour",
  "category": "grains",
  "nutrition": {
    "calories": 364,
    "protein": 10,
    "carbohydrates": 76,
    "fat": 1,
    "fiber": 3,
    "sugar": 0,
    "sodium": 2
  },
  "allergens": ["gluten"]
}
```

### Categories

#### GET /categories
Get all recipe categories.

#### POST /categories
Create a new category.

**Request:**
```json
{
  "name": "Desserts",
  "description": "Sweet treats and desserts",
  "image": "https://example.com/desserts.jpg"
}
```

### Reviews

#### GET /recipes/:id/reviews
Get all reviews for a recipe.

#### POST /recipes/:id/reviews
Add a review to a recipe.

**Request:**
```json
{
  "rating": 5,
  "review": "Amazing recipe! My family loved it.",
  "wouldMakeAgain": true
}
```

### Favorites

#### GET /users/favorites
Get user's favorite recipes.

#### POST /recipes/:id/favorite
Add recipe to favorites.

#### DELETE /recipes/:id/favorite
Remove recipe from favorites.

### Search

#### GET /search
Advanced search across recipes.

**Query Parameters:**
- `q` (string): Search query
- `filters` (object): Advanced filters
- `sort` (string): Sort order (rating, date, name)

### Nutrition

#### GET /recipes/:id/nutrition
Get detailed nutritional information for a recipe.

#### POST /recipes/:id/nutrition/calculate
Recalculate nutrition based on current ingredients.

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation errors |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## Rate Limiting

API requests are limited to:
- 100 requests per 15 minutes for authenticated users
- 20 requests per 15 minutes for unauthenticated users

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Pagination

Paginated endpoints return pagination metadata:
```json
{
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## File Uploads

Recipe images can be uploaded using multipart/form-data:

#### POST /recipes/:id/image
Upload recipe image.

**Request:**
```
Content-Type: multipart/form-data

image: [file]
```

**Response:**
```json
{
  "success": true,
  "data": {
    "imageUrl": "https://s3.amazonaws.com/bucket/recipe-image.jpg"
  }
}
```

## Webhooks

Configure webhooks to receive notifications for recipe events:

- `recipe.created` - New recipe created
- `recipe.updated` - Recipe updated
- `recipe.deleted` - Recipe deleted
- `review.created` - New review added

Webhook payload example:
```json
{
  "event": "recipe.created",
  "data": {
    "recipe": {...},
    "user": {...}
  },
  "timestamp": "2023-06-25T10:30:00Z"
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const RecipeAPI = require('teamtrainhub-recipe-sdk');

const client = new RecipeAPI({
  apiKey: 'your-api-key',
  baseURL: 'https://api.teamtrainhub.com/v1'
});

// Get recipes
const recipes = await client.recipes.list({
  search: 'chocolate',
  category: 'dessert'
});

// Create recipe
const newRecipe = await client.recipes.create({
  name: 'My Recipe',
  // ... other fields
});
```

### Python
```python
from teamtrainhub_recipe import RecipeClient

client = RecipeClient(api_key='your-api-key')

# Get recipes
recipes = client.recipes.list(search='chocolate', category='dessert')

# Create recipe
new_recipe = client.recipes.create({
    'name': 'My Recipe',
    # ... other fields
})
```

For more detailed examples and advanced usage, see the [Example Usage Guide](EXAMPLE_USAGE.md).
