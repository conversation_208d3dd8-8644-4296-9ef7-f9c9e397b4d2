import { Request, Response, NextFunction } from "express";
import { StatusCodes } from "http-status-codes";
import { hasPermission, isDefaultAccess } from "../utils/common";

/**
 * Permission-based authorization middleware
 * Checks if user has specific permissions for actions
 */
export const requirePermission = (module: string, action: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
      const user = (req as any).user;

      if (!user) {
        return res.status(StatusCodes.UNAUTHORIZED).json({
          success: false,
          message: "Authentication required",
          error: "No user found in request"
        });
      }

      // Check if user has super admin access (bypasses all permission checks)
      const hasAdminAccess = await isDefaultAccess(user.keycloak_userId);
      if (hasAdminAccess) {
        (req as any).user.isAdmin = true;
        return next();
      }

      // Check specific permission
      const hasRequiredPermission = await hasPermission(user.id, module, action);
      
      if (!hasRequiredPermission) {
        return res.status(StatusCodes.FORBIDDEN).json({
          success: false,
          message: `Permission denied for ${action} on ${module}`,
          error: "Insufficient permissions"
        });
      }

      next();

    } catch (error) {
      console.error("Permission check error:", error);
      return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: "Permission check failed",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  };
};

/**
 * Organization-based access control
 * Ensures users can only access data from their organization (unless admin)
 */
export const requireOrganizationAccess = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    const user = (req as any).user;

    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Authentication required",
        error: "No user found in request"
      });
    }

    // Check if user has super admin access
    const hasAdminAccess = await isDefaultAccess(user.keycloak_userId);
    
    if (hasAdminAccess) {
      (req as any).user.isAdmin = true;
      return next();
    }

    // For non-admin users, ensure they have an organization
    if (!user.organization_id) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: "Organization access required",
        error: "User must belong to an organization"
      });
    }

    // Add organization filter to request for use in controllers
    (req as any).organizationFilter = { organization_id: user.organization_id };
    
    next();

  } catch (error) {
    console.error("Organization access check error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Organization access check failed",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

/**
 * Ticket ownership validation
 * Ensures users can only access tickets they created or are assigned to (unless admin)
 */
export const requireTicketAccess = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
  try {
    const user = (req as any).user;
    const ticketId = req.params.id || req.params.ticket_id;

    if (!user) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: "Authentication required",
        error: "No user found in request"
      });
    }

    if (!ticketId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: "Ticket ID required",
        error: "No ticket ID provided in request"
      });
    }

    // Check if user has super admin access
    const hasAdminAccess = await isDefaultAccess(user.keycloak_userId);
    
    if (hasAdminAccess) {
      (req as any).user.isAdmin = true;
      return next();
    }

    // Import Ticket model dynamically to avoid circular dependencies
    const { default: Ticket } = await import("../models/Ticket");
    
    // Find the ticket
    const ticket = await Ticket.findByPk(ticketId);
    
    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: "Ticket not found",
        error: "Ticket does not exist"
      });
    }

    // Check if user has access to this ticket
    const hasAccess = 
      ticket.created_by === user.id || // User created the ticket
      ticket.assigned_to_user_id === user.id || // User is assigned to the ticket
      ticket.organization_id === user.organization_id; // Same organization

    if (!hasAccess) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: "Access denied to this ticket",
        error: "You don't have permission to access this ticket"
      });
    }

    // Add ticket to request for use in controllers
    (req as any).ticket = ticket;
    
    next();

  } catch (error) {
    console.error("Ticket access check error:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: "Ticket access check failed",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

/**
 * Rate limiting for public endpoints
 */
export const publicRateLimit = (maxRequests: number = 10, windowMinutes: number = 15) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): any => {
    const clientId = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;

    const clientData = requests.get(clientId);

    if (!clientData || now > clientData.resetTime) {
      // Reset or initialize
      requests.set(clientId, {
        count: 1,
        resetTime: now + windowMs
      });
      return next();
    }

    if (clientData.count >= maxRequests) {
      return res.status(StatusCodes.TOO_MANY_REQUESTS).json({
        success: false,
        message: "Too many requests",
        error: `Rate limit exceeded. Try again in ${Math.ceil((clientData.resetTime - now) / 60000)} minutes.`
      });
    }

    clientData.count++;
    next();
  };
};

/**
 * Support ticket module permissions
 */
export const SUPPORT_PERMISSIONS = {
  TICKET: {
    CREATE: 'CREATE',
    READ: 'READ',
    UPDATE: 'UPDATE',
    DELETE: 'DELETE',
    ASSIGN: 'ASSIGN',
    ESCALATE: 'ESCALATE',
    CLOSE: 'CLOSE'
  },
  MESSAGE: {
    CREATE: 'CREATE',
    READ: 'READ',
    UPDATE: 'UPDATE',
    DELETE: 'DELETE'
  },
  CONFIG: {
    READ: 'READ',
    UPDATE: 'UPDATE'
  }
};

// Convenience middleware functions
export const canCreateTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.CREATE);
export const canReadTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.READ);
export const canUpdateTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.UPDATE);
export const canDeleteTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.DELETE);
export const canAssignTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.ASSIGN);
export const canEscalateTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.ESCALATE);
export const canCloseTicket = requirePermission('TICKET', SUPPORT_PERMISSIONS.TICKET.CLOSE);

export const canCreateMessage = requirePermission('MESSAGE', SUPPORT_PERMISSIONS.MESSAGE.CREATE);
export const canReadMessage = requirePermission('MESSAGE', SUPPORT_PERMISSIONS.MESSAGE.READ);
export const canUpdateMessage = requirePermission('MESSAGE', SUPPORT_PERMISSIONS.MESSAGE.UPDATE);
export const canDeleteMessage = requirePermission('MESSAGE', SUPPORT_PERMISSIONS.MESSAGE.DELETE);

export const canReadConfig = requirePermission('CONFIG', SUPPORT_PERMISSIONS.CONFIG.READ);
export const canUpdateConfig = requirePermission('CONFIG', SUPPORT_PERMISSIONS.CONFIG.UPDATE);
