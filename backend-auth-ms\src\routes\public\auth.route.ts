import { Router } from "express";
import authController from "../../controller/auth.controller";
import authValidator from "../../validator/auth.validator";
const router = Router();

/** Organization User Register */
/**
 * @swagger
 * /v1/public/auth/org-user-register:
 *   post:
 *     summary: Register a new user with organization data
 *     description: This API allows registering a new user along with their associated organization details.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user
 *               - organization
 *             properties:
 *               user:
 *                 type: object
 *                 required:
 *                   - firstName
 *                   - lastName
 *                   - userPhoneNumber
 *                   - userCountryCode
 *                   - username
 *                   - password
 *                   - email
 *                 properties:
 *                   firstName:
 *                     type: string
 *                     description: The first name of the user.
 *                     example: "hardik11"
 *                   lastName:
 *                     type: string
 *                     description: The last name of the user.
 *                     example: "Jnext"
 *                   userPhoneNumber:
 *                     type: integer
 *                     description: The phone number of the user.
 *                     example: 6354898289
 *                   userCountryCode:
 *                     type: integer
 *                     description: The country code for the phone number.
 *                     example: 3
 *                   username:
 *                     type: string
 *                     description: The username for the user.
 *                     example: "hardik_test11"
 *                   password:
 *                     type: string
 *                     description: The password for the user.
 *                     example: "hardik@123"
 *                   email:
 *                     type: string
 *                     description: The email of the user.
 *                     example: "<EMAIL>"
 *               organization:
 *                 type: object
 *                 required:
 *                   - name
 *                   - website
 *                   - email
 *                   - multiple_location
 *                   - description
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: The name of the organization.
 *                     example: "test_org"
 *                   website:
 *                     type: string
 *                     description: The website for the organization.
 *                     example: "https://jnextservices11.com/"
 *                   email:
 *                     type: string
 *                     description: The email for the organization.
 *                     example: "<EMAIL>"
 *                   multiple_location:
 *                     type: integer
 *                     description: The number of locations for the organization.
 *                     example: 2
 *                   redirectUrl:
 *                     type: string
 *                     description: The redirect URL for the organization (nullable).
 *                     example: null
 *                   description:
 *                     type: string
 *                     description: A brief description of the organization.
 *                     example: "this is my org data"
 *     responses:
 *       200:
 *         description: Verification email has been sent to your registered email address, please check your inbox..
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Verification email has been sent to your registered email address, please check your inbox"
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.post("/org-user-register", authValidator.orgUserRegister(), authController.orgUserRegister);

/** verify auth token */
/**
 * @swagger
 * /v1/public/auth/verify-auth-token:
 *   put:
 *     summary: Verify authentication token
 *     description: This API verifies the authentication token provided by the user, including user credentials like email and password.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The unique identifier of the user.
 *                 example: "9fb912c8-e6ef-422a-8ca2-948a65512e7e"
 *     responses:
 *       200:
 *         description: Data updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data updated successfully."
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       401:
 *         description: Unauthorized, invalid user credentials or token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized, invalid credentials."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.put("/verify-auth-token", authValidator.verifyAuthToken(), authController.verifyAuthToken);

/** Get user data by id */
/**
 * @swagger
 * /v1/public/auth/get-user-data/{userId}:
 *   get:
 *     summary: Get user data by user ID
 *     description: This API allows you to retrieve user data using a user ID.
 *     tags:
 *       - Auth
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: The unique identifier of the user.
 *         schema:
 *           type: string
 *           example: "9fb912c8-e6ef-422a-8ca2-948a65512e7e"
 *     responses:
 *       200:
 *         description: Successfully retrieved user data.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 firstName:
 *                   type: string
 *                   example: "John"
 *                 lastName:
 *                   type: string
 *                   example: "Doe"
 *                 username:
 *                   type: string
 *                   example: "johndoe"
 *                 email:
 *                   type: string
 *                   example: "<EMAIL>"
 *                 userPhoneNumber:
 *                   type: integer
 *                   example: 1234567890
 *       400:
 *         description: Bad request, invalid user ID format.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid user ID format."
 *       404:
 *         description: User not found with the given user ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.get("/get-user-data/:userId", authController.getUserDataById);

/** Get user data by id */
/**
 * @swagger
 * /v1/public/auth/login:
 *   post:
 *     summary: User login
 *     description: This API endpoint allows a user to log in using their username and password.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *                 description: The username of the user.
 *                 example: "hitesh_test"
 *               password:
 *                 type: string
 *                 description: The password of the user.
 *                 example: "hitesh@123"
 *     responses:
 *       200:
 *         description: Successful login, returns a JWT token.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: The JWT token for the logged-in user.
 *                   example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiYjY4N2Y5ZTgtYjYwZC00OTI0LWI2YmUtNzU0ZTgwOGEzODI0In0.tgMIepQxoGTt34l5bniIUJgFiBaGBd33xyWpbLjQ5K0"
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       401:
 *         description: Unauthorized, invalid username or password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized, invalid credentials."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.post("/login", authValidator.login(), authController.login);

/** Forgot password API */
/**
 * @swagger
 * /v1/public/auth/forgot-password:
 *   post:
 *     summary: Request password reset
 *     description: This API endpoint allows a user to request a password reset by providing their email or user ID.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 description: The email address associated with the user's account.
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Password reset request was successful.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Password reset link sent to the provided email."
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       404:
 *         description: User not found with the provided email or user ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.post("/forgot-password", authValidator.forgotPassword(), authController.forgotPassword);

/** Resend OTP API */
/**
 * @swagger
 * /v1/public/auth/resend-otp:
 *   post:
 *     summary: Resend OTP for authentication
 *     description: This API endpoint allows a user to request a resend of the OTP for authentication by providing their user ID and email address.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The unique identifier of the user requesting the OTP resend.
 *                 example: "9fb912c8-e6ef-422a-8ca2-948a65512e7e"
 *     responses:
 *       200:
 *         description: OTP has been successfully resent.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP resent successfully."
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       404:
 *         description: User not found with the provided email or user ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.post("/resend-otp", authValidator.resendOTP(), authController.resendOTP);

/** Reset Password API */
/**
 * @swagger
 * /v1/public/auth/reset-password:
 *   put:
 *     summary: Reset user password
 *     description: This API endpoint allows a user to reset their password by providing their user ID and new password.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - password
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The unique identifier for the user.
 *                 example: "9fb912c8-e6ef-422a-8ca2-948a65512e7e"
 *               password:
 *                 type: string
 *                 description: The new password for the user.
 *                 example: "test@123"
 *     responses:
 *       200:
 *         description: Password successfully reset.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Password reset successfully."
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       404:
 *         description: User not found with the provided user ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.put("/reset-password", authValidator.resetUserPassword(), authController.resetUserPassword);

/** Verify OTP API */
/**
 * @swagger
 * /v1/public/auth/verify-otp:
 *   post:
 *     summary: Verify OTP for authentication
 *     description: This API endpoint allows a user to verify the OTP received for authentication by providing their user ID and the OTP.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - otp
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The unique identifier of the user for OTP verification.
 *                 example: "9fb912c8-e6ef-422a-8ca2-948a65512e7e"
 *               otp:
 *                 type: string
 *                 description: The OTP provided to the user for verification.
 *                 example: "1476"
 *     responses:
 *       200:
 *         description: OTP successfully verified.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP verified successfully."
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       401:
 *         description: Unauthorized, incorrect OTP or expired OTP.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid OTP."
 *       404:
 *         description: User not found with the provided user ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.post("/verify-otp", authValidator.verifyOTP(), authController.verifyOTP);

/** Timezone List API */
/**
 * @swagger
 * /v1/public/auth/timezone-list:
 *   get:
 *     summary: Get the list of available timezones
 *     description: This API endpoint returns a list of available timezones for the user.
 *     tags:
 *       - Auth
 *     responses:
 *       200:
 *         description: A list of available timezones.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     description: The timezone code (e.g., UTC, GMT, EST).
 *                     example: "UTC"
 *                   name:
 *                     type: string
 *                     description: The name of the timezone (e.g., Coordinated Universal Time).
 *                     example: "Coordinated Universal Time"
 *                   offset:
 *                     type: string
 *                     description: The offset of the timezone from UTC (e.g., +00:00, -05:00).
 *                     example: "+00:00"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.get("/timezone-list", authController.timezoneData);

/** Currency List API */
/**
 * @swagger
 * /v1/public/auth/currency-list:
 *   get:
 *     summary: Get the list of available currencies
 *     description: This API endpoint returns a list of available currencies for the user.
 *     tags:
 *       - Auth
 *     responses:
 *       200:
 *         description: A list of available currencies.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                     description: The currency code (e.g., USD, EUR).
 *                     example: "USD"
 *                   name:
 *                     type: string
 *                     description: The name of the currency (e.g., United States Dollar).
 *                     example: "United States Dollar"
 *                   symbol:
 *                     type: string
 *                     description: The symbol of the currency (e.g., $, €).
 *                     example: "$"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.get("/currency-list", authController.currencyData);

/** Get realm roles List API */
/**
 * @swagger
 * /v1/public/auth/get-realm-roles:
 *   get:
 *     summary: Get list of realm roles
 *     description: This API endpoint fetches a list of all available realm roles in the system.
 *     tags:
 *       - Auth
 *     responses:
 *       200:
 *         description: List of realm roles successfully retrieved.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The unique identifier of the role.
 *                     example: "123e4567-e89b-12d3-a456-426614174000"
 *                   name:
 *                     type: string
 *                     description: The name of the role.
 *                     example: "admin"
 *                   description:
 *                     type: string
 *                     description: A brief description of the role.
 *                     example: "Administrator with full access"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.get("/get-realm-roles", authController.getRealmRoles);

/** Country List API */
/**
 * @swagger
 * /v1/public/auth/country-list:
 *   get:
 *     summary: Get list of countries
 *     description: This API endpoint fetches a list of all available countries.
 *     tags:
 *       - Auth
 *     responses:
 *       200:
 *         description: List of countries successfully retrieved.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   countryCode:
 *                     type: string
 *                     description: The country code.
 *                     example: "IN"
 *                   countryName:
 *                     type: string
 *                     description: The name of the country.
 *                     example: "India"
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.get("/country-list", authController.countryLists);

/** Get access token From refresh token */
/** 
 * @swagger
 * /v1/public/auth/get-access-token:
 *   post:
 *     summary: Get access token From refresh token
 *     description: This API endpoint allows a to get new access token from the refresh token
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *                 description: The Refresh Token.
 *                 example: "eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJjOWJjZGQ1MS1lYzM1LTRjNzMtOWY3OC0xNDViN2UxN2RiYmQifQ.eyJleHAiOjE3MzQzNTEyMzMsImlhdCI6MTczNDM0OTQzMywianRpIjoiNDE3ZDlhN2ItODcxZC00OGUwLWJiMDItN2ZmMjA3YzA0ZmEwIiwiaXNzIjoiaHR0cDovL2xvY2FsaG9zdDo4ODkwL3JlYWxtcy9vcmdhIiwiYXVkIjoiaHR0cDovL2xvY2FsaG9zdDo4ODkwL3JlYWxtcy9vcmdhIiwic3ViIjoiNmVkNzhmZmQtNjI5Yi00Nzc5LWIwY2MtZjhjM2E1YzJkNjZjIiwidHlwIjoiUmVmcmVzaCIsImF6cCI6Im5vZGUtYmFja2VuZCIsInNpZCI6ImY2MjViMDNkLWEyNzctNGZkMi04MDVkLWFiZTAyMzgwNWQ2NCIsInNjb3BlIjoiYWNyIHJvbGVzIHdlYi1vcmlnaW5zIGVtYWlsIHByb2ZpbGUgYmFzaWMifQ.8yUixARBkOSYAovhpd30TJwCc9-_oQnxX9bi1Re653s9UiG_kgmvvrWlM8srNg9dwVI1fzZQ2dwQ9_r5Ug_AIw"
 *     responses:
 *       200:
 *         description: Data fetched successfully..
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Data fetched successfully."
 *       400:
 *         description: Bad request, missing or invalid fields.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid request payload."
 *       401:
 *         description: Unauthorized, Refresh token is not valid.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid refresh token."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Something went wrong."
 */
router.post("/get-access-token", authValidator.refreshToken(), authController.getAccessTokenFromRefreshToken);

/** Resend Email API */
/**
 * @swagger
 * /v1/public/auth/resend-email:
 *   post:
 *     summary: Resend verification email
 *     description: Resend the verification email to the user.
 *     tags:
 *       - Auth
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - email
 *               - isUpdateEmail
 *             properties:
 *               userId:
 *                 type: string
 *                 example: "b2f566e9-c7d1-437b-a477-0f7467828818"
 *                 description: Unique ID of the user
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: Email address of the user
 *               isUpdateEmail:
 *                 type: boolean
 *                 example: true
 *                 description: Flag to indicate if email needs to be updated
 *     responses:
 *       200:
 *         description: Verification email sent successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Verification email sent successfully."
 *       400:
 *         description: Bad request. Missing or invalid parameters.
 *       500:
 *         description: Internal server error.
 */
router.post("/resend-email", authValidator.resendEmail(), authController.resendEmail);

/** Script for Existing organization */
router.post("/create-organization", authController.createExistingOrganization);

/**Script for old organizations users */
router.post("/old-user-data", authController.oldUserDataScript);

/**Script for old organizations users */
router.post("/main-super-admin-user", authController.createMainSuperAdmin);

/** Send User Mail */
/**
 * @swagger
 * /v1/private/auth/send-user-mail:
 *   post:
 *     summary: Send user mail
 *     description: This API sends a mail to the user.
 *     tags:
 *       - Auth
 *     security:
 *       - BearerAuth: [] # JWT token authorization
 *     responses:
 *       200:
 *         description: Mail sent successfully.
 */
router.post("/send-user-mail", authController.sendUserMail);

export default router;