import { QueryTypes, Op } from "sequelize";
import { db, sequelize } from "../models";

/**
 * Database helper utilities for Customer Service module
 * Following the same patterns as the recipe module
 */
export class DatabaseHelper {
  /**
   * Execute raw SQL query with proper error handling
   */
  static async executeRawQuery(
    query: string,
    replacements: any = {},
    type: (typeof QueryTypes)[keyof typeof QueryTypes] = QueryTypes.SELECT
  ): Promise<any> {
    try {
      const result = await sequelize.query(query, {
        type,
        replacements,
        raw: true,
      });
      return result;
    } catch (error) {
      console.error("Raw query execution failed:", error);
      throw error;
    }
  }

  /**
   * Check if record exists by ID
   */
  static async recordExists(
    model: any,
    id: string | number,
    additionalWhere: any = {}
  ): Promise<boolean> {
    try {
      const count = await model.count({
        where: {
          id,
          ...additionalWhere,
        },
      });
      return count > 0;
    } catch (error) {
      console.error("Record existence check failed:", error);
      return false;
    }
  }

  /**
   * Get record by ID with error handling
   */
  static async findByIdSafe(
    model: any,
    id: string | number,
    options: any = {}
  ): Promise<any | null> {
    try {
      return await model.findByPk(id, options);
    } catch (error) {
      console.error("Find by ID failed:", error);
      return null;
    }
  }

  /**
   * Soft delete record
   */
  static async softDelete(
    model: any,
    id: string | number,
    userId?: number
  ): Promise<boolean> {
    try {
      const updateData: any = {
        deleted_at: new Date(),
      };

      if (userId) {
        updateData.updated_by = userId;
      }

      const [affectedRows] = await model.update(updateData, {
        where: { id },
      });

      return affectedRows > 0;
    } catch (error) {
      console.error("Soft delete failed:", error);
      return false;
    }
  }

  /**
   * Restore soft deleted record
   */
  static async restoreRecord(
    model: any,
    id: string | number,
    userId?: number
  ): Promise<boolean> {
    try {
      const updateData: any = {
        deleted_at: null,
      };

      if (userId) {
        updateData.updated_by = userId;
      }

      const [affectedRows] = await model.update(updateData, {
        where: { id },
        paranoid: false, // Include soft deleted records
      });

      return affectedRows > 0;
    } catch (error) {
      console.error("Record restoration failed:", error);
      return false;
    }
  }

  /**
   * Bulk update records with validation
   */
  static async bulkUpdate(
    model: any,
    ids: (string | number)[],
    updateData: any,
    userId?: number
  ): Promise<{ successful: number; failed: number; errors: any[] }> {
    const results = {
      successful: 0,
      failed: 0,
      errors: [] as any[],
    };

    if (userId) {
      updateData.updated_by = userId;
    }

    try {
      const [affectedRows] = await model.update(updateData, {
        where: {
          id: {
            [Op.in]: ids,
          },
        },
      });

      results.successful = affectedRows;
      results.failed = ids.length - affectedRows;
    } catch (error) {
      console.error("Bulk update failed:", error);
      results.failed = ids.length;
      results.errors.push(
        error instanceof Error ? error.message : "Unknown error"
      );
    }

    return results;
  }

  /**
   * Get paginated results with sorting
   */
  static async getPaginatedResults(
    model: any,
    options: {
      page?: number;
      size?: number;
      where?: any;
      include?: any[];
      order?: any[];
      attributes?: string[];
    } = {}
  ): Promise<{
    items: any[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    const page = options.page || 1;
    const size = Math.min(options.size || 10, 100); // Max 100 items per page
    const offset = (page - 1) * size;

    try {
      const result = await model.findAndCountAll({
        where: options.where || {},
        include: options.include || [],
        order: options.order || [["created_at", "DESC"]],
        attributes: options.attributes,
        limit: size,
        offset,
        distinct: true,
      });

      const totalPages = Math.ceil(result.count / size);

      return {
        items: result.rows,
        totalItems: result.count,
        totalPages,
        currentPage: page,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      };
    } catch (error) {
      console.error("Paginated query failed:", error);
      throw error;
    }
  }

  /**
   * Search records with full-text search
   */
  static async searchRecords(
    model: any,
    searchQuery: string,
    searchFields: string[],
    options: {
      where?: any;
      include?: any[];
      limit?: number;
      order?: any[];
    } = {}
  ): Promise<any[]> {
    try {
      const searchConditions = searchFields.map((field) => ({
        [field]: {
          [Op.like]: `%${searchQuery}%`,
        },
      }));

      const whereClause = {
        [Op.and]: [
          options.where || {},
          {
            [Op.or]: searchConditions,
          },
        ],
      };

      return await model.findAll({
        where: whereClause,
        include: options.include || [],
        limit: options.limit || 50,
        order: options.order || [["created_at", "DESC"]],
      });
    } catch (error) {
      console.error("Search query failed:", error);
      throw error;
    }
  }

  /**
   * Get aggregated data
   */
  static async getAggregatedData(
    model: any,
    aggregations: {
      field: string;
      function: "COUNT" | "SUM" | "AVG" | "MIN" | "MAX";
      alias?: string;
    }[],
    options: {
      where?: any;
      groupBy?: string[];
      having?: any;
    } = {}
  ): Promise<any[]> {
    try {
      const attributes = aggregations.map((agg) => [
        sequelize.fn(agg.function, sequelize.col(agg.field)),
        agg.alias || `${agg.function.toLowerCase()}_${agg.field}`,
      ]);

      return await model.findAll({
        attributes,
        where: options.where || {},
        group: options.groupBy || [],
        having: options.having || {},
        raw: true,
      });
    } catch (error) {
      console.error("Aggregation query failed:", error);
      throw error;
    }
  }

  /**
   * Check database connection health
   */
  static async checkConnectionHealth(): Promise<{
    connected: boolean;
    latency?: number;
    error?: string;
  }> {
    try {
      const startTime = Date.now();
      await sequelize.authenticate();
      const latency = Date.now() - startTime;

      return {
        connected: true,
        latency,
      };
    } catch (error: any) {
      return {
        connected: false,
        error: error.message,
      };
    }
  }

  /**
   * Get database statistics
   */
  static async getDatabaseStats(): Promise<{
    total_tickets: number;
    total_messages: number;
    active_tickets: number;
    resolved_tickets: number;
    avg_resolution_time_hours?: number;
  }> {
    try {
      const stats = await Promise.all([
        db.Ticket.count(),
        db.TicketMessage.count(),
        db.Ticket.count({
          where: { ticket_status: { [Op.notIn]: ["resolved", "closed"] } },
        }),
        db.Ticket.count({ where: { ticket_status: "resolved" } }),
        this.executeRawQuery(`
          SELECT AVG(TIMESTAMPDIFF(HOUR, created_at, resolved_at)) as avg_hours
          FROM mo_support_tickets 
          WHERE resolved_at IS NOT NULL
        `),
      ]);

      return {
        total_tickets: stats[0],
        total_messages: stats[1],
        active_tickets: stats[2],
        resolved_tickets: stats[3],
        avg_resolution_time_hours: stats[4][0]?.avg_hours || 0,
      };
    } catch (error) {
      console.error("Database stats query failed:", error);
      return {
        total_tickets: 0,
        total_messages: 0,
        active_tickets: 0,
        resolved_tickets: 0,
      };
    }
  }
}

/**
 * Customer Service specific database operations
 */
export class CustomerServiceDatabaseHelper extends DatabaseHelper {
  /**
   * Get ticket statistics for organization
   */
  static async getTicketStats(organizationId?: string): Promise<any> {
    try {
      const whereClause = organizationId
        ? { organization_id: organizationId }
        : {};

      const stats = await this.getAggregatedData(
        db.Ticket,
        [
          { field: "id", function: "COUNT", alias: "total_tickets" },
          {
            field: "ticket_priority",
            function: "COUNT",
            alias: "priority_count",
          },
          { field: "ticket_status", function: "COUNT", alias: "status_count" },
        ],
        {
          where: whereClause,
          groupBy: ["ticket_priority", "ticket_status"],
        }
      );

      return stats;
    } catch (error) {
      console.error("Ticket stats query failed:", error);
      return [];
    }
  }

  /**
   * Get escalation statistics
   */
  static async getEscalationStats(organizationId?: string): Promise<any> {
    try {
      const whereClause: any = { auto_escalated: true };
      if (organizationId) {
        whereClause.organization_id = organizationId;
      }

      const stats = await this.getAggregatedData(
        db.Ticket,
        [
          { field: "id", function: "COUNT", alias: "total_escalated" },
          {
            field: "escalation_reason",
            function: "COUNT",
            alias: "reason_count",
          },
        ],
        {
          where: whereClause,
          groupBy: ["escalation_reason"],
        }
      );

      return stats;
    } catch (error) {
      console.error("Escalation stats query failed:", error);
      return [];
    }
  }

  /**
   * Clean up old data
   */
  static async cleanupOldData(retentionDays: number = 365): Promise<{
    deletedTickets: number;
    deletedMessages: number;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // Hard delete old soft-deleted records
      const deletedTickets = await db.Ticket.destroy({
        where: {
          deleted_at: {
            [Op.lte]: cutoffDate,
          },
        },
        force: true,
      });

      const deletedMessages = await db.TicketMessage.destroy({
        where: {
          deleted_at: {
            [Op.lte]: cutoffDate,
          },
        },
        force: true,
      });

      return { deletedTickets, deletedMessages };
    } catch (error) {
      console.error("Data cleanup failed:", error);
      return { deletedTickets: 0, deletedMessages: 0 };
    }
  }
}

export default DatabaseHelper;
