import { celebrate, Joi, Segments } from "celebrate";

// Following recipe-ms pattern with function wrapper
const bulkTicketOperationValidation = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        ticket_ids: Joi.array().items(Joi.number().integer().positive()).min(1).required(),
        operation: Joi.string()
          .valid("assign", "resolve", "close", "delete", "priority_change", "status_change")
          .required(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        new_priority: Joi.string()
          .valid("low", "medium", "high", "urgent")
          .optional(),
        new_status: Joi.string()
          .valid("open", "in_progress", "resolved", "closed", "cancelled")
          .optional(),
        resolution_note: Joi.string().max(1000).optional(),
        bulk_note: Joi.string().max(500).optional(),
      })
      .unknown(true),
  });

const ticketAnalyticsValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        period: Joi.string()
          .valid("today", "week", "month", "quarter", "year", "custom")
          .default("month"),
        group_by: Joi.string()
          .valid("day", "week", "month", "priority", "status", "module", "agent")
          .default("day"),
        ticket_status: Joi.string()
          .valid("open", "in_progress", "resolved", "closed", "cancelled")
          .optional(),
        ticket_priority: Joi.string()
          .valid("low", "medium", "high", "urgent")
          .optional(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        ticket_module: Joi.string().optional(),
        include_metrics: Joi.boolean().default(true),
        include_trends: Joi.boolean().default(true),
      })
      .unknown(true),
  });

const dashboardValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        period: Joi.string()
          .valid("today", "week", "month", "quarter", "year")
          .default("month"),
        include_charts: Joi.boolean().default(true),
        include_recent_tickets: Joi.boolean().default(true),
        recent_limit: Joi.number().integer().min(1).max(50).default(10),
      })
      .unknown(true),
  });

const agentPerformanceValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        agent_id: Joi.number().integer().positive().optional(),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        period: Joi.string()
          .valid("today", "week", "month", "quarter", "year", "custom")
          .default("month"),
        include_ratings: Joi.boolean().default(true),
        include_response_times: Joi.boolean().default(true),
      })
      .unknown(true),
  });

const exportTicketsValidation = () =>
  celebrate({
    [Segments.QUERY]: Joi.object()
      .keys({
        format: Joi.string().valid("csv", "excel", "pdf").default("csv"),
        date_from: Joi.date().optional(),
        date_to: Joi.date().optional(),
        ticket_status: Joi.string()
          .valid("open", "in_progress", "resolved", "closed", "cancelled")
          .optional(),
        ticket_priority: Joi.string()
          .valid("low", "medium", "high", "urgent")
          .optional(),
        assigned_to_user_id: Joi.number().integer().positive().optional(),
        include_messages: Joi.boolean().default(false),
        include_attachments: Joi.boolean().default(false),
      })
      .unknown(true),
  });

export {
  bulkTicketOperationValidation,
  ticketAnalyticsValidation,
  dashboardValidation,
  agentPerformanceValidation,
  exportTicketsValidation,
};

export default {
  bulkTicketOperationValidation,
  ticketAnalyticsValidation,
  dashboardValidation,
  agentPerformanceValidation,
  exportTicketsValidation,
};
